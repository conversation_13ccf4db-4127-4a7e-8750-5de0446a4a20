/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 10:01:45
 * @LastEditTime: 2022-10-10 11:43:22
 * @LastEditors: PhilRandWu
 */
import BaseButton from '@components/base-button';
import BaseCard from '@components/base-card';
import FilterForm from '@components/filter-form';
import PageTitle from '@components/page-title';
import { addOrigin } from '@services/origin';
import { Form, message, Upload, Button } from 'antd';
import { useMutation, useQuery } from 'react-query';
import React, { Component, useEffect, useState } from 'react';
import type { FormInstance } from 'antd/es/form';
import { addConfigs } from './config';
import styles from './index.module.less';
import { ReformChainError } from '@utils/errorCodeReform';
import { useNavigate, useLocation } from 'react-router-dom';
import { signData } from '../../utils/blockChainUtils';
import { useDispatch } from 'react-redux';
import { getLocalPrivatekey } from '@utils/blockChainUtils';
import ImgCropUpload from '@components/img-upload';

import { fileUpload, getFileUrlFormUploadedFile, fileUrlsToUploadFileList } from '@utils';
import { UploadOutlined } from '@ant-design/icons';

const OriginAdd = (props: any) => {
    const { state } = useLocation();

    const [options, setOptions]: any = useState([]);
    const [uploadImageUrl, setUploadImageUrl]: any = useState('');
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [isUpLoading, setIsUpLoading]: any = useState();
    const [fooddata, setfooddata]: any = useState('');
    const [url, setUrl] = useState<any>();
    const addOriginMutation = useMutation(addOrigin, {
        onSuccess(res) {
            message.success('新增产地成功');
            navigate('/product-manage/origin');
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });

    const addBasicInfoConfigs = [
        {
            type: 'Input',
            label: '产地名称',
            value: 'placeName',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,30}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入产地名称!');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            if (value.length > 30) {
                                callback('请保持字符在30字符以内!');
                            } else {
                                callback('请输入产地名称，支持中文、字母或数字!');
                            }
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入产地名称'
        },
        {
            type: 'Input',
            label: '产地地址',
            value: 'placeAddress',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback('请输入产地地址！');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入产地地址'
        },
        {
            type: 'Custom',
            label: '产地位置图',
            value: 'placeAptitude',
            children: (
                <Form.Item noStyle>
                    <div className={styles.prodPic}>
                        <Form.Item
                            name='placeAptitude'
                            rules={[
                                {
                                    required: false,
                                    message: '请上传'
                                }
                            ]}
                        >
                            <ImgCropUpload
                                setIsUpLoading={setIsUpLoading}
                                isUpLoading={isUpLoading}
                                isCrop={false}
                                maxAmount={1}
                                tips={'(图片格式支持:jpg/jpeg/png,最大大小为5M)'}
                            ></ImgCropUpload>
                        </Form.Item>
                    </div>
                </Form.Item>
            )
        }
    ];

    const productVideoAcceptTypes = ['.mp4', '.webm', '.mov', '.m4v'];
    const handleBeforeProductVideoUpload = (file: any) => {
        const fileType = file?.name?.split('.').at(-1).toLowerCase();
        console.log(fileType, file, 'fffffff');
        if (!productVideoAcceptTypes.includes('.' + fileType)) {
            message.error('文件格式不正确');
            return Upload.LIST_IGNORE;
        }
        if (file.size / 1024 / 1024 > 50) {
            message.error('文件过大');
            return Upload.LIST_IGNORE;
        }
        return true;
    };

    const addIntroductionConfigs = [
        {
            type: 'TextArea',
            label: '产地介绍',
            value: 'placeIntro',
            placeholder: '请输入产地介绍',
            maxLength: 200,
            showCount: true
        },
        {
            type: 'Custom',
            label: '产地图片',
            value: 'placeImg',
            children: (
                <Form.Item noStyle>
                    <div className={styles.prodPic}>
                        <Form.Item
                            name='placeImg'
                            rules={[
                                {
                                    required: false,
                                    message: '请上传'
                                }
                            ]}
                        >
                            <ImgCropUpload
                                setIsUpLoading={setIsUpLoading}
                                isUpLoading={isUpLoading}
                                isCrop={false}
                                maxAmount={3}
                                tips={'最多支持上传3张图片，大小不超过5MB'}
                            ></ImgCropUpload>
                        </Form.Item>
                    </div>
                </Form.Item>
            )
        },
        {
            type: 'Custom',
            label: '产地视频',
            value: 'placeVideo',
            placeholder: '大小不超过50MB，支持mp4、webm、mov、m4v格式',
            children: (
                <Form.Item noStyle>
                    <div className={styles.prodPic}>
                        <Form.Item
                            name='placeVideo'
                            rules={[
                                {
                                    required: false,
                                    message: '请上传'
                                }
                            ]}
                            extra={<div style={{ color: '#333' }}>大小不超过50MB，支持mp4、webm、mov、m4v格式</div>}
                            valuePropName='fileList'
                            getValueFromEvent={(e: any) => {
                                if (e.fileList[0]?.response?.status == 200) {
                                    setUrl(e.fileList[0]?.response.fileUrl);
                                }
                                if (e.file?.status == 'removed') {
                                    setUrl(null);
                                }
                                if (Array.isArray(e)) {
                                    return e;
                                }
                                return e && e.fileList;
                            }}
                        >
                            <Upload
                                accept={productVideoAcceptTypes.join(',')}
                                customRequest={({ file, onError, onProgress, onSuccess }) => {
                                    // @ts-ignore
                                    fileUpload({
                                        ...{ file, onError, onProgress, onSuccess },
                                        isUploading: isUpLoading,
                                        setIsUpLoading: setIsUpLoading
                                    });
                                }}
                                beforeUpload={handleBeforeProductVideoUpload}
                                maxCount={1}
                            >
                                <Button icon={<UploadOutlined rev={undefined} />}>上传文件</Button>
                            </Upload>
                        </Form.Item>
                    </div>
                </Form.Item>
            )
        }
    ];

    const onFinish = (values: any) => {
        console.log('values123:', values);
        if (isUpLoading) {
            message.warning('正在上传文件请稍等～');
            return;
        }
        const params: any = {
            productIds: state.productIds,
            placeName: values?.placeName,
            placeAddress: values?.placeAddress,
            placeAptitude: getFileUrlFormUploadedFile(values?.placeAptitude)?.[0],
            placeIntro: values?.placeIntro,
            // TODO 多个
            placeImg: getFileUrlFormUploadedFile(values?.placeImg),
            placeVideo: url
        };
        const paramStr = JSON.stringify(params);
        signData(dispatch, JSON.stringify(params), (error, result: any) => {
            if (!error && result) {
                addOriginMutation.mutate({
                    addPlaceVo: params,
                    paramStr: paramStr,
                    signature: result
                });
            } else if (error !== 'misprivatekey') {
                message.info('签名异常，请重试或联系管理员');
            }
        });
    };
    return (
        <BaseCard title={<PageTitle title='新建产地' />}>
            <PageTitle title='产地基础信息' type='primaryIcon' bmagin={16} />
            <Form
                onFinish={onFinish}
                className='edit-label-title'
                // form={search}
            >
                <FilterForm itemConfig={addBasicInfoConfigs} labelCol={3} wrapperCol={9} />

                <PageTitle title='产地简介' type='primaryIcon' bmagin={16} />

                <FilterForm itemConfig={addIntroductionConfigs} labelCol={3} wrapperCol={9} />
                <div className={styles.addBtnContainer}>
                    <Form.Item className={styles.saveBtn}>
                        <BaseButton type='primary' htmlType='submit' className={styles.submitBtn}>
                            保存
                        </BaseButton>
                    </Form.Item>
                    <Form.Item>
                        <BaseButton
                            htmlType='button'
                            type='dashed'
                            className={styles.primaryBtn}
                            onClick={() => {
                                navigate('/product-manage/origin');
                            }}
                        >
                            取消
                        </BaseButton>
                    </Form.Item>
                </div>
            </Form>
        </BaseCard>
    );
};

export default OriginAdd;

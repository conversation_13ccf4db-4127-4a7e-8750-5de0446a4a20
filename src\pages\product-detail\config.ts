/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-12 10:13:22
 * @LastEditTime: 2022-10-12 11:39:15
 * @LastEditors: PhilRandWu
 */
export const SourceInfoConfig = [
    {
        label: '辣椒',
        name: 'code',
        value: '123',
        type: 'Display',
        span: 12
    },
    {
        label: '盐',
        name: 'package',
        value: '123',
        type: 'Display',
        span: 12
    },
    {
        label: '糖',
        name: 'time',
        value: '123',
        type: 'Display',
        span: 12
    },
    {
        label: '油',
        name: 'time',
        value: '123',
        type: 'Display',
        span: 12
    }
];

export const ProductInfoConfig = [
    {
        label: '食品名称',
        name: 'code',
        value: '123',
        type: 'Display',
        span: 12
    },
    {
        label: '生产日期',
        name: 'package',
        value: '123',
        type: 'Display',
        span: 12
    },
    {
        label: '数量',
        name: 'time',
        value: '123',
        type: 'Display',
        span: 12
    },
    {
        label: '生产批次',
        name: 'time',
        value: '123',
        type: 'Display',
        span: 12
    },
    {
        label: '附件',
        name: 'time',
        value: '123',
        type: 'Url',
        span: 12
    }
];

export const chainInfoConfig = [
    // {
    //     label: '区块号',
    //     name: 'blockNum',
    //     value: '123',
    //     type: 'Display',
    //     span: 24,
    //     tooltip: '信息上链时所在的区块编号',
    //     size: 15
    // },
    {
        label: '链上哈希',
        name: 'transactionId',
        value: '123',
        type: 'Display',
        span: 24,
        tooltip: '信息的链上的哈希值'
    },
    {
        label: '上链时间',
        name: 'transactionTime',
        value: '123',
        type: 'Display',
        span: 24,
        tooltip: '信息上链的时间'
    }
];

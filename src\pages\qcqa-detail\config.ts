/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-12 10:13:22
 * @LastEditTime: 2022-11-01 18:19:51
 * @LastEditors: PhilRandWu
 */
export const QcqaInfoConfig = [
    {
        label: '食品名称',
        name: 'code',
        value: '123',
        type: 'Display',
        span: 12
    },
    {
        label: '生产批次',
        name: 'package',
        value: '123',
        type: 'Display',
        span: 12
    },
    {
        label: '质检时间',
        name: 'time',
        value: '123',
        type: 'Display',
        span: 12
    },
    {
        label: '质检内容',
        name: 'time',
        value: '123',
        type: 'Display',
        span: 12
    },
    {
        label: '质检结果',
        name: 'time',
        value: '123',
        type: 'Status',
        span: 12
    },
    {
        label: '质检报告',
        name: 'time',
        value: '123',
        type: 'Url',
        span: 12
    }
];

export const chainInfoConfig = [
    // {
    //     label: '区块号',
    //     name: 'blockNum',
    //     value: '123',
    //     type: 'Display',
    //     span: 24,
    //     tooltip: '信息上链时所在的区块编号'
    // },
    {
        label: '链上哈希',
        name: 'transactionId',
        value: '123',
        type: 'Display',
        span: 24,
        write: 'ture',
        tooltip: '信息的链上的哈希值'
    },
    {
        label: '上链时间',
        name: 'transactionTime',
        value: '123',
        type: 'Display',
        span: 24,
        tooltip: '信息上链的时间'
    }
];

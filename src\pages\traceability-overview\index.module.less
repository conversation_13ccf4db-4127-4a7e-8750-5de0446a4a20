.displayCard {
    // display: flex
    // height: 1850px;
    // .flexContent(row,space-between,flex-start);
    // margin-top: 24px;
    // overflow: hidden;
}

.overViewContainer {
    background: #fff;
    .overViewContainerTop {
        display: flex;
        justify-content: space-between;
        width: 100%;
        .overViewContainerTopLeft {
            width: 65%;
            margin-right: 3%;
            box-sizing: border-box;
            padding-right: 8px;
            border-right: #fdeec9 1px solid;
            .overTextImgBox {
                display: flex;
            }
            .overTextImg {
                width: 160px;
                text-align: center;
                margin-right: 50px;
                .textTitle {
                    color: #757575;
                    font-size: 16px;
                }
                .overViewContainerTopLeftImg {
                    width: 105px;
                    height: 130px;
                    margin-left: 5px;
                }
            }
        }
        .overViewContainerTopGoods {
            width: 50%;
            box-sizing: border-box;
            .overTextImgBox {
                display: flex;
            }
            .overTextImg {
                width: 160px;
                text-align: center;
                margin-right: 50px;
                .textTitle {
                    color: #757575;
                    font-size: 16px;
                }
                .overViewContainerTopLeftImg {
                    width: 90px;
                    height: 120px;
                    margin-left: 5px;
                }
            }
        }

        //
        .overViewContainerTopRightGoodsPie {
            width: 30%;
            box-sizing: border-box;
        }
        // 收购
        .overViewContainerTopPurchase {
            width: 80%;
        }
        .overViewContainerTopRightPurchase {
            width: 20%;
        }
        // 概览
        .overViewContainerTopOverView {
            width: 80%;
        }
        // 销售
        .overViewContainerTopGoodsMarket {
            width: 65%;
            box-sizing: border-box;
            .overTextImgBox {
                display: flex;
            }
            .overTextImg {
                width: 160px;
                text-align: center;
                margin-right: 50px;
                .textTitle {
                    color: #757575;
                    font-size: 16px;
                }
                .overViewContainerTopLeftImg {
                    width: 90px;
                    height: 120px;
                    margin-left: 5px;
                }
            }
        }
        .overViewContainerTopRightGoodsPieMarket {
            width: 25%;
            box-sizing: border-box;
        }
    }

    .overViewContainerTopRightGoods {
        width: 20%;
        .chainNum {
            // width:150px ;
            width: 100%;
            height: 117px;
            border: 2px solid #ffb200;
            text-align: center;
            box-sizing: border-box;
            padding-top: 30px;
            background: #fdeec9;
            border-radius: 8px;
            .chainNumSubscript {
                font-size: 30px;
                font-weight: 500;
                font-variation-settings: 'opsz' auto;
                font-feature-settings: 'kern' on;
                color: #ffb200;
            }
        }
    }

    .overViewContainerTopRight {
        width: 35%;
        .chainNum {
            // width:150px ;
            width: 100%;
            height: 117px;
            border: 2px solid #ffb200;
            text-align: center;
            box-sizing: border-box;
            padding-top: 30px;
            background: #fdeec9;
            border-radius: 8px;
            .chainNumSubscript {
                font-size: 30px;
                font-weight: 500;
                font-variation-settings: 'opsz' auto;
                font-feature-settings: 'kern' on;
                color: #ffb200;
            }
        }
    }
    .weather {
        display: flex;
        justify-content: space-between;
    }
    .time {
        width: 70%;
        // position: relative;
        // right: 0;
        text-align: right;
    }
}
.mai {
    position: absolute;
    top: -25px;
    right: -15px;
    width: 80px;
    z-index: 999;
}
.bottomHalf {
    min-width: 0 !important;
    width: 50%;
}
.overViewContent {
    padding-bottom: 56px;
    height: 400px;
    margin-bottom: 32px;
}

.Column {
    // width: 1193px;
    position: relative;
    min-width: 100%;
    height: 100%;
    flex: 1 1;
    .flexContent(column,space-between,flex-start);
    & .ant-card {
        height: 100% !important;
    }
}
.title {
    overflow-y: scroll;
}

.cardTips {
    // display: flex;
    width: 300px;
    margin-left: 31px;
    flex: 1 1;
    // .flexContent(column,space-around,flex-start);
    // height: 1250px;
    // flex: none;
}

.columnTitle {
    color: #80a932;
    font-size: 18px;
    margin: 0 0px 11px 0px;
    box-sizing: border-box;
    width: 220px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    background: url('../../assets/home/<USER>') no-repeat;
    background-size: 100% 100%;
}
.columnTitleLeft {
    display: flex;
    color: #80a932;
    font-size: 18px;
    margin: 0 0px 11px 0px;
    box-sizing: border-box;
    width: 180px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    background: url('../../assets/home/<USER>') no-repeat;
    background-size: 100% 100%;
}
// 销售标题

.columnTitleMaker {
    color: #80a932;
    font-size: 18px;
    margin: 0 0px 11px 0px;
    box-sizing: border-box;
    width: 160px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    background: url('../../assets/home/<USER>') no-repeat;
    background-size: 100% 100%;
}
.columnTitleIcon {
    color: #347934;
    font-size: 18px;
    // margin: 15px 0px 11px 0px;
    box-sizing: border-box;
    padding-left: 40px;
    width: 180px;
    background: url('../../assets/landicon/1.png') no-repeat;
    background-size: 100% 100%;
}
.downCard1 {
    height: 500px;
}
.downCard {
    margin-top: 28px;
    height: 500px;
    background: #fff;
    box-sizing: border-box;
    padding: 20px;
}

.TableContainer {
    // min-height: 466px;
    & .ant-table-content {
        height: 400px;
    }
}
.pubilc {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}
.pubilcDiv {
    // width:150px ;
    width: 110px;
    height: 109px;
    border: 2px solid #ffb200;
    text-align: center;
    box-sizing: border-box;
    padding-top: 20px;
    background: #fdeec9;
    border-radius: 8px;
}
.textTitle {
    font-size: 16px;
    font-weight: 500;
    font-variation-settings: 'opsz' auto;
    font-feature-settings: 'kern' on;
    color: #4d4d4d;
    margin-bottom: 15px;
}

.subscript {
    font-size: 24px;
    font-weight: 500;
    font-variation-settings: 'opsz' auto;
    font-feature-settings: 'kern' on;
    color: #ffb200;
}

.downCard3 {
    margin-top: 28px;
    height: 280px;
    background: #fff;
    box-sizing: border-box;
    padding: 20px;
}

.downCard4 {
    margin-top: 28px;
    height: 480px;
    background: #fff;
    box-sizing: border-box;
    padding: 20px;
}
// .subscript::after {
//   content: '度';
//   position: absolute;
//   bottom: -0.5em; /* 控制下标的位置 */
//   right: 0.2em; /* 控制下标的位置 */
//   font-size:1em; /* 控制下标的大小 */
//   color: #FFB200;
// }
.product {
    display: flex;
    justify-content: space-between;
    .product_left {
        width: 60%;
    }
    .product_right {
        width: 35%;
    }
    .overViewContainerTopRow {
        display: flex;
        justify-content: space-between; /* 均匀分布空间 */
        // padding: 0 10%;
        box-sizing: border-box;
    }
    .overViewContainerTopRight {
        margin-bottom: 15px;
        width: 45%;
        .chainNum {
            // width:150px ;
            width: 100%;
            height: 117px;
            border: 2px solid #ffb200;
            text-align: center;
            box-sizing: border-box;
            padding-top: 30px;
            background: #fdeec9;
            border-radius: 8px;
            .chainNumSubscript {
                font-size: 30px;
                font-weight: 500;
                font-variation-settings: 'opsz' auto;
                font-feature-settings: 'kern' on;
                color: #ffb200;
            }
        }
    }
}
.MakerTop {
    display: flex;
    width: 100%;
    margin-bottom: 15px;
    .MakerTopLeft {
        width: 50%;
        display: flex;
        justify-content: space-between;
        box-sizing: border-box;
        padding: 0 60px 0 0;
        border-right: #ffb200 1px solid;
    }
    .MakerTopRight {
        width: 50%;
        display: flex;
        justify-content: space-between;
        padding: 0 0 0 60px;
    }
}

.chainNumMaker {
    // width:150px ;
    // width: 33%;
    height: 80px;
    border: 2px solid #ffb200;
    text-align: center;
    box-sizing: border-box;
    padding-top: 15px;
    background: #fdeec9;
    border-radius: 8px;
    .chainNumSubscript {
        font-size: 30px;
        font-weight: 500;
        font-variation-settings: 'opsz' auto;
        font-feature-settings: 'kern' on;
        color: #ffb200;
    }
}
.Details {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 14px;
    cursor: pointer;
    color: #80a932;
    z-index: 1000;
}
.down {
    width: 100%;
    height: 40px;
    line-height: 40px;
    text-align: center;
}
.silderCard {
    display: grid;
    grid-template-columns: repeat(4, 25%);
    box-sizing: border-box;
}
.increaseIcon {
    color: #f5222d;
    width: 15px;
    height: 15px;
    margin-left: 10px;
    margin-top: 5px;
}


import request from "@services/request";
import { message } from "antd";
import JSEncrypt from "jsencrypt";

async function rsaEncrypt(str: string) {
  try {
    const encryptor = new JSEncrypt();
    const pKRet = await request({
      method: 'get',
      url: '/sys-config/getPublicKey',
      data: {}
    });
    encryptor.setPublicKey(pKRet?.data || '');
    const rsaPassWord = encryptor.encrypt(str);
    return rsaPassWord || '';
  } catch (err) {
    message.destroy();
    message.error('请求加密失败');
    console.log(err);
    return Promise.reject();
  }
}

export default rsaEncrypt;

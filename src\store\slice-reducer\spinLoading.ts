import { createSlice } from "@reduxjs/toolkit";

interface IUserState {
    showLoading?: boolean
}

const initialState: IUserState = {
    showLoading: false
};

const spin: any = createSlice({
    name: "spin",
    initialState,
    reducers: {
        showLoading: (store: IUserState,action) => {
            console.log("showLoading",action)
            store.showLoading = action.payload;
        },
    },
});

export const { showLoading} = spin.actions;
export default spin.reducer;

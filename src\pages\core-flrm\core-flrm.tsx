import { ExclamationCircleFilled, PlusOutlined, SearchOutlined, SyncOutlined } from '@ant-design/icons';
import BaseButton from '@components/base-button';
import BaseCard from '@components/base-card';
import BaseModal from '@components/base-modal';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import FilterForm from '@components/filter-form';
import PageTitle from '@components/page-title';
import {
    addCore,
    candidateList,
    corePage,
    modifyUserState,
    setBasicInfo,
    getOrgMenuIdList,
    getUserMenuList
} from '@services/company';
import { menuListGurable } from '@services/menu';
import { useAppSelector } from '@store';
import { decryptStr, isArrayArr, renderRole } from '@utils';
import { ReformChainError } from '@utils/errorCodeReform';
import { Badge, Checkbox, Form, Input, Modal, Select, Space, Switch, message } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useRef, useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import { searchConfig } from './config';

import styles from './index.module.less';

const { confirm } = Modal;
const CoreFlrm = (props: any) => {
    const { Option } = Select;
    const dispatch = useDispatch();
    const querycore: any = useRef(null);
    const navigate = useNavigate();
    const [reset] = Form.useForm();
    const { pageInfo, handlePaginationChange } = props;
    const [addModalVisible, setAddModelVisible] = useState(false);
    const [addSelectTypeModalVisible, setAddSelectTypeModalVisible] = useState(false);
    const [editNameVisible, setEditNameVisible] = useState(false);
    const [editId, setEditId] = useState();
    const [addCoreSelectTypeForm] = Form.useForm();
    const [editCoreSelectForm] = Form.useForm();
    const [addCoreForm] = Form.useForm();
    const [iscoreEnterprise, setIscoreEnterprise] = useState(false);
    const [userId1, setUserId1] = useState(0);
    // 农户
    const [peasant, setPeasant] = useState(false);
    const [userName, setUserName] = useState(false);
    const [tableData, setTableData] = useState<any>();
    const [nameArr, setNameArr] = useState<any>();
    const [phoneNumberArr, setPhoneNumberArr] = useState<any>();
    const [identityId, setIdentityId] = useState(0);
    // 是否显示用户反馈开关
    const [showFeedbackSwitch, setShowFeedbackSwitch] = useState(false);
    // 当前选择的企业类型
    const [selectedEnterpriseType, setSelectedEnterpriseType] = useState(false);

    // 取用户信息
    const userInfo = useAppSelector((store) => store.user);
    // const [editCormForm] = Form.useForm();
    const getOrgMenuId = useQuery(
        ['getOrgMenuId', userId1], // 查询键，可以是数组或字符串
        () => {
            // 请求函数
            return getOrgMenuIdList({
                orgId: editId
            });
        },
        {
            // 查询选项
            onSuccess: (data) => {
                console.log(data);
                // 请求成功时的处理逻辑
                // 你的处理逻辑
                editCoreSelectForm.setFieldsValue({
                    limits: data?.data
                });
            },
            onError: (err: any) => {
                // 请求失败时的处理逻辑
                ReformChainError(err);
            },
            enabled: Boolean(editId) // 控制查询是否执行
        }
    );
    //请求列表
    const corepage = useQuery(
        ['corepage', pageInfo],
        () => {
            // if (!userInfo.user?.organization_id) {
            //     message.error('未获取到机构id');
            //     return Promise.reject('未获取到机构id');
            // }
            return corePage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                shortName: querycore?.current?.name?.trim() || undefined,
                state: querycore?.current?.data,
                identity: querycore?.current?.identity
            });
        },
        {
            async onSuccess(res) {
                //列表数据
                console.log('res?.data----解密', res?.data);
                const phoneNumberArr: any = await Promise.all(
                    isArrayArr(res?.data?.records)?.map((item: any, index: number) => {
                        return decryptStr(item.phoneNumber);
                    })
                );
                const nameArr = await Promise.all(
                    isArrayArr(res?.data?.records)?.map((item: any, index: number) => {
                        return decryptStr(item.userName);
                    })
                );
                const tableData = await Promise.all(
                    isArrayArr(res?.data?.records)?.map((item: any, index: number) => {
                        const value = {
                            id: item?.id,
                            businessType: renderRole(item?.identity),
                            corename: item.shortName,
                            manager: index,
                            manageraccount: item.adminId,
                            phone: index,
                            status: item.state,
                            identity: item.identity,
                            companyId: item.id,
                            userFeedback: item.userFeedback
                        };
                        return value;
                    })
                );
                console.log('tableData----', tableData);
                setTableData(tableData);
                console.log('nameArr', nameArr, phoneNumberArr);
                setNameArr(nameArr);
                setPhoneNumberArr(phoneNumberArr);
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    //候选人列表
    const candidatae = useQuery(
        ['candidatae', addModalVisible],
        () => {
            return candidateList();
        },
        {
            onError(err: any) {
                ReformChainError(err);
            },
            enabled: addModalVisible
        }
    );

    //权限查询表
    // const menulist = useQuery(
    //     ['candidates', identityId], // query key - 包含 identityId 作为依赖
    //     () =>
    //         menuListGurable({
    //             identity: identityId
    //         }), // query function
    //     {
    //         onError: (err: any) => {
    //             ReformChainError(err);
    //         },
    //         onSuccess: (data) => {
    //             // 成功回调函数

    //             // addCoreForm.setFieldsValue({
    //             //     limits: [
    //             //         102, 103, 123, 100, 103, 102, 107, 106, 110, 111, 115, 117, 118, 121, 120, 123, 115, 130, 143
    //             //     ]
    //             // });
    //             console.log('Data fetched successfully:', data);
    //             // 在这里执行成功后的操作
    //         }
    //     }
    // );
    const menulist = useMutation(menuListGurable, {
        onSuccess(res) {
            // message.success('修改状态成功');
            // corepage.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });

    console.log('cabdidatae', candidatae, [
        candidatae?.data?.data?.map((item: any, index: any) => {
            // console.log(7777777,item)
            const manager = {
                value: item.id,
                label: item.user_name
            };
            return manager;
        })
    ]);
    // 更新企业
    const updateCore = useMutation(setBasicInfo, {
        onSuccess(res) {
            message.success('编辑企业成功');
            setEditNameVisible(false);
            corepage.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            corepage.refetch();
        }
    });

    //改变状态
    const coremodify = useMutation(modifyUserState, {
        onSuccess(res) {
            message.success('修改状态成功');
            corepage.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            corepage.refetch();
        }
    });
    const corepa = useMutation(addCore, {
        onSuccess(res) {
            corepage.refetch();
            setAddModelVisible(false);
            addCoreForm.resetFields();
            setIscoreEnterprise(false);
            addCoreSelectTypeForm.resetFields();
            message.success('添加企业成功');
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    const listColumn: ColumnsType<any> = [
        {
            title: '企业ID',
            dataIndex: 'id',
            key: 'id',
            ellipsis: true
        },
        {
            title: '企业名称',
            dataIndex: 'corename',
            key: 'corename',
            ellipsis: true
        },
        {
            title: '企业类型',
            dataIndex: 'businessType',
            key: 'businessType',
            ellipsis: true
        },
        {
            title: '管理员',
            dataIndex: 'manager',
            key: 'operation',
            ellipsis: true,
            render: (data: any) => <span>{nameArr[data]}</span>
        },
        {
            title: '管理员账号',
            dataIndex: 'manageraccount',
            key: 'name',
            ellipsis: true
        },
        {
            title: '联系方式',
            dataIndex: 'phone',
            key: 'phone',
            ellipsis: true,
            render: (data: any) => <span>{phoneNumberArr[data]}</span>
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            ellipsis: true,
            render: (data: any) => (
                <span style={{ color: data ? '#F64041' : '#666666' }}>
                    <Badge
                        status={data ? 'error' : 'success'}
                        color={data ? '#F64041' : 'rgb(36, 171, 59)'}
                        text={data ? '禁用' : '可用'}
                    />
                </span>
            )
        },
        {
            width: 300,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle'>
                    {/* <span onClick={() => {
                        setEditModelVisible(true);
                    }}>编辑</span> */}
                    <BaseButton
                        type='dashed'
                        className={record.status ? 'primaryBtn' : 'warnBtn'}
                        // onClick={() => {
                        // console.log(77777777, record);
                        // coremodify.mutate({
                        //     companyId: record?.companyId,
                        //     state: !record?.status ? 1 : 0
                        // });
                        // }}
                        // disabled={record?.identity == 0 ? true : false}
                        disabled={record?.identity === 0 ? true : false}
                        onClick={
                            record.status
                                ? () => {
                                      coremodify.mutate({
                                          opt: !record?.status ? 'DISABLE' : 'ENABLE',
                                          id: record?.companyId
                                      });
                                  }
                                : () => {
                                      showConfirm(record?.companyId);
                                  }
                        }
                    >
                        {record.status ? '启用' : '禁用'}
                    </BaseButton>
                    <BaseButton
                        type='text'
                        className='editBtn'
                        onClick={() => {
                            editCoreSelectForm.setFieldsValue({
                                shortName: record?.corename,
                                userFeedback: record?.userFeedback === 1
                            });
                            setEditId(record?.id);
                            setEditNameVisible(true);
                            const newIdentityId = record?.identity;
                            setIdentityId(newIdentityId);
                            setUserId1(userId1 + 1);
                            // 使用新的 identityId 值手动触发查询
                            menulist.mutate({
                                identity: newIdentityId,
                                orgId: record?.id
                            });
                        }}
                    >
                        编辑
                    </BaseButton>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            navigate(`/core-flrm/detail/${record?.id}`);
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    const addCormFIrmSelectType = {
        okText: '确定',
        title: '添加企业',
        visible: addSelectTypeModalVisible,
        setVisible: setAddSelectTypeModalVisible,
        okHandle: async () => {
            try {
                const data: any = await addCoreSelectTypeForm.validateFields();
                if (data?.addCoreSelectType === 7) {
                    setPeasant(true);
                } else {
                    setPeasant(false);
                }
                if (data?.addCoreSelectType === 1) {
                    //溯源环节之前由后端控制，现在前端注释掉
                    // setIscoreEnterprise(true);
                    setShowFeedbackSwitch(true);
                } else {
                    setShowFeedbackSwitch(false);
                }
                if (data?.addCoreSelectType === 1 || data?.addCoreSelectType === 8) {
                    menulist.mutate({
                        identity: data?.addCoreSelectType
                    });
                    setSelectedEnterpriseType(true);
                } else {
                    setSelectedEnterpriseType(false);
                }

                setAddSelectTypeModalVisible(false);
                setAddModelVisible(true);
            } catch (e) {}
        },
        onCancelHandle: () => {
            setAddSelectTypeModalVisible(false);
            setSelectedEnterpriseType(false);
            addCoreSelectTypeForm.resetFields();
        }
    };

    const addCormFIrm = {
        okText: '确定',
        title: '添加企业',
        visible: addModalVisible,
        setVisible: setAddModelVisible,
        okHandle: async () => {
            try {
                const data = await addCoreForm.validateFields();

                const params: any = {
                    shortName: peasant ? userName : data.name,
                    adminId: data.account,
                    identity: addCoreSelectTypeForm.getFieldValue('addCoreSelectType'),
                    // processIds: data?.traceabilityType
                    processIds: [2]
                };

                // 如果是生产加工企业，添加用户反馈参数和权限参数
                if (showFeedbackSwitch) {
                    params.userFeedback = data.userFeedback ? 1 : 0;
                }

                // 添加权限字段 - 当身份为平台方(1)或销售企业(8)时
                const identityId = addCoreSelectTypeForm.getFieldValue('addCoreSelectType');
                if (identityId == 1 || identityId === 8) {
                    params.limits = data.limits || [];
                }

                // const paramStr = JSON.stringify(params);
                // signData(dispatch, JSON.stringify(params), (error, result: any) => {
                //     if (!error && result) {
                //         corepa.mutate({
                //             insertOrgVo: params,
                //             paramStr: paramStr,
                //             signature: result
                //         });
                //     } else if (error !== 'misprivatekey') {
                //         message.info('签名异常，请重试或联系管理员');
                //     }
                // });
                console.log('提交参数:', params); // 添加日志查看参数
                corepa.mutate(params);
            } catch {
                console.log('报错了');
            }
        },
        onCancelHandle: () => {
            setAddModelVisible(false);
            setIscoreEnterprise(false);
            setShowFeedbackSwitch(false);
            setSelectedEnterpriseType(false);
            addCoreSelectTypeForm.resetFields();
            addCoreForm.resetFields();
            corepage.refetch();
        }
    };

    //查询
    const findcore = (values: any) => {
        handlePaginationChange(1);
        // console.log("values",values)
        querycore.current = values;
        console.log('queryuser', querycore);
        corepage.refetch();
    };

    const findUserNameById = (id: any) => {
        // 这里简化处理，实际应用中你需要根据id从源数据中查找
        const selectedUser = (candidatae?.data?.data || []).find((user: { id: any }) => user.id === id);
        return selectedUser ? selectedUser.userName : '';
    };

    const handleSelectChange = (value: any) => {
        // 使用id查找对应的userName
        const userName = findUserNameById(value);
        setUserName(userName);
        console.log('Selected userName:', userName);
        // 这里可以进一步处理userName，比如更新状态等
    };

    //禁用
    const showConfirm = (id: any) => {
        confirm({
            wrapClassName: 'forbid',
            title: '确定要禁用该企业吗？',
            okText: '停用',
            cancelText: '取消',
            icon: <ExclamationCircleFilled rev={undefined} />,
            content: `禁用后该企业以及该企业参与方所有用户无法使用${sessionStorage.systemTitle}`,
            onOk() {
                coremodify.mutate({
                    opt: 'DISABLE',
                    id: id
                });
            },
            onCancel() {}
        });
    };
    const CoreSelectType =
        userInfo?.userInfo?.identity === 0 || userInfo?.userInfo?.identity === 6
            ? [
                  // { value: 1, label: '生产加工企业' },
                  { value: 1, label: '生产加工企业' },
                  // { value: 2, label: '供应商' },
                  // { value: 3, label: '质检机构' },
                  // { value: 4, label: '监管机构' },
                  // { value: 5, label: '物流企业' }
                  { value: 6, label: '运营方' },
                  { value: 7, label: '农户' },
                  { value: 8, label: '销售企业' }
              ]
            : [{ value: 1, label: '生产加工企业' }];
    searchConfig[1].fields = CoreSelectType;

    return (
        <>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                title={<PageTitle title='企业列表' bg='container qi' />}
            >
                <div
                    // className="searchContainer"
                    className={`${styles.searchContainer} partiesManageSearchContainer`}
                >
                    <Form
                        style={{
                            width: '100%',
                            display: 'flex',
                            justifyContent: 'space-between',
                            marginBottom: '21px'
                        }}
                        onFinish={findcore}
                        layout='inline'
                        labelAlign='left'
                        form={reset}
                        className='label-title'
                    >
                        <FilterForm itemConfig={searchConfig} labelCol={{ span: 6 }} />
                        <Space>
                            <BaseButton
                                type='primary'
                                htmlType='submit'
                                style={{ width: 100 }}
                                // className='searchBtn'
                                className={`${styles.searchBtn} ${styles.baseBtn}`}
                                icon={<SearchOutlined rev={undefined} />}
                            >
                                查询
                            </BaseButton>
                            <BaseButton
                                type='dashed'
                                className='primaryBtn'
                                style={{ width: 100 }}
                                icon={<SyncOutlined rev={undefined} />}
                                onClick={() => {
                                    querycore.current = null;
                                    corepage.refetch();
                                    reset.resetFields();
                                }}
                            >
                                重置
                            </BaseButton>
                            <BaseButton
                                type='dashed'
                                icon={<PlusOutlined rev={undefined} />}
                                className='bgBtn'
                                onClick={() => {
                                    setAddSelectTypeModalVisible(true);
                                }}
                            >
                                添加企业
                            </BaseButton>
                        </Space>
                    </Form>
                </div>
                <BaseTable
                    rowKey='account'
                    // btnDisplay={(checkData: any, resetSelect: any) => {
                    //     return (
                    //         <TableHead
                    //             LeftDom={
                    //             }
                    //         />
                    //     );
                    // }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={corepage?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={corepage?.data?.data?.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>

            <BaseModal destroyOnClose {...addCormFIrm}>
                <Form labelCol={{ span: 5 }} name='addCoreForm' form={addCoreForm} className='edit-label-title'>
                    {peasant ? null : (
                        <Form.Item
                            label='企业名称'
                            name='name'
                            rules={[
                                { required: true, message: '' },
                                () => ({
                                    validator: (_: any, value: any, callback: any) => {
                                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,40}$/);
                                        const verify = regExp.test(value);
                                        if (!value) {
                                            callback('请输入企业名称');
                                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                                            callback('字段前后不能输入空格！');
                                        } else if (verify === false) {
                                            if (value.length > 40) {
                                                callback('请保持字符在40字符以内!');
                                            } else {
                                                callback('请输入企业名称，支持中文、字母或数字!');
                                            }
                                        } else {
                                            callback();
                                        }
                                    }
                                })
                            ]}
                        >
                            <Input placeholder='请输入企业名称' />
                        </Form.Item>
                    )}
                    {peasant ? (
                        <Form.Item
                            label='农户名称'
                            name='account'
                            rules={[{ required: true, message: '请选择农户名称' }]}
                        >
                            <Select placeholder='请选择农户名称' onChange={(value) => handleSelectChange(value)}>
                                {(candidatae?.data?.data || [])?.map((item: any) => (
                                    <Select.Option value={item?.id}>{item?.userName}</Select.Option>
                                ))}
                            </Select>
                        </Form.Item>
                    ) : (
                        <Form.Item label='管理员' name='account' rules={[{ required: true, message: '请选择管理员' }]}>
                            <Select placeholder='请选择管理员'>
                                {(candidatae?.data?.data || [])?.map((item: any) => (
                                    <Select.Option value={item?.id}>{item?.userName}</Select.Option>
                                ))}
                            </Select>
                        </Form.Item>
                    )}
                    {/* <Form.Item label='管理员' name='account' rules={[{ required: true, message: '请选择你的管理员' }]}>
                        <Select placeholder='请选择管理员'>
                            {(candidatae?.data?.data || [])?.map((item: any) => (
                                <Select.Option value={item?.id}>{item?.userName}</Select.Option>
                            ))}
                        </Select>
                    </Form.Item> */}
                    {iscoreEnterprise ? (
                        <Form.Item
                            label='溯源环节'
                            name='traceabilityType'
                            rules={[{ required: false, message: '请选择溯源环节' }]}
                        >
                            <Checkbox.Group>
                                <Checkbox value={1}>原料溯源</Checkbox>
                                <Checkbox value={2}>质检溯源</Checkbox>
                                <Checkbox value={3}>物流溯源</Checkbox>
                            </Checkbox.Group>
                        </Form.Item>
                    ) : null}
                    {/* 权限选择 - 仅在选择生产加工企业时显示 */}
                    {selectedEnterpriseType ? (
                        <Form.Item label='权限' name='limits'>
                            <Select
                                mode='multiple'
                                placeholder='请选择'
                                options={menulist?.data?.data
                                    ?.filter((val: any) => val?.menuName.indexOf('参与方') < 0)
                                    .map((item: any) => {
                                        return {
                                            label: item?.menuName,
                                            options: item?.childrenMenu
                                                ?.filter((val: any) => val?.menuName.indexOf('原料') < 0)
                                                .map((item: any) => {
                                                    return {
                                                        label: item?.menuName,
                                                        value: item?.id,
                                                        disabled: item.configurable == 1 // 添加这一行
                                                    };
                                                })
                                        };
                                    })}
                            ></Select>
                        </Form.Item>
                    ) : null}
                    {/* 用户反馈开关 */}
                    {/* {showFeedbackSwitch ? (
                        <Form.Item label='用户反馈展示' name='userFeedback' valuePropName='checked' initialValue={true}>
                            <Switch />
                        </Form.Item>
                    ) : null} */}
                </Form>
            </BaseModal>

            <BaseModal {...addCormFIrmSelectType}>
                <Form name='addCoreSelectTypeForm' form={addCoreSelectTypeForm} className='edit-label-title'>
                    <Form.Item
                        name='addCoreSelectType'
                        label='企业类型'
                        rules={[
                            {
                                required: true,
                                message: '请选择企业类型'
                            }
                        ]}
                    >
                        <Select placeholder='请选择企业类型'>
                            {CoreSelectType.map((item: any, index: any) => (
                                <Select.Option value={item?.value}>{item?.label}</Select.Option>
                            ))}
                        </Select>
                    </Form.Item>
                </Form>
            </BaseModal>
            <BaseModal
                okText='确定'
                title='编辑'
                visible={editNameVisible}
                setVisible={setEditNameVisible}
                okHandle={async () => {
                    try {
                        const values = await editCoreSelectForm.validateFields();
                        updateCore.mutate({
                            orgId: editId,
                            shortName: values.shortName,
                            userFeedback: values.userFeedback ? 1 : 0,
                            limits: values.limits
                        });
                    } catch {}
                }}
                onCancelHandle={() => {
                    setEditNameVisible(false);
                }}
            >
                <Form
                    name='editCoreSelectForm'
                    form={editCoreSelectForm}
                    className='edit-label-title'
                    labelAlign='right'
                    labelCol={{ span: 6 }}
                >
                    <Form.Item
                        name='shortName'
                        label='企业名称'
                        rules={[
                            {
                                required: true,
                                message: '请输入企业名称'
                            },
                            {
                                max: 40,
                                message: '请保持字符在40字符以内!'
                            }
                        ]}
                    >
                        <Input placeholder='请输入企业名称' />
                    </Form.Item>
                    {identityId == 1 || identityId === 8 ? (
                        <Form.Item label='权限' name='limits'>
                            <Select
                                mode='multiple'
                                placeholder='请选择'
                                options={menulist?.data?.data
                                    ?.filter((val: any) => val?.menuName.indexOf('参与方') < 0)
                                    .map((item: any) => {
                                        return {
                                            label: item?.menuName,
                                            options: item?.childrenMenu
                                                ?.filter((val: any) => val?.menuName.indexOf('原料') < 0)
                                                .map((item: any) => {
                                                    return {
                                                        label: item?.menuName,
                                                        value: item?.id,
                                                        disabled: item.configurable == 1 // 添加这一行
                                                    };
                                                })
                                        };
                                    })}
                            ></Select>
                        </Form.Item>
                    ) : (
                        ''
                    )}
                    {/* {tableData?.find((item: any) => item.id === editId)?.identity === 1 && (
                        <Form.Item label='用户反馈展示' name='userFeedback' valuePropName='checked' initialValue={true}>
                            <Switch />
                        </Form.Item>
                    )} */}
                </Form>
            </BaseModal>

            {/* <BaseModal {...editCormFIrm}>
            <Form
                name="editCormForm"
                form={editCormForm}>
                {
                    <FilterForm itemConfig={editCoreFirm} />
                }
            </Form>
        </BaseModal>
 */}
        </>
    );
};

export default WithPaginate(CoreFlrm);

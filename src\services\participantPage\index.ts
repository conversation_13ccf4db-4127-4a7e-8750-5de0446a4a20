/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-11-01 18:29:01
 * @LastEditTime: 2022-11-01 18:29:01
 * @LastEditors: PhilRandWu
 */
import request from '../request';
//参与方分页
export const participantPage = (obj: any) => {
    return request({
        url: '/org/getPartyList',
        method: 'post',
        data: obj
    });
};
//改变参与方状态
export const modifyUserState = (obj: any) => {
    return request({
        url: `/org/updateParticipantState`,
        method: 'post',
        data: obj
    });
};
//根据名称查找参与方
export const checkPartcipantByName = (obj: any) => {
    return request({
        url: `/org/getParticipantInfo`,
        method: 'get',
        params: obj
    });
};
//生产加工企业关联参与方
export const associateParticipant = (obj: any) => {
    return request({
        url: `/org/associate`,
        method: 'post',
        data: obj
    });
};
//新增参与方
export const addParticipant = (obj: any) => {
    return request({
        url: '/company/addParticipant',
        method: 'post',
        data: obj
    });
};
//编辑参与方企业信息
export const updateParticipant = (obj: any) => {
    return request({
        url: '/company/updateParticipant',
        method: 'post',
        data: obj
    });
};
//编辑参与方企业信息
export const addAdmin = (obj: any) => {
    return request({
        url: '/company/addAdmin',
        method: 'post',
        data: obj
    });
};
//编辑管理员信息(用户信息)
export const updateUser = (obj: any) => {
    return request({
        url: '/user/updateUser',
        method: 'post',
        data: obj
    });
};
//参与方详情
export const checkParticipantDetail = (obj: any) => {
    return request({
        url: `/org/getPartyDetail`,
        method: 'get',
        params: obj
    });
};

// 参与方查询生产加工企业列表
export const coreCompanyList = () => {
    return request({
        url: `/user/cores`,
        method: 'get',
        params: {}
    });
};

// 参与方切换生产加工企业
export const switchCoreCompany = (obj: any) => {
    return request({
        url: `/user/switchCore`,
        method: 'post',
        data: obj
    });
};
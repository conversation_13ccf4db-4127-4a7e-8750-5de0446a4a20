import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space, Modal, Input, Descriptions, DescriptionsProps } from 'antd';

import {
    participantPage,
    modifyUserState,
    addParticipant,
    checkPartcipantByName,
    associateParticipant
} from '@services/participantPage';
import useUrlState from '@ahooksjs/use-url-state';
import { ReformChainError } from '@utils/errorCodeReform';

import styles from './index.module.less';
import { addPartiesConfigs, editEmployeesConfigs, searchConfig } from './config';
import SearchForm from '@components/search-form';
import { useRef, useState, useEffect } from 'react';
import { useMutation, useQuery } from 'react-query';
import BaseModal from '@components/base-modal';
import FilterForm from '@components/filter-form';
import PageTitle from '@components/page-title';
import { SearchOutlined, PlusOutlined,SyncOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { useAccountList } from '../../myhooks/useaccountlist';
import { useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import { ColumnsType } from 'antd/lib/table';

interface IUrlState {
    pageIndex: number;
    pageSize: number;
}

const { confirm } = Modal;
const PartiesManager = (props: any) => {
    const [pageindex, setpageindex]: any = useState(null);
    const { pageInfo, handlePaginationChange } = props;
    const navigate = useNavigate();
    const [addPartiesForm] = Form.useForm();
    const [queryPartiesForm] = Form.useForm();

    // // const [addModalVisible, setAddModelVisible] = useState(false);
    const [queryModalVisible, setQueryModelVisible] = useState(false);
    const [correlationModalVisible, setCorrelationModalVisible] = useState(false);

    const [search]: any = Form.useForm();
    const queryuser: any = useRef(null);
    const participantRef: any = useRef(null);
    // const queryList: any = useAccountList({
    //     pageIndex: pageInfo.pageIndex,
    //     pageSize: pageInfo.pageSize
    // });
    useEffect(() => {
        partquery.refetch();
    }, [pageInfo]);
    const addparticipant = useMutation(addParticipant, {
        onSuccess(res) {
            // setAddModelVisible(false);
            // 关联成功后调用
            setCorrelationModalVisible(false);
            message.success('添加成功');
            partquery.refetch();
            addPartiesForm.resetFields();
        },
        onError(err: any) {
            ReformChainError(err);
            partquery.refetch();
        }
    });

    const partmodiy = useMutation(modifyUserState, {
        onSuccess(res) {
            message.success('修改状态成功');
            partquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            partquery.refetch();
        }
    });
    // 参与方列表
    const partquery = useQuery(
        ['partquery', pageInfo],
        () => {
            return participantPage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                shortName: queryuser?.current?.name?.trim() || undefined,
                identity: queryuser?.current?.type,
                state: Number(queryuser?.current?.status)
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            },
            retry: false
        }
    );
    // 根据名称查询参与方
    const partNamequery = useMutation(checkPartcipantByName, {
        onSuccess(res) {
            participantRef.current = res?.data;
            setCorrelationModalVisible(true);
            queryPartiesForm.resetFields();
            setQueryModelVisible(false);
        },
        onError(err: any) {
            ReformChainError(err);
            queryPartiesForm.resetFields();
            setQueryModelVisible(false);
        }
    });
    // 关联参与方
    const associateParticipantMutation = useMutation(associateParticipant, {
        onSuccess(res) {
            message.success('关联参与方成功');
            setCorrelationModalVisible(false);
            partquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            setCorrelationModalVisible(false);
            partquery.refetch();
        }
    });
    // console.log("userquery",userquery)
    const tableData = partquery?.data?.data?.records?.map((item: any) => ({
        id: item?.id,
        name: item.shortName,
        type: item.identity || '-',
        manager: item.userName,
        managerAccount: item.account,
        phone: item.telephone,
        status: item.state,
        companyId: item.companyId,
        remark: item.remark
    }));

    const listColumn: ColumnsType<any> = [
        {
            title: '企业ID',
            dataIndex: 'id',
            key: 'id'
        },
        {
            title: '企业名称',
            dataIndex: 'name',
            key: 'name',
            ellipsis: true
        },
        {
            title: '企业类型',
            dataIndex: 'type',
            key: 'type',
            ellipsis: true,
            render: (id) => {
                switch (id) {
                    case 2:
                        return '供应商';

                    case 3:
                        return '质检机构';

                    case 4:
                        return '监管机构';

                    case 5:
                        return '物流企业';

                    default:
                        return '-';
                }
            }
        },
        {
            title: '参与方状态',
            dataIndex: 'status',
            key: 'status',
            ellipsis: true,
            render: (data: any) => (
                <span style={{ color: data ? '#F64041' : '#666666' }}>
                    <Badge
                        status={data ? 'error' : 'success'}
                        color={data ? '#F64041' : 'rgb(36, 171, 59)'}
                        text={data ? '禁用' : '可用'}
                    />
                </span>
            )
        },
        {
            width: 200,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='small'>
                    <BaseButton
                        type='dashed'
                        className={record.status ? 'primaryBtn' : 'warnBtn'}
                        onClick={
                            record.status
                                ? () => {
                                      partmodiy.mutate({
                                          opt: !record?.status ? 'DISABLE' : 'ENABLE',
                                          id: record?.id
                                      });
                                  }
                                : () => {
                                      showConfirm(record);
                                  }
                        }
                    >
                        {record.status ? '启用' : '禁用'}
                    </BaseButton>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            navigate('/parties/manage/detail', {
                                state: {
                                    data: record
                                }
                            });
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    const queryPartiesModalConfig = {
        okText: '确定',
        title: '关联参与方',
        visible: queryModalVisible,
        setVisible: setQueryModelVisible,
        okHandle: async () => {
            const data = await queryPartiesForm.validateFields();
            partNamequery.mutate({
                name: data?.businessName?.trim()
            });
            // 获取参与方名称进行请求，调用接口成功后执行以下代码
        },
        onCancelHandle: () => {
            setQueryModelVisible(false);
            queryPartiesForm.resetFields();
        }
    };

    const correlationModalConfig = {
        okText: '确定',
        title: '关联参与方',
        visible: correlationModalVisible,
        setVisible: setCorrelationModalVisible,
        okHandle: async () => {
            associateParticipantMutation.mutate({
                id: participantRef.current.id
            });
            setCorrelationModalVisible(false);
        },
        onCancelHandle: () => {
            setCorrelationModalVisible(false);
        }
    };

    //查询
    const onFinish = (values: any) => {
        handlePaginationChange(1);
        queryuser.current = values;
        partquery.refetch();
    };
    //禁用
    const showConfirm = (record: any) => {
        confirm({
            title: '确定要禁用该参与方吗？',
            okText: '停用',
            cancelText: '取消',
            icon: <ExclamationCircleFilled rev={undefined} />,
            content: '禁用后该参与方无法再上传新的溯源数据，已上传的溯源数据不受影响',
            onOk() {
                partmodiy.mutate({
                    opt: !record?.status ? 'DISABLE' : 'ENABLE',
                    id: record?.id
                });
            },
            onCancel() {}
        });
    };

    const correlationModalItems = [
        {
            key: 'shortName',
            label: '企业名称',
            children: participantRef.current?.shortName
        },
        {
            key: 'identity',
            label: '企业类型',
            children: (() => {
                switch (participantRef.current?.identity) {
                    case 2:
                        return '供应商';

                    case 3:
                        return '质检机构';

                    case 4:
                        return '监管机构';

                    case 5:
                        return '物流企业';

                    default:
                        return '-';
                }
            })()
        }
    ];

    return (
        <>
            <BaseCard
                title={<PageTitle title='参与方列表' />}
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                mt24
            >
                <div
                    // className="searchContainer"
                    className={`${styles.searchContainer} partiesManageSearchContainer`}
                >
                    <Form
                        style={{
                            width: '100%',
                            display: 'flex',
                            justifyContent: 'space-between'
                        }}
                        layout='inline'
                        labelAlign='left'
                        className='label-title'
                        onFinish={onFinish}
                        form={search}
                        // className='label-title'
                    >
                        <FilterForm labelCol={{ span: 6 }} itemConfig={searchConfig} />
                        <Space>
                            <BaseButton
                                type='primary'
                                htmlType='submit'
                                style={{ width: 100 }}
                                // className='searchBtn'
                                className={`${styles.searchBtn} ${styles.baseBtn}`}
                                icon={<SearchOutlined rev={undefined} />}
                            >
                                查询
                            </BaseButton>
                            <BaseButton
                                type='dashed'
                                className='primaryBtn'
                                style={{ width: 100 }}
                                icon={<SyncOutlined rev={undefined} />}
                                onClick={() => {
                                    queryuser.current = null;
                                    partquery.refetch();
                                    search.resetFields();
                                }}
                            >
                                重置
                            </BaseButton>
                            <BaseButton
                                type='dashed'
                                icon={<PlusOutlined rev={undefined} />}
                                className='greenBtn'
                                onClick={() => {
                                    // setAddModelVisible(true);
                                    setQueryModelVisible(true);
                                }}
                            >
                                关联参与方
                            </BaseButton>
                        </Space>
                    </Form>
                </div>
                <BaseTable
                    rowKey='account'
                    className='table-operation'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return <TableHead />;
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={partquery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageindex ? pageindex : pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={partquery.data?.data.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>

            <BaseModal {...queryPartiesModalConfig}>
                <Form form={queryPartiesForm}>
                    <Form.Item
                        label='企业名称'
                        rules={[
                            {
                                required: true,
                                message: '请输入您要关联的参与方的企业名称'
                            },
                            {
                                max: 40,
                                message: '请保持字符在40字符以内!'
                            }
                        ]}
                        name='businessName'
                    >
                        <Input placeholder='请输入您要关联的参与方的企业名称'></Input>
                    </Form.Item>
                </Form>
            </BaseModal>

            <BaseModal {...correlationModalConfig}>
                <Descriptions title='企业信息如下，请确认是否关联该参与方' column={1}>
                    {correlationModalItems?.map((correlation) => (
                        <Descriptions.Item key={correlation?.key} label={correlation?.label}>
                            {correlation?.children}
                        </Descriptions.Item>
                    ))}
                </Descriptions>
            </BaseModal>

            {/*// 原添加参与方代码*/}
            {/*<BaseModal {...addPartiesConfig}>*/}
            {/*    <Form name='addPartiesForm' form={addPartiesForm} className='edit-label-title'>*/}
            {/*        {<FilterForm itemConfig={addPartiesConfigs} />}*/}
            {/*    </Form>*/}
            {/*</BaseModal>*/}
        </>
    );
};

export default WithPaginate(PartiesManager);

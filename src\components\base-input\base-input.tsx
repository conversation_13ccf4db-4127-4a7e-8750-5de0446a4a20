/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-06-28 18:20:00
 * @LastEditTime: 2022-10-11 13:45:57
 * @LastEditors: PhilRandWu
 */
import React, { useState } from 'react';
import { Input } from 'antd';
import type { InputProps } from 'antd';

import './override.less';

const BaseInput = React.forwardRef((props: InputProps, ref: any) => {
    return <Input className={`base-input ${props.className}`} ref={ref} {...props} />;
});

export default React.memo(BaseInput);

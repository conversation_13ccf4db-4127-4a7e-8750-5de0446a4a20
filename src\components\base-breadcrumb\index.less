.Bread {
    margin: 0 24px;

    .ant-card-body {
        padding: 24px 0px 16px 25px;
    }

    .BreadcrumbContainer {
        box-sizing: border-box;
        padding: 16px 23px;
        padding-left: 0px;
        // background: linear-gradient(180deg, #f5f7f9 0%, #ffffff 100%);
        // box-shadow: 0px 7px 7px 0px #e5eaf1, -4px 0px 13px 0px #ffffff;
        // border-radius: 4px;
        // border: 2px solid #ffffff;
    }

    .Breadcrumb {
        display: flex;
    }

    .BreadcrumbItem {
        display: flex;
        font-size: 14px !important;
        line-height: 20px;
        font-weight: 500;
        color: #c7c7c7;
    }

    .BreadcrumbItem a {
        color: #c7c7c7;
    }

    .division {
        margin: 0 6px;
    }

    .lastBreadcrumbItem {
        color: #5b5b69 !important;
    }

    .curRoute {
        margin-top: 18px;
        line-height: 22px;
        font-size: 18px;
        font-weight: 500;
        color: #333333;
    }
}

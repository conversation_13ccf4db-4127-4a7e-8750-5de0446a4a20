import React, { ReactElement, useContext, useState, useEffect, Suspense } from 'react';
import { Layout, Button, Menu, Avatar, Dropdown, Space, Spin, message } from 'antd';
import { DownOutlined, UpOutlined, UserOutlined } from '@ant-design/icons';
import { Outlet, resolvePath, useNavigate, useLocation } from 'react-router-dom';

// @ts-ignore
import platformIcon from '@assets/icon/logo.png';
import newplatformIcon from '@assets/icon/newlogo.png';

import { getInitialRoute, getCurUserAuthRoute } from '@router';

import { useAppDispatch, useAppSelector } from '@store';
import type { MenuProps } from 'antd';
import { permissionAction, updataisFill } from '@store/slice-reducer/user';
import './sider.less';
import { useAccountList } from 'myhooks/useaccountmanage';
import { useQuery } from 'react-query';
import { getUserPermission } from '@services/menu';
import { getAllIds } from './config';
import { menuIdMap, menuIdToUrl } from '@config';

const { Header, Content, Sider } = Layout;

const routeConfigToSiderConfig = (routeConfig: any): MenuProps['items'] => {
    const convert = (config: any, basePath = ''): MenuProps['items'] => {
        const ret = [];
        for (let i = 0; i < config.length; i++) {
            const item = config[i];
            if (
                !item.showInSider ||
                item.isNoPermission ||
                (item.children && item.children.every((item: any) => item.isNoPermission === true))
            ) {
                continue;
            }
            if (item.children && item.children.every((item: any) => item.showInSider)) {
                const siderConfigGroupItem = {
                    label: item.label,
                    key: resolvePath(item?.path || '', basePath).pathname,
                    ...(!item.children[0]?.index
                        ? {
                              children: convert(item.children, '/' + item.path)
                          }
                        : {}),
                    icon:
                        item.icon &&
                        (React?.isValidElement(item?.icon) ? (
                            item?.icon
                        ) : (
                            <img className='sider-menu-icon' src={item.icon} alt={`${item.label}-logo`} />
                        ))
                };
                ret.push(siderConfigGroupItem);
            } else {
                let enablePath = item?.path || '';
                if (!item?.element && !item?.children?.[0]?.index) {
                    // 当级路由无法匹配的情况
                    enablePath = getInitialRoute([item]);
                }
                const siderMenuItemPath = resolvePath(enablePath, basePath).pathname;
                const siderConfigItem = {
                    label: item.label,
                    key: siderMenuItemPath,
                    icon:
                        item.icon &&
                        (React?.isValidElement(item?.icon) ? (
                            item?.icon
                        ) : (
                            <img className='sider-menu-icon' src={item.icon} alt={`${item.label}-logo`} />
                        ))
                };
                ret.push(siderConfigItem);
            }
        }
        return ret;
    };
    return convert(routeConfig);
};

const AppSider: React.FC = (props) => {
    const navigate = useNavigate();
    const location = useLocation();

    const dispatch = useAppDispatch();
    const userInfo = useAppSelector((store) => store.user);
    const [openKeys, setOpenKeys] = useState<string[]>([]);
    const [permissionIds, setPermissionIds] = useState<string[]>([]);
    const [collapsed, setCollapsed] = useState(false);

    const getLoginUserPermission: any = useQuery(['getLoginUserPermission'], () => getUserPermission(), {
        onSuccess(response) {
            let originRes = JSON.parse(JSON.stringify(response));
            let res = originRes;
            // 前端实现侧边栏过滤
            if ((userInfo.userInfo.identity === 1 || userInfo.userInfo.identity === 8) && userInfo.isFill === 0) {
                //1为核心企业
                res['data'] = res?.data.filter((item: any) => item.id === 2);
                res['data'][0]['childrenMenu'] = res['data'][0]['childrenMenu'].filter((item: any) => item.id === 102);
            }
            const permissions = getAllIds(res?.data);
            const menu = permissions.map((menuId: any) => menuIdMap[menuId]);

            dispatch(
                permissionAction({
                    menuPermissionList: menu
                })
            );
            setPermissionIds(menu);
            if (!!sessionStorage.getItem('islogin') || !userInfo?.userInfo?.privateKey) {
                return;
            }
            if (!!!userInfo.privateKey) {
                navigate('/private');
                return;
            }
            if ((userInfo.userInfo.identity === 1 || userInfo.userInfo.identity === 8) && userInfo.isFill === 0) {
                //1为核心企业
                message.info('温馨提示：请您及时填写企业信息，否则将无法使用平台其他功能');
                navigate('/account/basicInfo');
                return;
            }
            navigate(menuIdToUrl[res.data[0]?.childrenMenu[0]?.id]);
            sessionStorage.setItem('islogin', '1');
        },
        onError(err) {},
        retry: true
    });

    const getCurOpenKeys = () => {
        const paths = location.pathname.split('/').slice(1, -1);
        const curOpenKeys = paths.map((item, index) => {
            return '/' + paths.slice(0, index + 1).join('/');
        });
        setOpenKeys(curOpenKeys);
    };
    //parties raw product qcqa source-data
    const sidername = ['parties', 'raw', 'product', 'qcqa', 'source-data']; //选中效果
    useEffect(() => {
        getCurOpenKeys();
        // 进入企业编辑页面重查下菜单列表
        if (
            location.pathname === '/account/basicInfo' &&
            (userInfo.userInfo.identity === 1 || userInfo.userInfo.identity === 8) &&
            sessionStorage.isFill === '1'
        ) {
            getLoginUserPermission.refetch();
        }
    }, [location.pathname]);

    return (
        <Sider
            width={265}
            // collapsible
            // collapsed={collapsed}
            // onCollapse={(value, type) => {
            //     if (!value) {
            //         getCurOpenKeys();
            //     }
            //     setCollapsed(value);
            // }}
            // breakpoint='lg'
            // trigger={null}
            className='sider-container'
        >
            <div className='sider-header'>
                <img className='sider-header-logo' src={newplatformIcon} alt='logo' />
                {!collapsed && <div className='sider-header-app-name'>{sessionStorage.systemTitle}</div>}
            </div>
            <Menu
                className='siderMenu'
                // defaultSelectedKeys={['admin-manage']}
                // defaultOpenKeys={['permission']}
                items={routeConfigToSiderConfig(getCurUserAuthRoute(permissionIds, userInfo, true))}
                // theme='dark'
                mode='inline'
                onSelect={({ key }) => {
                    if (!userInfo?.privateKey) {
                        message.info('请配置口令密码');
                        return;
                    }
                    if (userInfo?.isFill === 0 && userInfo?.userInfo?.roleId === 1) {
                        navigate('account/basicInfo');
                        message.info('请配置企业信息');
                        return;
                    }
                    navigate(key);
                }}
                selectedKeys={
                    location.pathname.split('/')[1] === 'core-flrm'
                        ? [location.pathname.split('/').slice(0, 2).join('/')]
                        : sidername.indexOf(location.pathname.split('/')[1]) > -1
                        ? [location.pathname.split('/').slice(0, 3).join('/')]
                        : location.pathname.split('/').length >= 4
                        ? [location.pathname.split('/').slice(0, 3).join('/')]
                        : [location.pathname]
                }
                // selectedKeys={['/' + location.pathname.split('/').slice(-1)]}
                openKeys={openKeys}
                onOpenChange={(keys) => {
                    setOpenKeys(keys);
                }}
                inlineIndent={41}
            ></Menu>
            <div className='bottomDev'>
                <span className='bottom-font'>Copyright 2024 中国移动信息技术中心.</span>
                <span className='bottom-font' style={{ bottom: '10px' }}>
                    All Rights Reserved.
                </span>
            </div>
        </Sider>
    );
};

export default AppSider;

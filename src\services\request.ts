/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-07-11 21:10:02
 * @LastEditTime: 2022-11-01 18:03:08
 * @LastEditors: PhilRandWu
 */
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { message } from 'antd';
import store from '@store';
import { logoutAction } from '@store/slice-reducer/user';
import { showLoading }  from '@store/slice-reducer/spinLoading';
import { dealJson, decryptedUrl } from '@utils';

// import errorHandler from "@utils";
const dispatch = store.dispatch;

let showMessage = true;

export const instance = axios.create({
    baseURL: '/api',
    timeout: 60000
});
instance.interceptors.request.use(
    (config:any) => {
        if(config?.method === 'post' && !(config?.url.indexOf('page')>0||config?.url.indexOf('Page')>0||config?.url.indexOf('List')>0)){
            console.log('进来了吗？', config)
            dispatch(showLoading(true))
        }
        const Authentication = sessionStorage.getItem('jwt');
        if (Authentication) {
            config.headers.token = Authentication;
        }
        return config;
    },
    (err) => {
        console.log('response err', err);
        return Promise.reject(err);
    }
);
instance.interceptors.response.use(
    (response: AxiosResponse<any, any>) => {
        return new Promise(async(resolve, reject) => {
            console.log('instance.interceptors.response', response);
            if(response?.config?.method==='post'){
                console.log('返回了吗？')
                dispatch(showLoading(false))
            }
            if (response.headers['content-type'] === "application/json") {
                if (response?.data?.code === 200 || typeof response?.data == 'string') {
                    // console.log('instance data');
                    console.log('response.data--start',response,response.data)
                    const  pathArr:any = ['/org/getOrgDetail','/product/getProductDetail','/place/getPlaceDetail','/process/getDetail','/quality/getDetail','/trace-code/detail','/process/getDetailByApp','/trace-code/trace/data','/trace-code/trace/product','/trace-code/trace/inspection','/trace-code/trace/org']
                    const key =response.config?.url?.split("?")?.[0] || response.config?.url
                    console.log('response.config?.url',response.config?.url)
                    if(pathArr.includes(key)){
                        // const newArr = await dealJson(JSON.parse(JSON.stringify(response.data.data)))
                        // response.data['data'] = newArr[0]
                        // console.log('renewArr====end',newArr)
                        // console.log('response.data--end---',response.data,response.data.data)
                        resolve(response.data);
                    }else{
                        resolve(response.data);
                    }
                } else {
                    // console.log('instance.interceptors.response reject', response);
                    reject(response);
                }
            } else if (response?.data?.code === 5001020010 || response?.data?.code === 5001010017) {
                if (showMessage) {
                    showMessage = false;
                    message.destroy()
                    message.error(response?.data?.message, 2)
                    setTimeout(() => {
                        window.location.href = '/login';
                        dispatch(logoutAction())
                        localStorage.clear();
                        sessionStorage.clear();
                    }, 2000);
                }
            }
            else {
                console.log('进二二二二二==')
                resolve(response.data);
            }
        });
    },
    (err) => {
        dispatch(showLoading(false))
        window.addEventListener('popstate', function () {
            window.history.pushState(null, null || '', window.location.href);
        })
        const loginmessage = ['用户名错误或用户不存在', '密码错误', '用户名或密码错误', '用户名或密码错误']
        console.log('response err', err, err?.response?.data?.message);
        const timeout = err?.config?.timeout;
        if (err?.message === 'Request failed with status code 403') {
            dispatch(logoutAction())  //禁止返回
            if (err?.response?.data?.message === 'jwt解析异常') {
                // message.error('登录凭证失效','请重新登录');
            } else {
                message.error(err?.response?.data?.message)
            }
            localStorage.clear();
            sessionStorage.clear();
            // message.error(err?.response?.data?.message)
        }
        if (err?.message === 'Request failed with status code 401') {
            window.location.href = '/login';
            message.error('登录凭证失效，请重新登录', 5);
            sessionStorage.removeItem('privateKey');
            message.destroy();
        }
        if (err?.message === `timeout of ${timeout}ms exceeded`) {
            message.error('请求超时');
            message.destroy();
        }
        if (loginmessage.indexOf(err?.message) !== -1) {
            message.error('用户名或密码错误');
            message.destroy();
        }
        return Promise.reject(err);
    }
);

export default instance.request;

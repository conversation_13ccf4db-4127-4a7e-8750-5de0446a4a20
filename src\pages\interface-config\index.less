.cancel {
    display: inline-block;
}

.interface-config {
    .base-card-mt24 {
        margin-top: 0;
    }
    .warnTips {
        font-size: 14px;
        color: #fa5151;
        margin: 10px 0px 40px 0;
        font-family: PingFangSC-Medium, PingFang SC;
    }
    .reset,
    .create {
        width: 100px;
    }
}
.privatekey-publickey-warp {
    margin: 0 6px 0 35px;
    .key-wrap {
        display: flex;
        .key-name {
            font-size: 13px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #495870;
            width: 100px;
            // margin-left: 45px;
        }
        .key-list {
            margin-bottom: 15px;
            display: flex;
            width: 100%;
            justify-content: space-between;
            .key {
                font-size: 13px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #333333;
                word-break: break-all;
                margin-right: 25px;
            }
        }
    }
    .key-tips {
        font-size: 13px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ff3b30;
        // margin-left: 45px;
    }
}
.ant-modal-header {
    border-bottom: 0px solid #f0f0f0;
    .ant-modal-title {
        font-family: PingFangSC-Medium, PingFang SC;
        color: #43425d;
        font-weight: 600;
    }
}
.ant-modal-footer {
    border-top: 0px solid #f0f0f0;
}
.interface-config-form {
    margin-top: 40px;
}

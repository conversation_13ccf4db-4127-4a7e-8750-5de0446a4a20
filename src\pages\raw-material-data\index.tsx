import { useState } from 'react';
import { Badge, Card, Col, Form, message, Row, Space } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';

import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';
import BaseInput from '@components/base-input/base-input';
import BaseSelect from '@components/base-select/base-select';
import BaseDatePicker from '@components/base-date-picker';

import styles from './index.module.less';
import { RoleEnum } from '@config';

import { DownOutlined, UpOutlined } from '@ant-design/icons';
import RawMaterialService, { SourceRawMaterialStateEnum } from '@services/traceability_data/raw_material';
import { Enum2Object } from '@utils/enum';
import PublicSourceService from '@services/traceability_data/public_source';
import { useAppSelector } from '@store';
import { QueryTime } from '@utils';

interface IUrlState {
    pageIndex: number;
    pageSize: number;
    // ...
}

const RawMaterial = () => {
    const userInfo = useAppSelector((store) => store.user);
    const LocalLoginIdentity = Number(userInfo?.userInfo?.identity);
    const [queryParams, setQueryParams] = useState<any>({ pageSize: 10, pageIndex: 1 });

    const [form] = Form.useForm();
    const navigate = useNavigate();

    const [isSimpleSearch, setIsSimpleSearch] = useState(true);

    const queryRawMaterialList = useQuery(['queryRawMaterialList'], () => PublicSourceService.RawMaterialList());
    const queryRawMaterialListData: any[] = queryRawMaterialList?.data?.data;

    const queryOrgList = useQuery(['queryOrgList'], () =>
        PublicSourceService.getOrgList({
            identity: RoleEnum['供应商']
        })
    );
    const queryOrgListData: any[] = queryOrgList?.data?.data;

    const queryCoreOrgList = useQuery(['queryCoreOrgList'], () => PublicSourceService.getCoreList(), {
        enabled: [RoleEnum.平台方]?.includes(LocalLoginIdentity)
    });
    const queryCoreOrgListData: any[] = queryCoreOrgList?.data?.data;

    const queryRawMaterial: any = useQuery(
        ['queryRawMaterial', queryParams],
        () =>
            RawMaterialService.Query({
                ...queryParams
            }),
        {
            onSuccess() {},
            onError() {}
        }
    );
    const coreEnterpriseColumns = () => {
        return [RoleEnum.平台方].includes(LocalLoginIdentity)
            ? [
                  {
                      title: '生产加工企业', //生产加工企业
                      dataIndex: 'coreName',
                      key: 'coreName',
                      ellipsis: true
                  }
              ]
            : [];
    };

    const columns: ColumnsType<any> = [
        {
            title: '原料采购批次',
            dataIndex: 'purchaseBatch',
            key: 'purchaseBatch',
            ellipsis: true
        },
        {
            title: '原料名',
            dataIndex: 'materialName',
            key: 'materialName',
            ellipsis: true
        },
        {
            title: '企业名称',
            dataIndex: 'shortName',
            key: 'shortName',
            ellipsis: true
        },
        {
            title: '操作人',
            dataIndex: 'optName',
            key: 'optName',
            ellipsis: true
        },
        ...coreEnterpriseColumns(),
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true,
            render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
        },
        {
            title: '哈希',
            dataIndex: 'transactionId',
            key: 'transactionId',
            ellipsis: true
        },
        {
            title: '状态',
            dataIndex: 'state',
            key: 'state',
            ellipsis: true,
            render: (_, row) => <Badge status='success' text='可用' />
        },

        {
            title: '操作',
            render: (_, row) => (
                <BaseButton type='dashed' className='primaryBtn' onClick={() => navigate(`detail/${row?.id}`)}>
                    查看详情
                </BaseButton>
            )
        }
    ];

    const searchFormItems = [
        <Form.Item label='批次号' name='purchaseBatch'>
            <BaseInput placeholder='请输入批次号'></BaseInput>
        </Form.Item>,
        <Form.Item label='原料名' name='materialId'>
            <BaseSelect
                placeholder='请选择'
                options={queryRawMaterialListData?.map((item) => ({
                    label: item?.materialName,
                    value: item?.id
                }))}
            ></BaseSelect>
        </Form.Item>,
        <Form.Item label='创建时间' name='createTime'>
            <BaseDatePicker style={{ width: '100%' }}></BaseDatePicker>
        </Form.Item>,
        <Form.Item label='状态' name='state'>
            <BaseSelect placeholder='请选择' options={Enum2Object(SourceRawMaterialStateEnum)}></BaseSelect>
        </Form.Item>,
        <Form.Item label='企业名称' name='orgId'>
            <BaseSelect
                placeholder='请选择'
                options={queryOrgListData?.map((item) => ({
                    label: item?.shortName,
                    value: item?.id
                }))}
            ></BaseSelect>
        </Form.Item>,
        ...([RoleEnum.平台方].includes(LocalLoginIdentity)
            ? [
                  <Form.Item label='生产加工企业' name='coreId'>
                      <BaseSelect
                          placeholder='请选择'
                          options={queryCoreOrgListData?.map((item) => ({
                              label: item?.shortName,
                              value: item?.id
                          }))}
                      ></BaseSelect>
                  </Form.Item>
              ]
            : [])
    ];

    return (
        <>
            <Card style={{ marginBottom: 10 }} title={<PageTitle title='原料数据列表' />}>
                <Form
                    form={form}
                    labelCol={{ span: 5 }}
                    labelAlign='left'
                    onFinish={(values) => {
                        console.log(values, 'values');
                        const TimeArr = QueryTime(values?.createTime);
                        setQueryParams({
                            ...values,
                            purchaseBatch: values?.purchaseBatch?.trim(),
                            startTime: TimeArr?.[0],
                            endTime: TimeArr?.[1],
                            pageIndex: 1,
                            pageSize: 10
                        });
                    }}
                    className='label-title label-title-more'
                >
                    <Row gutter={[36, 12]}>
                        {searchFormItems.slice(0, isSimpleSearch ? 2 : searchFormItems.length).map((searchFormItem) => (
                            <Col key={searchFormItem.key} span={8}>
                                {searchFormItem}
                            </Col>
                        ))}
                        <Col span={[RoleEnum.平台方].includes(LocalLoginIdentity) && !isSimpleSearch ? 24 : 8}>
                            <div style={{ display: 'flex', justifyContent: 'end' }}>
                                <Space>
                                    <BaseButton type='primary' htmlType='submit'>
                                        查询
                                    </BaseButton>
                                    <BaseButton
                                        onClick={() => {
                                            form.resetFields();
                                            setQueryParams({
                                                pageIndex: 1,
                                                pageSize: 10
                                            });
                                        }}
                                    >
                                        重置
                                    </BaseButton>
                                    <BaseButton
                                        type='link'
                                        style={{ color: '#80a932' }}
                                        onClick={() => {
                                            setIsSimpleSearch(!isSimpleSearch);
                                        }}
                                    >
                                        {isSimpleSearch ? '展开' : '收起'}
                                        {isSimpleSearch ? (
                                            <DownOutlined rev={undefined} />
                                        ) : (
                                            <UpOutlined rev={undefined} />
                                        )}
                                    </BaseButton>
                                </Space>
                            </div>
                        </Col>
                    </Row>
                </Form>
            </Card>
            <BaseCard className={styles.coreFIrmContainer}>
                <BaseTable
                    loading={queryRawMaterial.isFetching}
                    columns={columns}
                    dataSource={(queryRawMaterial?.data?.data?.records as any[]) || []}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={queryParams.pageIndex}
                    pageSize={queryParams.pageSize}
                    total={queryRawMaterial?.data?.data?.total}
                    onShowSizeChange={(page, pageSize) => {
                        setQueryParams({
                            ...queryParams,
                            pageIndex: page,
                            pageSize
                        });
                    }}
                    onChange={(page, pageSize) => {
                        setQueryParams({
                            ...queryParams,
                            pageIndex: page,
                            pageSize
                        });
                    }}
                />
            </BaseCard>
        </>
    );
};

export default RawMaterial;

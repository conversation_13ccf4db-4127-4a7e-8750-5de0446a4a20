import request from '../request';
// 仓储管理列表

export const warehouseListData = (obj: any) => {

  return request({
      url: '/traceData/warehousePage',
      method: 'post',
      data: obj
  });
};
// 仓储信息详情

export const getWareDetailData = (obj: any) => {
  return request({
      url: `/traceData/warehouseInfo`,
      method: 'get',
      params: obj
  });
};


//作废
export const cancelWare = (obj: any) => {
  return request({
      url: `/warehouse/cancel`,
      method: 'get',
      params: obj
  });
};



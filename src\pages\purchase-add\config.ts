/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 10:20:37
 * @LastEditTime: 2022-10-10 11:04:43
 * @LastEditors: PhilRandWu
 */
export const addConfigs = [
    {
        type: 'Input',
        label: '食品名称',
        value: 'name',
        rules: [{ required: true, message: '请输入食品名称!' }],
        placeholder: '请输入食品名称'
    },
    {
        type: 'Input',
        label: '食品编码',
        value: 'code',
        placeholder: '请输入食品编码'
    },
    {
        type: 'Cascader',
        label: '食品品类',
        value: 'type',
        rules: [{ required: true, message: '请选择食品品类!' }],
        placeholder: '请选择食品品类',
        defaultValue: ['zhejiang', 'hangzhou', 'xihu'],
        fields: [
            {
                value: 'zhejiang',
                label: 'Zhejiang',
                children: [
                    {
                        value: 'hangzhou',
                        label: 'Hangzhou',
                        children: [
                            {
                                value: 'xihu',
                                label: 'West Lake'
                            }
                        ]
                    }
                ]
            },
            {
                value: 'jiangsu',
                label: '<PERSON><PERSON>',
                children: [
                    {
                        value: 'nanjing',
                        label: 'Nanjing',
                        children: [
                            {
                                value: 'zhonghuamen',
                                label: 'Zhong Hua Men'
                            }
                        ]
                    }
                ]
            }
        ]
    },
    {
        type: 'Input',
        label: '食品单位',
        value: 'business',
        rules: [{ required: true, message: '请输入食品单位!' }],
        placeholder: '请输入食品单位'
    },
    {
        type: 'Input',
        label: '保质期',
        value: 'time',
        rules: [{ required: true, message: '请输入保质期!' }],
        placeholder: '请输入保质期'
    },
    {
        type: 'Input',
        label: '食品规格',
        value: 'size',
        placeholder: '请输入食品规格'
    },
    {
        type: 'Input',
        label: '食品执行标准',
        value: 'standard',
        placeholder: '请输入食品执行标准'
    },
    {
        type: 'Upload',
        label: '食品图片',
        value: 'img',
        // rules: [{ required: true, message: '请输入食品规格!' }],
        placeholder: '(尺寸要求: 278X167、556X337、834X501)'
    },
    {
        type: 'TextArea',
        label: '食品说明',
        value: 'note',
        placeholder: '请输入说明',
        showCount: true
    }
];

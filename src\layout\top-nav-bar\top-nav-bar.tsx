import { DownOutlined } from '@ant-design/icons';
import { RoleEnum } from '@config';
import { logOut } from '@services/login';
import { coreCompanyList, switchCoreCompany } from '@services/participantPage';
import { getUserInfo } from '@services/user';
import { useAppSelector } from '@store';
import { showPasswordInput } from '@store/slice-reducer/app';
import { loginAction, logoutAction } from '@store/slice-reducer/user';
import { decryptStr } from '@utils';
import { ReformChainError } from '@utils/errorCodeReform';
import { Button, Dropdown, Form, Input, Layout, Menu, Modal, Space, message } from 'antd';
import React, { useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { checkAndSavePassword } from '../../utils/blockChainUtils';
import './styles.less';

const TopNavBar = () => {
    // const username: any = localStorage.getItem('userdata');
    // const userdata = JSON.parse(username);
    const dispatch = useDispatch();
    const state = useSelector((state: any) => state.appContext);
    const userInfo = useAppSelector((store) => store.user);
    const navigate = useNavigate();

    const [isLoginCheckEnd, setIsLoginCheckEnd] = React.useState(false);
    const [privateKeyPassword, setPrivateKeyPassword] = useState('');
    const [privateKeyPasswordModal, setPrivateKeyPasswordModal] = useState(false);
    // const [isActive, setisActive] = useState(false);
    // const [toolTipVisible, settoolTipVisible] = useState(false);

    const coreCompanyQuery = useQuery(['coreCompanyQuery'], () => coreCompanyList(), {
        onError(err: any) {
            ReformChainError(err);
        },
        enabled: [RoleEnum.质检机构, RoleEnum.供应商, RoleEnum.监管机构, RoleEnum.物流企业].includes(
            RoleEnum[userInfo.role]
        )
    });

    const getUserInfoQuery = useQuery(['getUserInfoQuery'], getUserInfo, {
        enabled: true, //登录管理员判断
        async onSuccess(loginRes) {
            const telephone = await decryptStr(loginRes?.data?.phoneNumber);
            const userName = await decryptStr(loginRes?.data?.userName);
            const userinfo = {
                name: userName,
                user_id: loginRes?.data?.userId,
                role_id: loginRes?.data?.roleId,
                circle_id: loginRes?.data?.circleId,
                company_id: loginRes?.data?.companyId,
                isFirst: loginRes?.data?.isFirst,
                telephone: telephone,
                privateKey: loginRes?.data?.privateKey,
                publicKey: loginRes?.data?.publicKey
            };
            localStorage.setItem('userdata', JSON.stringify(userinfo));
            sessionStorage.setItem('encryptKey', loginRes?.data?.privateKey);
            dispatch(
                loginAction({
                    userInfo: Object.assign({}, loginRes?.data, userinfo)
                })
            );
            setIsLoginCheckEnd(true);
        },
        onError(err: any) {
            ReformChainError(err);
            setIsLoginCheckEnd(true);
        },
        retry: false
    });

    const switchCoreCompanyMutation = useMutation(switchCoreCompany, {
        onSuccess(res) {
            const jwt = res?.data;
            sessionStorage.setItem('jwt', jwt);
            getUserInfoQuery.refetch();
            setTimeout(() => {
                window.history.replaceState({}, document.title, window.location.href.split('?')[0]);
                window.location.reload();
            }, 1000);
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });

    const menu = (
        <Menu
            className='dropDownContainer'
            items={[
                {
                    key: '1',
                    label: (
                        <span
                            className='changePSW'
                            style={{
                                display: 'block',
                                textAlign: 'center'
                            }}
                            onClick={() => {
                                // let change = userInfo?.userInfo?.menuList?.some((item: any) => {
                                //     return item === 2;
                                // });
                                if (userInfo?.privateKey === undefined) {
                                    message.info('请配置口令密码');
                                    return;
                                } else if (userInfo?.isFill === 0 && userInfo?.userInfo?.roleId === 2) {
                                    message.info('请配置基础信息');
                                    return;
                                } else {
                                    navigate('/admin/password-reset');
                                }
                            }}
                        >
                            修改密码
                        </span>
                    )
                },
                {
                    key: '2',
                    label: (
                        <span
                            style={{
                                display: 'block',
                                textAlign: 'center'
                            }}
                            onClick={() => navigate('/private')}
                        >
                            私钥管理
                        </span>
                    )
                }
            ]}
        />
    );

    const companyMenu = (
        <Menu
            className='dropDownContainer'
            items={coreCompanyQuery?.data?.data?.map((item: any) => {
                return {
                    key: `${item?.orgId}`,
                    label: (
                        <span
                            className='changePSW'
                            style={{
                                display: 'block',
                                textAlign: 'center'
                            }}
                            onClick={(value) => {
                                message.loading('正在切换生产加工企业');
                                switchCoreCompanyMutation.mutate({
                                    id: item?.orgId
                                });
                            }}
                        >
                            {item?.orgName}
                        </span>
                    )
                };
            })}
        />
    );

    const checkPassword = () => {
        message.loading('正在校验密码,请稍等...', 0);
        const privateKey = checkAndSavePassword(privateKeyPassword);
        
        if (privateKey) {
            dispatch(
                showPasswordInput({
                    showPasswordInput: false
                })
            );
            message.destroy();
            message.success('验证成功，请继续操作');
            setPrivateKeyPassword('');
            setPrivateKeyPasswordModal(false);
        } else {
            message.destroy();
            message.error('口令密码错误,请重新输入');
        }
    };

    const renderInputPasswordModal = () => {
        return (
            <Modal
                centered={true}
                width={400}
                maskClosable={false}
                visible={state.showPasswordInput}
                destroyOnClose
                onCancel={() => {
                    setPrivateKeyPassword('');
                    dispatch(
                        showPasswordInput({
                            showPasswordInput: false
                        })
                    );
                }}
                title={<div className='text-center text-base'>请输入口令密码</div>}
                footer={
                    <div
                        className='w-full flex justify-around px-20 border-t-4'
                        style={{
                            width: '100%',
                            marginBottom: 5
                        }}
                    >
                        <Button
                            style={{ height: 28, paddingTop: 2, fontSize: 14 }}
                            type='primary'
                            onClick={checkPassword}
                        >
                            确认
                        </Button>
                    </div>
                }
            >
                <Form preserve={false}>
                    <Form.Item label='口令密码' style={{ color: '#596174', fontSize: 15 }}>
                        <Input.Password
                            style={{ maxWidth: 200, backgroundColor: '#fff' }}
                            value={privateKeyPassword}
                            onChange={(e) => setPrivateKeyPassword(e.currentTarget.value)}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        );
    };

    return (
        <Layout.Header className='app-top-nav-bar' style={{ minWidth: 1250 }}>
            <Space size='large'>
                {[RoleEnum.质检机构, RoleEnum.供应商, RoleEnum.监管机构, RoleEnum.物流企业].includes(
                    RoleEnum[userInfo.role]
                ) ? (
                    <Dropdown overlay={companyMenu} placement='bottom' trigger={['hover']}>
                        <Button>{userInfo?.userInfo?.shortName}</Button>
                    </Dropdown>
                ) : null}
                <Space className='handleBtn'>
                    <Dropdown
                        overlay={menu}
                        placement='bottomRight'
                        trigger={['hover']}
                        // visible={isActive && !toolTipVisible}
                        overlayStyle={{
                            width: 98,
                            minWidth: 98
                        }}
                        // visible={true}
                    >
                        <Space>
                            {/* <img src={Account} /> */}
                            <pre>{userInfo.userInfo.name}</pre>
                            <DownOutlined rev={undefined} />
                            {/* <img src={dropDown} /> */}
                            {/* <DownOutlined /> */}
                        </Space>
                    </Dropdown>
                </Space>
                <Space className='handleBtn'>
                    {/* <img src={Logout} /> */}
                    <div
                        className='logout'
                        onClick={async () => {
                            const result: any = await logOut();
                            if (result?.code !== 200) {
                                message.error('登出失败');
                                return;
                            }
                            localStorage.clear();
                            sessionStorage.clear();
                            navigate('/login', { replace: false });
                            dispatch(logoutAction());
                            // dispatch(change({
                            //   id: '',
                            //   bucket_name: '',
                            // }))
                            // dispatch(setBucket({
                            //   setId: '',
                            //   setName: ''
                            // }))
                        }}
                    >
                        退出登录
                    </div>
                </Space>
            </Space>
            {renderInputPasswordModal()}
        </Layout.Header>
    );
};

export default React.memo(TopNavBar);

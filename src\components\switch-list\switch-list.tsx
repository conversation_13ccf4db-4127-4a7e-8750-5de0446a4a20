/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-11-01 18:39:10
 * @LastEditTime: 2022-11-01 18:39:11
 * @LastEditors: PhilRandWu
 */
/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 14:00:32
 * @LastEditTime: 2022-10-10 17:38:49
 * @LastEditors: PhilRandWu
 */
import { Divider, Form, List, Switch, Tooltip } from 'antd';
import './index.less';
import React, { useState,useEffect } from 'react';
import BaseTooptip from '@components/base-tooltip';

interface itemConfigInterface {
    type?: string;
    // "Display" | 'Switch'
    name?: string;
    title?: string;
    describe?: string;
    initial?: any;
    isShow?: any;
    changeStatus?: any;
}

interface configInterface {
    headTitle?: string;
    itemConfig: itemConfigInterface[];
    headData?: any;
    changeStatus?: any;
}

export default function SwitchList({ headTitle, itemConfig, changeStatus, headData }: configInterface) {
    console.log('itemConfig',itemConfig)
    let headerStatus:any =  itemConfig.length>1 &&itemConfig.filter((e) => e?.isShow === true).length > 0 ? true : false;  //列表下是否有选择的数据。
    const [headDat1a, setheadData] = useState(headData);
    const [data, setdata] = useState(headerStatus);
    console.log('datadata',headerStatus)

    useEffect(() => {
        if(itemConfig.filter((e) => e?.isShow === true).length > 0){
            console.log('jiant','执行')
            // setheaderStatus(true)
            setdata(true)
        }
        setdata(headerStatus)

        console.log('jiant','变化执行一次',headerStatus,data,headTitle,itemConfig,itemConfig.filter((e) => e?.isShow === true).length > 0 ? true : false)
    }, itemConfig);
    const lig: any = (values: any) => {
        console.log('lig',values)
        if (values === true) {
            setdata(true);
        } else {
            const nextData: any = [];
            itemConfig.forEach((e) => {
                nextData.push({
                    ...e,
                    isShow: false
                });
            });
            if (changeStatus) {
                changeStatus(nextData);
            }
            console.log('nextData1: ', nextData);
            setdata(false);
        }
    };
    return (
        <div className='SwitchListContainer'>
            {headTitle ? (
                <>
                    <div className='listHead'>
                        <span className='listHeadTitle'>{headTitle}</span>
                        <Form.Item name={headTitle} valuePropName='checked1213'>
                            <Switch defaultChecked={headerStatus} onChange={lig}  key={headerStatus} />
                        </Form.Item>
                    </div>
                    <Divider />
                </>
            ) : null}
            {data || headDat1a ? (
                <>
                    <List
                        itemLayout='horizontal'
                        dataSource={itemConfig}
                        renderItem={(item) => (
                            <List.Item>
                                <div className='displayContent' style={{ width: 200 }}>
                                    <BaseTooptip
                                        data={item?.title}
                                        slice={true}
                                        maxWidth={200}
                                        sliceData={`${
                                            typeof item?.title === 'string' && item?.title && item?.title.length > 15
                                                ? item?.title?.slice(0, 15)?.trim() + '...'
                                                : item?.title
                                        }`}
                                    ></BaseTooptip>
                                    {item.type === 'Display' ? (
                                        <div>{item?.describe}</div>
                                    ) : (
                                        <Form.Item name={item?.name}>
                                            <Switch
                                                defaultChecked={item?.isShow}
                                                key={item?.isShow}
                                                onChange={(value) => {
                                                    console.log('value1: ', value,item);
                                                    const nextData: any = [];
                                                    itemConfig.forEach((e) => {
                                                        if (e.title === item.title) {
                                                            nextData.push({
                                                                ...e,
                                                                isShow: value
                                                            });
                                                        } else {
                                                            nextData.push(e);
                                                        }
                                                    });
                                                    if (changeStatus) {
                                                        changeStatus(nextData);
                                                    }
                                                }}
                                            />
                                        </Form.Item>
                                    )}
                                </div>
                            </List.Item>
                        )}
                    />
                </>
            ) : null}
        </div>
    );
}

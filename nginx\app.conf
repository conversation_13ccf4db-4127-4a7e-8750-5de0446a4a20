server {
    listen       80;
    listen  [::]:80;
    server_name  ${CM_RICETRACE_WEB_HOST};

    set $domain_name ${CM_RICETRACE_WEB_HOST};
    if ($http_host != $domain_name) {
        return 403;
    }

    #access_log  /var/log/nginx/host.access.log  main;

        client_max_body_size 50m;

    location / {
        alias   /usr/share/nginx/html/;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-store, no-cache, must-revalidate";
        # secure
        # add_header Content-Security-Policy "default-src 'self'; img-src 'self' data: http: https:; style-src 'self' 'unsafe-inline';  script-src 'self' 'unsafe-inline'; connect-src '*'";
        add_header X-XSS-Protection "1";
        add_header X-Content-Type-Options "nosniff";
    }
    location /static {
        alias   /usr/share/nginx/html/static/;
        expires 1y;
        add_header Cache-Control "public";
        access_log off;
        # secure
        add_header Content-Security-Policy "default-src 'self'; img-src 'self' data:;";
        add_header X-XSS-Protection "1";
        add_header X-Content-Type-Options "nosniff";
    }
    location /api {
        rewrite ^/api/(.*)$ /$1 break;
        proxy_pass ${CM_RICETRACE_WEB_SERVER_HOST};
    }


    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

}
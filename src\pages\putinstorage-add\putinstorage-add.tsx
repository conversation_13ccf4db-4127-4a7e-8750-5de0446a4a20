/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-13 15:07:01
 * @LastEditTime: 2022-10-19 20:30:59
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import PageTitle from '@components/page-title';
import { useRef, useState, useEffect } from 'react';
import React from 'react';
import BaseTable from '@components/base-table';
import TableHead from '@components/table-head';

import { canInFoodList, productionBatchList, addStorageIn } from '@services/storage-in';
import { useMutation, useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { signData } from '../../utils/blockChainUtils';
import { getLocalPrivatekey } from '@utils/blockChainUtils';

import BaseModal from '@components/base-modal';
import BaseButton from '@components/base-button';
import FilterForm from '@components/filter-form';
import { addConfigs } from './config';
import { Button, Input, Space } from 'antd';
import { Form, message } from 'antd';
import './index.less';
import { ReformChainError } from '@utils/errorCodeReform';
import WithPaginate from '../../hoc/withpaginate';
import { useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
interface InStorageinterface {}
const PutInStorageAdd = (props: any) => {
    const navigate = useNavigate();
    const [FoodForm] = Form.useForm();
    const [SourceCodeForm] = Form.useForm();
    const [addEmployeesForm] = Form.useForm();
    const [food_id, setfood_id] = useState('');
    const [addModalVisible, setAddModelVisible] = useState(false);
    const { pageInfo, handlePaginationChange } = props;
    const dispatch = useDispatch();
    const foodData: any = useRef('');
    // const tablelist :any = useRef([])
    const [tablelist, settablelist]: any = useState([]);
    //仓存地点
    const [placedata, setplacedata] = useState('');
    const [mapToEnum_name, setmapToEnum_name]: any = useState({});
    const [mapToEnum_number, setmapToEnum_number]: any = useState({});
    const [mapToEnum_batch, setmapToEnum_batch]: any = useState({});
    // const mapToEnum_number: any = {};
    // const mapToEnum_batch: any = {};
    const addConfigs = [
        {
            type: 'Input',
            label: '入库单号:',
            value: 'inNumber',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback('请输入入库单号！');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入',
            span: 12,
            className: 'count'
        },
        {
            type: 'DatePicker',
            label: '入库时间:',
            value: 'inTime',
            rules: [{ required: true, message: '请输入!' }],
            placeholder: '请输入',
            span: 12,
            wide: 280
        },
        {
            type: 'Input',
            label: '仓储地点:',
            value: 'storageLocation',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback('请输入仓储地点');
                        } else if (value[0] === ' ' || value[value.length - 1] === ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入',
            span: 12,
            className: 'count'
        },
        {
            type: 'Button',
            label: '选择食品:',
            name: 'changefood',
            value: 'foodlist',
            required: 'required',
            rules: [{ required: true, message: '请选择食品' }],
            onClick: () => {
                console.log('123321');
                setAddModelVisible(true);
            },
            placeholder: '选择食品',
            color: 'primary',
            span: 12
        }
    ];
    //分页数据
    const productionquery: any = useQuery(
        ['purchsequery111111', pageInfo],
        () => {
            return canInFoodList({});
        },
        {
            onError(err: any) {
                ReformChainError(err);
            },
            onSuccess(res: any) {
                getLocalPrivatekey(dispatch);
                (res?.data || [])?.forEach((item: any, index: any) => {
                    mapToEnum_name[item.food_id] = item.food_name;
                    mapToEnum_number[item.food_id] = item.food_number;
                });
                const date = new Date().getTime();
                const datetext = date.toString().slice(1, 13);
                SourceCodeForm.setFieldsValue({
                    inNumber: datetext
                });
                console.log('res999999', res);
            }
        }
    );
    //生产批次
    const productionList = useMutation(productionBatchList, {
        onSuccess(res) {
            (res?.data || [])?.forEach((item: any, index: any) => {
                mapToEnum_batch[item.id] = item.production_batch;
            });
            console.log('res', res);
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    console.log('productionList?.data?.data', productionList?.data?.data);
    //新增
    const addStoragein = useMutation(addStorageIn, {
        onSuccess(res) {
            message.success('添加成功');
            navigate('/cpsjsr/putIn');
        },
        onError(err: any) {
            ReformChainError(err);   //数量报错
        }
    });
    //监听数据变化
    const onFieldsChange=( values:any, errorFields:any)=>{
        if(values[0].name[0]==='food'){
            addEmployeesForm.setFieldsValue({foodbatch: null })      //清空生产批次输入框
        }else{
        }

    }

    //选择食品
    const choosefood = [
        {
            label: '食品',
            type: 'Select',
            value: 'food',
            placeholder: '请选择',
            rules: [{ required: true, message: '请选择食品!' }],
            onChange: (option: any, input: any) => {
                setfood_id(option);
                if (option !== undefined) {
                    productionList.mutate({
                        foodId: option
                    });
                }
                console.log('option', option, input);
            },
            fields: [
                ...(productionquery?.data?.data || [])?.map((item: any, index: any) => {
                    const materialdata = {
                        value: item.food_id,
                        label: item.food_name,
                        food_number: item.food_number
                    };
                    return materialdata;
                })
            ]
        },
        {
            label: '生产批次',
            type: 'Select',
            value: 'foodbatch',
            placeholder: '请选择',
            name:'foodbatch',
            rules: [{ required: true, message: '请选择生产批次' }],
            fields: [
                ...(food_id
                    ? (productionList?.data?.data || []).map((item: any, index: any) => {
                          const materialdata = {
                              value: item.id,
                              label: item.production_batch
                          };
                          return materialdata;
                      })
                    : [])
            ]
        },
        {
            label: '入库数量',
            type: 'Input',
            value: 'Input',
            placeholder: '请输入',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[1-9]\d{0,9}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入入库数量！');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            callback('请输入正整数，并保持10字符以内');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            wide: 292
        }
    ];
    const addEmployeesConfig = {
        okText: '确定',
        title: '选择食品',
        visible: addModalVisible,
        setVisible: setAddModelVisible,
        okHandle: async () => {
            const values = await addEmployeesForm.validateFields();
            const values_2 = await SourceCodeForm.getFieldsValue();
            foodData.current = values;
            const arr = tablelist;
            let foodlist = null;
            Object.keys(values).forEach((key) => {
                console.log(values[key] == undefined);
                if (values[key] == undefined) {
                    console.log('123');
                    foodlist = 1;
                }
            });
            if (
                tablelist.length !== 0 &&
                tablelist.some(function (item: any) {
                    if (item.productionId === values.foodbatch) {
                        return true;
                    }
                })
            ) {
                message.error('您选的批次号已重复,请正确选择食品!');
            } else {
                if (foodlist === null) {
                    arr.push({
                        foodId: values.food,
                        foodNumber: mapToEnum_number[values.food],
                        foodName: mapToEnum_name[values.food],
                        productionId: values.foodbatch,
                        productionBatch: mapToEnum_batch[values.foodbatch],
                        count: values.Input
                    });
                    setAddModelVisible(false);
                    SourceCodeForm.setFieldsValue({
                        foodlist: 1
                    });
                }
            }
            settablelist(arr);
            // setAddModelVisible(false);
            addEmployeesForm.resetFields();
        },
        onCancelHandle: () => {
            setAddModelVisible(false);
            addEmployeesForm.resetFields();
        }
    };

    const Storingfood = [
        {
            title: '食品编号',
            dataIndex: 'foodNumber',
            key: 'foodNumber'
        },
        {
            title: '食品名称',
            dataIndex: 'foodName',
            key: 'foodName'
        },
        {
            title: '生产批次',
            dataIndex: 'productionBatch',
            key: 'productionBatch'
        },
        {
            title: '入库数量',
            dataIndex: 'count',
            key: 'count'
        },
        {
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle'>
                    <BaseButton
                        type='dashed'
                        className='warnBtn'
                        onClick={() => {
                            for (let i = 0; i < tablelist.length; i++) {
                                console.log('i', i, record.id);
                                if (i === record.id) {
                                    tablelist.splice(i, 1);
                                    const arr = tablelist;
                                    settablelist([...arr]);
                                }
                            }
                        }}
                    >
                        删除
                    </BaseButton>
                </Space>
            )
        }
    ];
    const tableData = tablelist.map((item: any, index: any) => ({
        foodNumber: item.foodNumber,
        foodName: item.foodName,
        productionBatch: item.productionBatch,
        count: item.count,
        id: index
    }));
    const onFinish_1 = async (values: any) => {
        console.log('foodData', values, SourceCodeForm.getFieldsValue());
        if (tablelist !== '') {
            const params: any = {
                inNumber: values.inNumber,
                inTime: values.inTime,
                storageLocation: values.storageLocation,
                foodList: tablelist.map((item: any) => {
                    const materialdata = {
                        foodId: item.foodId,
                        foodNumber: mapToEnum_number[item.foodId],
                        foodName: mapToEnum_name[item.foodId],
                        productionId: item.productionId,
                        productionBatch: mapToEnum_batch[item.productionId],
                        count: item.count
                    };
                    return materialdata;
                })
            };
            console.log('params888888888', JSON.stringify(params));
            const paramStr = JSON.stringify(params);
            signData(dispatch, JSON.stringify(params), (error, result: any) => {
                if (!error && result) {
                    params['signature'] = result;
                    params['paramStr'] = paramStr;
                    console.log('addfood.mutate(): ', error, result);
                    console.log('params7777777777', params);
                    addStoragein.mutate(params);
                } else if (error !== 'misprivatekey') {
                    message.info('签名异常，请重试或联系管理员');
                }
            });
        } else {
            message.error('提交错误,请正确选择食品');
        }
    };
    return (
        <div className='putinStorage'>
            <BaseCard title={<PageTitle title='入库信息填写' />}>
                <Form form={SourceCodeForm} onFinish={onFinish_1} className='edit-label-title'>
                    <PageTitle title='入库信息' type='primaryIcon' />
                    <FilterForm itemConfig={addConfigs} labelCol={6} wrapperCol={9} />
                    <Form form={FoodForm}>
                        <PageTitle title='入库食品' type='primaryIcon' />
                        <BaseTable
                            rowKey='account'
                            className='baseTable-title-nopadding'
                            btnDisplay={(checkData: any, resetSelect: any) => {
                                return (
                                    <TableHead
                                        LeftDom={<div></div>}
                                        RightDom={
                                            <div
                                                style={{
                                                    display: 'flex'
                                                }}
                                            ></div>
                                        }
                                    />
                                );
                            }}
                            columns={Storingfood}
                            dataSource={tableData}
                        />
                    </Form>
                    <div className='footerBtn'>
                        <Form.Item>
                            <Button className='submitBtn' type='primary' htmlType='submit'>
                                提交
                            </Button>
                            <Button
                                className='cancelBtn'
                                onClick={() => {
                                    navigate('/cpsjsr/putIn');
                                }}
                            >
                                取消
                            </Button>
                        </Form.Item>
                    </div>
                </Form>
            </BaseCard>
            <BaseModal {...addEmployeesConfig}>
                <Form name='addEmployeesForm' form={addEmployeesForm} onFieldsChange={onFieldsChange} className='edit-label-title'>
                    {<FilterForm itemConfig={choosefood} />}
                </Form>
            </BaseModal>
        </div>
    );
};
export default WithPaginate(PutInStorageAdd);


import request from "@services/request";
import { message } from "antd";
import JSEncrypt from "jsencrypt";

async function rsaDecrypte(encryptStr: string) {
  try {
    const decrypted = new JSEncrypt();
    if(sessionStorage.getRsaPubKey){
      decrypted.setPrivateKey(sessionStorage.getRsaPubKey);
    }else{
      const pKRet = await request({
        method: 'get',
        url: '/sys-config/getPubKey',
        data: {}
      });
      decrypted.setPrivateKey(pKRet?.data || '');
      sessionStorage.setItem('getRsaPubKey', pKRet?.data||'');
      console.log('pKRet?.data?.data',pKRet?.data?.data)
    }
    const decryptedWord = decrypted.decrypt(encryptStr);
    console.log('decryptedWord',decryptedWord)
    return decryptedWord || '';
  } catch (err) {
    message.destroy();
    message.error('请求解密失败');
    console.log(err);
    return Promise.reject();
  }
}

export default rsaDecrypte;

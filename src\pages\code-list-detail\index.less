.code-detail {
    .ant-descriptions-item-container {
        display: flex !important;
        align-items: center !important;
        height: 80px !important;
    }
}
.ellipsis-text {
    .ant-descriptions-item-content {
        a {
            width: 250px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}
// .ellipsis-text {
//     .ant-descriptions-item-content {
//         a:hover {
//             white-space: normal;
//             overflow: visible;
//         }
//     }
// }
.sourceContainer {
    .ant-descriptions-item-content {
        max-width: 80%;
    }
    .ant-image {
        // padding-top: 5px;
    }
}
.checkRecords {
    .ant-table-bordered {
        border: 1px solid #f0f0f0;
    }
    .ant-table-title {
        display: none;
    }
}

import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space, Card, DatePicker, Row, Col, Button } from 'antd';
import useUrlState from '@ahooksjs/use-url-state';

import { useMutation, useQuery } from 'react-query';
import dayjs from 'dayjs';
import { purchasePage, cancelPurchase, materialNameList } from '@services/purchase-controller';

import styles from './index.module.less';
import { useRef, useState } from 'react';
import PageTitle from '@components/page-title';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { useAccountList } from '../../myhooks/useaccountlist';
import copyToClipboard from 'copy-to-clipboard';
import BaseInput from '@components/base-input';
import SingleSearch from '@components/single-search';
import { Navigate, useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import { ReformChainError } from '@utils/errorCodeReform';
import { ColumnsType } from 'antd/lib/table';
import FilterForm from '@components/filter-form/filter-form';
import BaseDatePicker from '@components/base-date-picker';
import BaseSelect from '@components/base-select';
import { SearchOutlined, DownOutlined, UpOutlined, PlusOutlined,SyncOutlined } from '@ant-design/icons';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

const { RangePicker } = DatePicker;

interface IUrlState {
    pageIndex: number;
    pageSize: number;
}

const RawMaterialList = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;
    const [isSimpleSearch, setIsSimpleSearch] = useState(true);
    const [rawMaterialOptions, setRawMaterialOptions] = useState<any>();
    const navigate = useNavigate();
    const [addEmployeesForm] = Form.useForm();
    const [search]: any = Form.useForm();
    const queryListParam: any = useRef(null);

    // const queryListParam: any = useAccountList({
    //     pageIndex: pageInfo.pageIndex,
    //     pageSize: pageInfo.pageSize
    // });
    //作废
    const matermodiy = useMutation(cancelPurchase, {
        onSuccess(res) {
            message.success('作废成功');
            purchsequery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            purchsequery.refetch();
        }
    });
    //分页数据
    const purchsequery = useQuery(
        ['purchsequery', pageInfo],
        () => {
            return purchasePage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                state: queryListParam?.current?.state,
                materialId: queryListParam?.current?.materialId,
                purchaseBatch: queryListParam?.current?.purchaseBatch?.trim() || undefined,
                startTime: queryListParam?.current?.createTime
                    ? dayjs.utc(dayjs(queryListParam?.current?.createTime[0]).startOf('day')).format()
                    : undefined,
                endTime: queryListParam?.current?.createTime
                    ? dayjs.utc(dayjs(queryListParam?.current?.createTime[1]).endOf('day')).format()
                    : undefined
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    //原料数据
    const rawMaterialQuery = useQuery(
        ['rawMaterialQuery', pageInfo],
        () => {
            return materialNameList({
                valid: false
            });
        },
        {
            onSuccess(data) {
                console.log(data);
                setRawMaterialOptions({});
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );

    //列表数据
    const tableData = purchsequery?.data?.data?.records?.map((item: any) => ({
        purchaseBatch: item?.purchaseBatch,
        materialName: item?.materialName,
        createTime: dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss'),
        purchaseId: item?.id,
        status: item?.state,
        optName: item?.optName
    }));
    const listColumn: ColumnsType<any> = [
        {
            title: '原料采购批次',
            dataIndex: 'purchaseBatch',
            key: 'purchaseBatch',
            ellipsis: true
        },
        {
            title: '原料名',
            dataIndex: 'materialName',
            key: 'materialName',
            ellipsis: true
        },

        {
            title: '操作人',
            dataIndex: 'optName',
            key: 'optName',
            ellipsis: true
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            ellipsis: true,
            render: (data: any) => (
                <span style={{ color: data ? 'rgb(161, 158, 162)' : '#F666666' }}>
                    <Badge
                        status={data ? 'success' : 'error'}
                        color={data ? 'rgb(161, 158, 162)' : 'rgb(36, 171, 59)'}
                        text={data ? '已作废' : '可用'}
                    />
                </span>
            )
        },
        {
            width: 200,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle'>
                    <BaseButton
                        danger
                        type='dashed'
                        className='primaryBtn'
                        disabled={record.status}
                        // className={record.status ? 'warnBtn' : 'disableBtn'}
                        onClick={() => {
                            matermodiy.mutate({
                                id: record?.purchaseId
                            });
                        }}
                    >
                        作废
                    </BaseButton>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            navigate('/raw/rawlist/detail', {
                                state: {
                                    data: record
                                }
                            });
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    const searchConfig = [
        {
            label: '批次号',
            type: 'Input',
            value: 'name',
            placeholder: '请输入批次号',
            span: 8,
            className: 'find'
        },
        {
            label: '原料名',
            type: 'Select',
            value: 'data',
            placeholder: '请选择',
            span: 8,
            className: 'find',
            fields: [
                {
                    value: '1',
                    label: '禁用'
                },
                {
                    value: '0',
                    label: '可用'
                }
            ]
        },
        {
            label: '创建时间',
            type: 'Custom',
            value: 'data',
            placeholder: '请选择',
            span: 8,
            className: 'find',
            children: <RangePicker style={{ width: '100%' }} getPopupContainer={(trigger: any) => trigger.parentNode} />
        },
        {
            label: '状态',
            type: 'Select',
            value: 'data',
            placeholder: '请选择',
            span: 8,
            className: 'find',
            fields: [
                {
                    value: '1',
                    label: '禁用'
                },
                {
                    value: '0',
                    label: '可用'
                }
            ]
        }
    ];

    const searchFormItems = [
        <Form.Item label='批次号' name='purchaseBatch'>
            <BaseInput placeholder='请输入批次号'></BaseInput>
        </Form.Item>,
        <Form.Item label='原料名' name='materialId'>
            <BaseSelect
                placeholder='请选择'
                options={rawMaterialQuery?.data?.data?.map((item: any) => {
                    return {
                        label: item?.materialName,
                        value: item?.id
                    };
                })}
            ></BaseSelect>
        </Form.Item>,
        <Form.Item label='创建时间' name='createTime'>
            <BaseDatePicker style={{ width: '100%' }}></BaseDatePicker>
        </Form.Item>,
        <Form.Item label='状态' name='state'>
            <BaseSelect
                placeholder='请选择'
                options={[
                    { label: '正常', value: 0 },
                    { label: '作废', value: 1 }
                ]}
            ></BaseSelect>
        </Form.Item>
    ];

    return (
        <>
            <Card title={<PageTitle title='原料采购列表' />} style={{ marginBottom: 10 }}>
                <Form
                    labelAlign='left'
                    form={search}
                    labelCol={{ span: 5 }}
                    onFinish={(values) => {
                        handlePaginationChange(1);
                        console.log('values', values);
                        queryListParam.current = values;
                        purchsequery.refetch();
                    }}
                    className='label-title'
                >
                    <Row gutter={[36, 12]}>
                        {searchFormItems.slice(0, isSimpleSearch ? 2 : searchFormItems.length).map((searchFormItem) => (
                            <Col key={searchFormItem.key} span={8}>
                                {searchFormItem}
                            </Col>
                        ))}
                        <Col
                            span={
                                isSimpleSearch
                                    ? 8
                                    : searchFormItems.length % 3
                                    ? searchFormItems.length % 3 === 1
                                        ? 16
                                        : 8
                                    : 24
                            }
                        >
                            <div style={{ display: 'flex', justifyContent: 'end' }}>
                                <Space>
                                    <BaseButton
                                        htmlType='submit'
                                        type='primary'
                                        // className='searchBtn'
                                        style={{ width: 100 }}
                                        className={`${styles.searchBtn} ${styles.baseBtn}`}
                                        icon={<SearchOutlined rev={undefined} />}
                                    >
                                        查询
                                    </BaseButton>
                                    <BaseButton
                                        type='dashed'
                                        className='primaryBtn'
                                        style={{ width: 100 }}
                                        icon={<SyncOutlined rev={undefined} />}
                                        onClick={() => {
                                            queryListParam.current = null;
                                            purchsequery.refetch();
                                            search.resetFields();
                                        }}
                                    >
                                        重置
                                    </BaseButton>
                                    <BaseButton
                                        type='link'
                                        style={{ color: '#80a932' }}
                                        onClick={() => {
                                            setIsSimpleSearch(!isSimpleSearch);
                                        }}
                                    >
                                        {isSimpleSearch ? '展开' : '收起'}
                                        {isSimpleSearch ? (
                                            <DownOutlined rev={undefined} />
                                        ) : (
                                            <UpOutlined rev={undefined} />
                                        )}
                                    </BaseButton>
                                </Space>
                            </div>
                        </Col>
                    </Row>
                </Form>
                {/*</div>*/}
            </Card>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
            >
                <BaseTable
                    rowKey='account'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return (
                            <TableHead
                                LeftDom={<div></div>}
                                RightDom={
                                    <div
                                        style={{
                                            display: 'flex'
                                        }}
                                    >
                                        <BaseButton
                                            type='dashed'
                                            icon={<PlusOutlined rev={undefined} />}
                                            className='greenBtn'
                                            onClick={() => {
                                                navigate('/raw/rawlist/add');
                                            }}
                                        >
                                            新建批次
                                        </BaseButton>
                                    </div>
                                }
                            />
                        );
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={purchsequery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={purchsequery?.data?.data.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>
        </>
    );
};

export default WithPaginate(RawMaterialList);

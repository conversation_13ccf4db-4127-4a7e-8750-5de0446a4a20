/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-11-01 18:29:06
 * @LastEditTime: 2022-11-01 18:29:06
 * @LastEditors: PhilRandWu
 */
import request from '../request';
//员工接口
export const menuList = () => {
    return request({
        url: '/menu/getConfigMenuList',
        method: 'get',
        params: {}
    });
};
//菜单接口
export const getUserPermission = () => {
    return request({
        url: '/menu/getMenuList',
        method: 'get',
        params: {}
    });
};

// 查询菜单权限

export const getHasPermissions = (obj:any) => {
  return request({
      url: '/menu/hasPermissions',
      method: 'get',
      params: obj
  });
};

export const menuListGurable = (data:any) => {
   return request({
     // url: '/menu/getConfigurableMenuList',
     url: '/menu/getConfigurableMenuList',
     method: 'get',
     params: data
   });
  };

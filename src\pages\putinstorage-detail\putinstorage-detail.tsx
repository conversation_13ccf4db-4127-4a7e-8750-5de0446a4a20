import BaseCard from '@components/base-card';
import BaseCollapse from '@components/base-collapse';
import BaseFormItem from '@components/base-form-item';
import BaseModal from '@components/base-modal';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';

import { storageInDetail } from '@services/storage-in';
import dayjs from 'dayjs';
import TableHead from '@components/table-head';

import { Form, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { useLocation } from 'react-router-dom';
import FilterForm from '@components/filter-form/filter-form';
import ChainDetailModal from '@components/chain_detail_modal';

const PutInStorageDetail = () => {
    const { state } = useLocation();

    const [SourceForm] = Form.useForm();
    const [FoodForm] = Form.useForm();
    const [ChainForm] = Form.useForm();

    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);

    const storageIndetail = useQuery(
        ['storageIndetail'],
        () => {
            return storageInDetail({
                inWarehouseId: state?.data?.id
            });
        },
        {
            onSuccess(res) {
                console.log(res);
                SourceForm.setFieldsValue(res?.data);
            }
        }
    );

    useEffect(() => {}, []);

    const renderHouseType = (type: any) => {
        switch (type) {
            case 0:
                return '普通库';
            case 1:
                return '冷藏库';
            case 2:
                return '冷冻库';
            case 3:
                return '干燥库';

            default:
                break;
        }
    };

    const SourceInfoConfig = [
        {
            label: '入库单号',
            name: 'warehouseNumber',
            value: 'warehouseNumber',
            type: 'ShowText',
            span: 8
        },
        {
            label: '仓库名称',
            name: 'storehouse',
            value: 'storehouse',
            type: 'ShowText',
            span: 8
        },
        {
            label: '仓储地点',
            name: 'storehouseArea',
            value: 'storehouseArea',
            type: 'ShowText',
            span: 8
        },
        {
            label: '仓位',
            name: 'position',
            value: 'position',
            type: 'ShowText',
            span: 8
        },
        {
            label: '仓库类型',
            name: 'storehouseType',
            value: 'storehouseType',
            type: 'Custom',
            span: 8,
            children: <div>{renderHouseType(SourceForm.getFieldValue('storehouseType'))}</div>
        }
    ];

    const tableData = storageIndetail?.data?.data?.boxTo.map((item: any) => ({
        boxCode: item?.boxCode,
        productName: item?.productName
    }));

    const Storingfood = [
        {
            title: '箱码',
            dataIndex: 'boxCode',
            key: 'boxCode'
        },
        {
            title: '产品名称',
            dataIndex: 'productName',
            key: 'productName'
        }
    ];

    const ChainDetailModalConfig = {
        transactionId: storageIndetail?.data?.data?.transactionId,
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };

    const chainInfoConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            type: 'custom',
            span: 24,
            tooltip: '信息的链上的哈希值',
            children: (
                <a onClick={() => setChainDetailModalVisible(true)}>{storageIndetail?.data?.data?.transactionId}</a>
            )
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            type: 'Display',
            span: 24,
            tooltip: '信息上链的时间',
            displayDom: dayjs(storageIndetail?.data?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
        }
    ];

    return (
        <>
            <BaseCard title={<PageTitle title='入库单详情' />}>
                <Form form={SourceForm} className='edit-label-title'>
                    <PageTitle title='入库单信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={SourceInfoConfig} />
                </Form>
                <Form form={FoodForm}>
                    <PageTitle title='箱码信息' type='primaryIcon' />
                    <BaseTable
                        style={{ width: '50%' }}
                        rowKey='account'
                        className='baseTable-title-nopadding'
                        btnDisplay={(checkData: any, resetSelect: any) => {
                            return (
                                <TableHead
                                    LeftDom={<div></div>}
                                    RightDom={
                                        <div
                                            style={{
                                                display: 'flex'
                                            }}
                                        ></div>
                                    }
                                />
                            );
                        }}
                        columns={Storingfood}
                        dataSource={tableData}
                    />
                </Form>
                <Form form={ChainForm}>
                    <PageTitle title='区块链信息' type='primaryIcon' size={25} bmagin={16} />
                    <BaseFormItem configs={chainInfoConfig} />
                </Form>
            </BaseCard>
            <ChainDetailModal {...ChainDetailModalConfig} />
        </>
    );
};

export default PutInStorageDetail;

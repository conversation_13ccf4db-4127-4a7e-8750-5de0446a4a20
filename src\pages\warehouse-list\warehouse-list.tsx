/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-09 16:17:21
 * @LastEditTime: 2022-10-14 10:15:21
 * @LastEditors: PhilRandWu
 */
/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-10-09 16:16:29
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space, Button, Row, Col, DatePicker, Select, Input, Radio } from 'antd';

import { foodPage, FoodState } from '@services/food';
import useUrlState from '@ahooksjs/use-url-state';
import { useMutation, useQuery } from 'react-query';
import styles from './index.module.less';
import { useRef, useState } from 'react';
import PageTitle from '@components/page-title';
import { PlusOutlined, SyncOutlined, SearchOutlined, DownOutlined, UpOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { useFoodList } from '../../myhooks/usefoodlist';
import copyToClipboard from 'copy-to-clipboard';
import BaseInput from '@components/base-input';
import SingleSearch from '@components/single-search';
import { Navigate, useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import { ReformChainError } from '@utils/errorCodeReform';
import { ColumnsType } from 'antd/lib/table';
import BaseSelect from '@components/base-select';
import { landPlantPage, LandSourceService, LandSourceServiceUser, salesList, salesExport } from '@services/land-test';
import { scoreList, scoreExport, scoreReview } from '@services/score';
import { warehouseList, cancelWare } from '@services/ware';
import { decryptedUrl } from '@utils';
import FilterForm from '@components/filter-form/filter-form';
import BaseModal from '@components/base-modal';
import dayjs from 'dayjs';
import { useAppSelector } from '@store';
import utc from 'dayjs/plugin/utc';
import { RoleEnum } from '@config';
import PublicSourceService from '@services/traceability_data/public_source';
import { signData } from '../../utils/blockChainUtils';
import { useDispatch } from 'react-redux';

dayjs.extend(utc);
const { TextArea } = Input;
const FoodList = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;
    const navigate = useNavigate();
    const [addEmployeesForm] = Form.useForm();
    const [editEmployeesForm] = Form.useForm();
    const querylist = useRef<any>('');
    const [search]: any = Form.useForm();
    const dispatch = useDispatch();
    const queryList: any = useFoodList({
        pageIndex: pageInfo.pageIndex,
        pageSize: pageInfo.pageSize
    });
    const { RangePicker } = DatePicker;
    console.log('queryList', queryList);
    const [addOriginForm] = Form.useForm<any>();
    const [addModalVisible, setAddModelVisible] = useState(false);
    const [currentRecord, setCurrentRecord] = useState<any>(null);
    const [showProcessOpinion, setShowProcessOpinion] = useState(false);

    const userInfo = useAppSelector((store) => store.user);
    const LocalLoginIdentity = Number(userInfo?.userInfo?.identity);
    const [isSimpleSearch, setIsSimpleSearch] = useState(true);
    console.log([RoleEnum.销售企业], 'RoleEnum.平台方, RoleEnum.运营方,');

    //修改状态
    // const foodstate = useMutation(FoodState, {
    //     onSuccess(res) {
    //         message.success('修改状态成功');
    //         foodquery.refetch();
    //     },
    //     onError(err: any) {
    //         ReformChainError(err);
    //         foodquery.refetch();
    //     }
    // });
    //作废
    const matermodiy = useMutation(cancelWare, {
        onSuccess(res) {
            message.success('作废成功');
            foodquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            foodquery.refetch();
        }
    });
    const queryProductList = useQuery(['queryProductList'], () => PublicSourceService.getProductList());
    const queryProductListData: any[] = queryProductList?.data?.data;

    const foodquery = useQuery(
        ['foodquery', pageInfo],
        () => {
            // if (!userInfo.user?.organization_id) {
            //     message.error('未获取到机构id');
            //     return Promise.reject('未获取到机构id');
            // }
            console.log(querylist?.current?.startTime);
            return warehouseList({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                inboundNo: querylist?.current?.inboundNo, // 入库单号
                productId: querylist?.current?.productName, // 产品名称
                productionBatch: querylist?.current?.productionBatch, // 生产批次
                warehouseName: querylist?.current?.warehouseName, // 仓库名称
                startTime: querylist?.current?.inboundTime
                    ? dayjs.utc(dayjs(querylist?.current?.inboundTime[0]).startOf('day')).format()
                    : undefined,
                endTime: querylist?.current?.inboundTime
                    ? dayjs.utc(dayjs(querylist?.current?.inboundTime[1]).endOf('day')).format()
                    : undefined,
                state: querylist?.current?.status, // 处理状态
                orgId: querylist?.current?.orgId // 生产加工企业
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    console.log('staffquery', foodquery);

    // const queryCoreOrgList = useQuery(['queryCoreOrgList'], () => PublicSourceService.getCoreList(), {
    //     enabled: [RoleEnum.平台方, RoleEnum.运营方, RoleEnum.销售企业]?.includes(LocalLoginIdentity)
    // });
    // const queryCoreOrgListData: any[] = queryCoreOrgList?.data?.data;

    const tableData = foodquery?.data?.data?.records?.map((item: any) => ({
        ...item,
        inboundTime: dayjs(item.inboundTime).format('YYYY-MM-DD HH:mm:ss')
    }));

    // 处理接口
    const feekback = useMutation(scoreReview, {
        onSuccess(res) {
            sessionStorage.setItem('isFill', '1');
            setAddModelVisible(false);
            foodquery.refetch();
            message.success('处理成功');
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    // 根据角色显示生产加工企业列
    const coreEnterpriseColumns = () => {
        return [RoleEnum.平台方, RoleEnum.运营方, RoleEnum.销售企业]?.includes(LocalLoginIdentity)
            ? [
                  {
                      title: '生产加工企业',
                      dataIndex: 'orgName',
                      key: 'orgName',
                      ellipsis: true,
                      render: (_: any, row: any) => row.orgName || '-'
                  }
              ]
            : [];
    };

    // 根据角色显示经销商列
    const dealerColumns = () => {
        return [RoleEnum.平台方, RoleEnum.运营方, RoleEnum.生产加工企业]?.includes(LocalLoginIdentity)
            ? [
                  {
                      title: '经销商',
                      dataIndex: 'dealerName',
                      key: 'dealerName',
                      ellipsis: true,
                      render: (_: any, row: any) => row.dealerName || '-'
                  }
              ]
            : [];
    };

    // 根据角色显示状态列（仅平台方、运营方、销售企业显示）
    const statusColumns = () => {
        return [RoleEnum.平台方, RoleEnum.运营方, RoleEnum.销售企业]?.includes(LocalLoginIdentity)
            ? [
                  {
                      title: '状态',
                      dataIndex: 'state',
                      key: 'state',
                      ellipsis: true,
                      render: (_: any, row: any) => (
                          <span style={{ color: row.state == '0' ? 'rgb(36, 171, 59)' : '#BFBFBF' }}>
                              <Badge
                                  status={row.state == '0' ? 'success' : 'error'}
                                  color={row.state == '0' ? 'rgb(36, 171, 59)' : '#BFBFBF'}
                                  text={row.state == '0' ? '可用' : '已作废'}
                              />
                          </span>
                      )
                  }
              ]
            : [];
    };
    const listColumn: ColumnsType<any> = [
        {
            title: '入库单号',
            dataIndex: 'inboundNo',
            key: 'inboundNo',
            ellipsis: true
        },
        ...coreEnterpriseColumns(),

        {
            title: '产品名称',
            dataIndex: 'productName',
            key: 'productName',
            ellipsis: true
        },
        {
            title: '生产批次',
            dataIndex: 'productionBatch',
            key: 'productionBatch',
            ellipsis: true
        },
        ...dealerColumns(),

        {
            title: '仓库名称',
            dataIndex: 'warehouseName',
            key: 'warehouseName',
            ellipsis: true
        },

        {
            title: '操作人',
            dataIndex: 'optName',
            key: 'optName',
            ellipsis: true
        },
        {
            title: '入库时间',
            dataIndex: 'inboundTime',
            key: 'inboundTime',
            ellipsis: true
        },
        ...statusColumns(),
        // {
        //     title: '处理状态',
        //     dataIndex: 'status',
        //     key: 'status',
        //     ellipsis: true,
        //     render: (data: any) => (
        //         <span style={{ color: data == '3' ? 'rgb(36, 171, 59)' : data == '2' ? '#F59A23' : '#F64041' }}>
        //             <Badge
        //                 status={data == '3' ? 'success' : data == '2' ? 'processing' : 'error'}
        //                 color={data == '3' ? 'rgb(36, 171, 59)' : data == '2' ? '#F59A23' : '#F64041'}
        //                 text={data == '1' ? '待处理' : data == '2' ? '处理中' : '已处理'}
        //             />
        //         </span>
        //     )
        // },
        {
            width: 210,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle' className='operation'>
                    {/* 作废按钮 - 仅平台方、运营方、销售企业显示 */}
                    {[RoleEnum.平台方, RoleEnum.运营方, RoleEnum.销售企业]?.includes(LocalLoginIdentity) && (
                        <BaseButton
                            type='dashed'
                            className='warnBtn'
                            disabled={record.state != '0'} // 当状态不是'0'(可用)时禁用按钮
                            onClick={() => {
                                matermodiy.mutate({
                                    id: record?.id
                                });
                            }}
                        >
                            作废
                        </BaseButton>
                    )}
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            const feek = record?.id;
                            navigate('detail', {
                                state: {
                                    id: feek
                                }
                            });
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    // const searchConfig = {
    //     label: '',
    //     classname: 'searchConfig-input',
    //     handleSearch: (values: any) => {
    //         handlePaginationChange(1);
    //         foodquery.refetch();
    //     },
    //     placeholder: '输入产品编号/产品名称',
    //     setSearchValue: (values: any) => {
    //         // console.log("values",values)
    //         querylist.current = values;
    //     }
    // };
    const exportFile = async () => {
        // 导出函数
        if (tableData.length == 0) {
            return message.warning('当前暂无可导出数据');
        }
        try {
            const data: any = await scoreExport({
                inboundNo: querylist?.current?.inboundNo, // 入库单号
                productName: querylist?.current?.productName, // 产品名称
                productionBatch: querylist?.current?.productionBatch, // 生产批次
                warehouseName: querylist?.current?.warehouseName, // 仓库名称
                startTime: querylist?.current?.inboundTime
                    ? dayjs.utc(dayjs(querylist?.current?.inboundTime[0]).startOf('day')).format()
                    : undefined,
                endTime: querylist?.current?.inboundTime
                    ? dayjs.utc(dayjs(querylist?.current?.inboundTime[1]).endOf('day')).format()
                    : undefined,
                status: querylist?.current?.status, // 处理状态
                orgId: querylist?.current?.orgId // 生产加工企业
            });
            console.log(data);
            var link = document.createElement('a');
            link.href = URL.createObjectURL(data);
            link.setAttribute('download', '用户反馈信息.xlsx');
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (error: any) {
            if (error?.data?.message === '生成中，请稍后下载') {
                message.warning(error?.data?.message);
            } else {
                message.warning('没有操作权限');
            }
        }
    };

    const addOriginConfig = {
        okText: '确 定',
        cancelText: '取 消',
        title: '反馈处理',
        visible: addModalVisible,
        setVisible: setAddModelVisible,
        okHandle: async () => {
            try {
                const values = await addOriginForm.validateFields();
                console.log('表单数据:', values);

                // 如果是已处理状态，验证处理意见是否填写
                if (values.status === '3') {
                    if (!values.result || values.result.trim() === '') {
                        message.error('请输入处理意见');
                        return;
                    }
                }
                const params: any = {
                    id: values?.id,
                    result: values?.result?.trim(), // 去掉前后空格
                    status: values?.status
                };

                feekback.mutate({
                    id: currentRecord.id,
                    ratingFeedbackReviewVo: params
                    // paramStr: paramStr,
                    // signature: result
                });
                // console.log('params,params', params);
                const paramStr = JSON.stringify(params);
                // signData(dispatch, JSON.stringify(params), (error, result: any) => {
                //     if (!error && result) {
                //         feekback.mutate({
                //             id: currentRecord.id,
                //             ratingFeedbackReviewVo: params
                //             // paramStr: paramStr,
                //             // signature: result
                //         });
                //     } else if (error !== 'misprivatekey') {
                //         message.info('签名异常，请重试或联系管理员');
                //     }
                // });

                // setAddModelVisible(false);
                // foodquery.refetch();
            } catch (e) {
                console.error('表单验证失败', e);
            }
        },
        onCancelHandle: () => {
            setAddModelVisible(false);
            addOriginForm.resetFields();
        }
    };
    const addOriginConfigsColumns = () => {
        return [RoleEnum.平台方, RoleEnum.运营方]?.includes(LocalLoginIdentity)
            ? [
                  {
                      label: '反馈描述',
                      type: 'Custom',
                      value: 'systemFeedback',
                      rules: [{ required: false, message: '' }],
                      children: (
                          <div
                              style={{
                                  wordBreak: 'break-word',
                                  display: 'inline-block',
                                  whiteSpace: 'pre-wrap',
                                  maxHeight: '100px',
                                  overflowY: 'auto',
                                  width: '100%',
                                  padding: '2px 0px',
                                  borderRadius: '4px',
                                  marginTop: '3px',
                                  scrollbarWidth: 'none', // Firefox
                                  msOverflowStyle: 'none' // IE and Edge
                              }}
                              className='hide-scrollbar'
                              dangerouslySetInnerHTML={{
                                  __html: currentRecord?.systemFeedback || '-'
                              }}
                          />
                      )
                  }
              ]
            : [
                  {
                      label: '反馈描述',
                      type: 'Custom',
                      value: 'productFeedback',
                      rules: [{ required: false, message: '' }],
                      children: (
                          <div
                              style={{
                                  wordBreak: 'break-word',
                                  display: 'inline-block',
                                  whiteSpace: 'pre-wrap',
                                  maxHeight: '100px',
                                  overflowY: 'auto',
                                  width: '100%',
                                  padding: '2px 0px',
                                  borderRadius: '4px',
                                  marginTop: '3px',
                                  scrollbarWidth: 'none', // Firefox
                                  msOverflowStyle: 'none' // IE and Edge
                              }}
                              className='hide-scrollbar'
                              dangerouslySetInnerHTML={{
                                  __html: currentRecord?.productFeedback || '-'
                              }}
                          />
                      )
                  }
              ];
    };
    const addOriginConfigs = [
        ...addOriginConfigsColumns(),
        {
            label: '反馈时间',
            type: 'Custom',
            value: 'feedbackTime',
            rules: [{ required: false, message: '' }],
            children: <div>{currentRecord?.feedbackTime || '-'}</div>
        },
        {
            label: '处理结果',
            type: 'Custom',
            value: 'status',
            rules: [{ required: false, message: '' }],
            children: (
                <Form.Item
                    name='status'
                    rules={[
                        {
                            required: true,
                            message: '请选择处理结果'
                        }
                    ]}
                    style={{ marginBottom: '0px' }}
                >
                    <Radio.Group onChange={(e) => setShowProcessOpinion(e.target.value === '3')}>
                        <Radio value='1'>待处理</Radio>
                        <Radio value='2'>处理中</Radio>
                        <Radio value='3'>已处理</Radio>
                    </Radio.Group>
                </Form.Item>
            )
        },
        showProcessOpinion && {
            label: '处理意见',
            name: 'result',
            value: 'result',
            type: 'Custom',
            rules: [{ required: true, message: '' }],
            children: (
                <Form.Item
                    name='result'
                    rules={[
                        {
                            required: true,
                            message: ''
                        },
                        () => ({
                            validator: (_: any, value: any, callback: any) => {
                                if (!value) {
                                    callback('请输入处理意见!');
                                } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                                    callback('字段前后不能输入空格！');
                                } else {
                                    // 使用trim去掉前后空格
                                    const trimmedValue = value.trim();
                                    if (!trimmedValue) {
                                        callback('请输入处理意见!');
                                    } else {
                                        callback();
                                    }
                                }
                            }
                        })
                    ]}
                    style={{ marginBottom: '0px' }}
                >
                    <TextArea
                        showCount
                        maxLength={300}
                        className='summarytextinput'
                        placeholder='请输入处理意见'
                        style={{ height: 120, resize: 'none' }}
                        // onBlur={(e) => {
                        //     const target = e.target as HTMLTextAreaElement;
                        //     const originalValue = target.value;
                        //     // 只去掉首尾空格，保留中间的空格
                        //     const trimmedValue = originalValue.replace(/^\s+|\s+$/g, '');
                        //     if (originalValue !== trimmedValue) {
                        //         target.value = trimmedValue;
                        //         // 触发onChange事件以更新表单状态
                        //         const event = new Event('input', { bubbles: true });
                        //         target.dispatchEvent(event);
                        //     }
                        // }}
                    />
                </Form.Item>
            )
        }
    ].filter(Boolean);

    // 根据不同角色构建搜索表单项
    const searchFormItems = [
        // 入库单号 - 所有角色都显示
        <Form.Item label='入库单号' name='inboundNo'>
            <BaseInput placeholder='请输入入库单号'></BaseInput>
        </Form.Item>,

        // 产品名称 - 所有角色都显示
        <Form.Item label='产品名称' name='productName'>
            <BaseSelect
                placeholder='请选择'
                options={queryProductListData?.map((item) => ({
                    label: item?.productName,
                    value: item?.id
                }))}
            ></BaseSelect>
        </Form.Item>,

        // 生产批次 - 仅生产加工企业显示
        ...([RoleEnum.生产加工企业]?.includes(LocalLoginIdentity)
            ? [
                  <Form.Item label='生产批次' name='productionBatch'>
                      <BaseInput placeholder='请输入生产批次'></BaseInput>
                  </Form.Item>
              ]
            : []),

        // 仓库名称 - 所有角色都显示
        <Form.Item label='仓库名称' name='warehouseName'>
            <BaseInput placeholder='请输入仓库名称'></BaseInput>
        </Form.Item>,

        // 状态 - 平台方、运营方、销售企业显示
        ...([RoleEnum.平台方, RoleEnum.运营方, RoleEnum.销售企业]?.includes(LocalLoginIdentity)
            ? [
                  <Form.Item label='状态' name='status'>
                      <BaseSelect
                          placeholder='请选择'
                          options={[
                              { label: '可用', value: '0' },
                              { label: '已作废', value: '1' }
                          ]}
                      ></BaseSelect>
                  </Form.Item>
              ]
            : []),

        // 生产加工企业 - 平台方、运营方、销售企业显示
        // ...([RoleEnum.平台方, RoleEnum.运营方, RoleEnum.销售企业]?.includes(LocalLoginIdentity)
        //     ? [
        //           <Form.Item label='生产加工企业' name='orgId'>
        //               <BaseSelect
        //                   placeholder='请选择生产加工企业'
        //                   options={queryCoreOrgListData?.map((item) => ({
        //                       label: item?.shortName,
        //                       value: item?.id
        //                   }))}
        //               ></BaseSelect>
        //           </Form.Item>
        //       ]
        //     : []),

        // 入库时间 - 所有角色都显示
        <Form.Item label='入库时间' name='inboundTime'>
            <RangePicker style={{ width: '100%' }} />
        </Form.Item>
    ];
    return (
        <>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
                title={<PageTitle title='仓储管理列表' bg='container war' />}
            >
                <div
                // style={{
                //     display: 'flex',
                //     width: '100%'
                // }}
                >
                    {/* <SingleSearch {...searchConfig} /> */}
                    <Form
                        onFinish={(values) => {
                            handlePaginationChange(1);
                            console.log('values', values);
                            querylist.current = values;
                            // 恢复接口调用，确保点击查询按钮时能请求数据
                            foodquery.refetch();
                        }}
                        form={search}
                        labelCol={{ span: 6 }}
                        className='label-title'
                        labelAlign='left'
                    >
                        {/* <SearchForm /> */}
                        <Row gutter={[36, 12]}>
                            {searchFormItems
                                .slice(0, isSimpleSearch ? 2 : searchFormItems.length)
                                .map((searchFormItem) => (
                                    <Col key={searchFormItem.key} span={8}>
                                        {searchFormItem}
                                    </Col>
                                ))}
                            <Col span={isSimpleSearch ? 8 : 8}>
                                <div
                                    style={{
                                        display: 'flex',
                                        justifyContent: 'end'
                                    }}
                                >
                                    <Space>
                                        <BaseButton
                                            htmlType='submit'
                                            type='primary'
                                            className={`${styles.searchBtn} ${styles.baseBtn}`}
                                            icon={<SearchOutlined rev={undefined} />}
                                        >
                                            查询
                                        </BaseButton>
                                        <BaseButton
                                            type='dashed'
                                            className='primaryBtn'
                                            icon={<SyncOutlined rev={undefined} />}
                                            style={{ marginLeft: 8 }}
                                            onClick={() => {
                                                querylist.current = null;
                                                foodquery.refetch();
                                                search.resetFields();
                                            }}
                                        >
                                            重置
                                        </BaseButton>
                                        <BaseButton
                                            type='link'
                                            style={{ color: '#80a932' }}
                                            onClick={() => {
                                                setIsSimpleSearch(!isSimpleSearch);
                                            }}
                                        >
                                            {isSimpleSearch ? '展开' : '收起'}
                                            {isSimpleSearch ? (
                                                <DownOutlined rev={undefined} />
                                            ) : (
                                                <UpOutlined rev={undefined} />
                                            )}
                                        </BaseButton>
                                    </Space>
                                </div>
                            </Col>
                        </Row>
                    </Form>
                    {/* <div style={{ marginBottom: '15px', textAlign: 'right' }}>
                        <BaseButton
                            type='dashed'
                            className='primaryBtn'
                            // style={{width: 100}}
                            // icon={<SyncOutlined rev={undefined} />}
                            // onClick={() => {
                            //   // querylist.current = null;
                            //   // qualityquery.refetch();
                            //   foodquery.refetch();

                            //   search.resetFields();
                            // }}
                            onClick={exportFile}
                        >
                            导出
                        </BaseButton>
                    </div> */}
                </div>
                <BaseTable
                    rowKey='account'
                    className='food-table-operation'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return <TableHead />;
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={foodquery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={foodquery?.data?.data?.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
                <BaseModal {...addOriginConfig}>
                    <Form name='addOriginForm' form={addOriginForm} className='edit-label-title'>
                        {<FilterForm itemConfig={addOriginConfigs} />}
                    </Form>
                </BaseModal>
            </BaseCard>
        </>
    );
};

export default WithPaginate(FoodList);

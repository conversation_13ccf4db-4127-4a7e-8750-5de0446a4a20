import { useState } from 'react';
import { Col, Form, message, Row, Space } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';

import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';

import styles from './index.module.less';

interface IUrlState {
    pageIndex: number;
    pageSize: number;
    // ...
}

const mock = (params: Partial<IUrlState>) => {
    return new Promise((reslove, reject) => {
        setTimeout(() => {
            reslove([
                {
                    id: ********,
                    account: 'Tom1',
                    time: new Date(),
                    module: '入库管理',
                    type: '新建',
                    content: '编辑了员工A'
                },
                {
                    id: ********,
                    account: 'Tom2',
                    time: new Date(),
                    module: '入库管理',
                    type: '新建',
                    content: '编辑了员工A'
                },
                {
                    id: ********,
                    account: 'Tom3',
                    time: new Date(),
                    module: '入库管理',
                    type: '新建',
                    content: '编辑了员工A'
                },
                {
                    id: ********,
                    account: 'Tom4',
                    time: new Date(),
                    module: '入库管理',
                    type: '新建',
                    content: '编辑了员工A'
                },
                {
                    id: ********,
                    account: 'Tom5',
                    time: new Date(),
                    module: '入库管理',
                    type: '新建',
                    content: '编辑了员工A'
                }
            ]);
        }, 1000);
    });
};

const FleeWarningDetail = () => {
    const [queryParams, setQueryParams] = useState<Partial<IUrlState>>({ pageSize: 10, pageIndex: 1 });

    const navigate = useNavigate();

    const queryFleeWarningDetail = useQuery(['queryFleeWarningDetail', queryParams], () => mock(queryParams), {
        onSuccess() {},
        onError() {}
    });

    console.log(queryFleeWarningDetail, 'queryFleeWarningDetail queryFleeWarningDetail');

    const columns: ColumnsType<any> = [
        {
            title: '产品编号',
            dataIndex: 'id',
            key: 'id',
            ellipsis: true
        },
        {
            title: '产品名称',
            dataIndex: 'account',
            key: 'account',
            ellipsis: true
        },
        {
            title: '溯源码',
            dataIndex: 'module',
            key: 'module',
            ellipsis: true
        },
        {
            title: '经销商',
            dataIndex: 'module',
            key: 'module',
            ellipsis: true
        },
        {
            title: '经销商区域',
            dataIndex: 'module',
            key: 'module',
            ellipsis: true
        },
        {
            title: '首次扫描时间',
            dataIndex: 'time',
            key: 'time',
            ellipsis: true,
            render: (_, row) => dayjs(row.time).format('YYYY-MM-DD HH:mm:ss')
        },
        {
            title: '首次扫描位置',
            dataIndex: 'module',
            key: 'module',
            ellipsis: true
        }
    ];

    return (
        <BaseCard className={styles.coreFIrmContainer} title={<PageTitle title='窜货预警详情' />}>
            <BaseTable
                loading={queryFleeWarningDetail.isFetching}
                columns={columns}
                dataSource={(queryFleeWarningDetail.data as unknown[]) || []}
            />
            <BasePagination
                shouldShowTotal
                showQuickJumper
                showSizeChanger
                current={1}
                pageSize={10}
                total={100}
                onShowSizeChange={() => {}}
                onChange={() => {}}
            />
        </BaseCard>
    );
};

export default FleeWarningDetail;

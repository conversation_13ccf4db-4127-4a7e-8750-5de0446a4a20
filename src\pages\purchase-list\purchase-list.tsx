/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-09 16:17:21
 * @LastEditTime: 2022-10-14 10:15:21
 * @LastEditors: PhilRandWu
 */
/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-10-09 16:16:29
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space, Button, Row, Col, DatePicker } from 'antd';
import { foodPage, FoodState } from '@services/food';
import useUrlState from '@ahooksjs/use-url-state';
import { useMutation, useQuery } from 'react-query';
import styles from './index.module.less';
import { useRef, useState } from 'react';
import PageTitle from '@components/page-title';
import { SearchOutlined, PlusOutlined, SyncOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { useFoodList } from '../../myhooks/usefoodlist';
import copyToClipboard from 'copy-to-clipboard';
import BaseInput from '@components/base-input';
import SingleSearch from '@components/single-search';
import { Navigate, useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import { ReformChainError } from '@utils/errorCodeReform';
import { ColumnsType } from 'antd/lib/table';
import BaseSelect from '@components/base-select';
import { LandSourceServiceUser } from '@services/land-test';
import { purchasePage, purchaseUpdata, PurchaseBatchExists } from '@services/purchase';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
dayjs.extend(utc);
const FoodList = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;
    const navigate = useNavigate();
    const [addEmployeesForm] = Form.useForm();
    const [editEmployeesForm] = Form.useForm();
    const querylist = useRef<any>('');
    const [search]: any = Form.useForm();
    const queryList: any = useFoodList({
        pageIndex: pageInfo.pageIndex,
        pageSize: pageInfo.pageSize
    });
    const { RangePicker } = DatePicker;
    console.log('queryList', queryList);
    //修改状态
    const foodstate = useMutation(purchaseUpdata, {
        onSuccess(res) {
            message.success('修改状态成功');
            purchase.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            purchase.refetch();
        }
    });

    // 编辑

    const purchaseBatchExists = useMutation(PurchaseBatchExists, {
        onSuccess(res: any, variables) {
            console.log(res);
            if (res.code === 200) {
                const foodid = variables?.id;
                navigate('edit', {
                    state: {
                        id: foodid
                    }
                });
                purchase.refetch();
            } else {
                message.error(res.message);
            }
            // const foodid = record?.id;
            // navigate('edit', {
            //     state: {
            //         id: foodid
            //     }
            // });
        },
        onError(err: any) {
            ReformChainError(err);
            purchase.refetch();
        }
    });
    const purchase = useQuery(
        ['purchase', pageInfo],
        () => {
            // if (!userInfo.user?.organization_id) {
            //     message.error('未获取到机构id');
            //     return Promise.reject('未获取到机构id');
            // }
            return purchasePage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                startTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[0]).startOf('day')).format()
                    : undefined,
                endTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[1]).endOf('day')).format()
                    : undefined,
                purchaseBatch: querylist?.current?.purchaseBatch,
                farmerName: querylist?.current?.farmerName
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    console.log('staffquery', purchase);

    const landProductSeleUser = useQuery(['landProductSeleUser'], () => LandSourceServiceUser());

    const landProductSeleUserData: any[] = landProductSeleUser?.data?.data;
    console.log(landProductSeleUserData);
    // const tableData = purchase?.data?.data?.records;

    const tableData = purchase?.data?.data?.records?.map((item: any) => ({
        purchaseBatch: item?.purchaseBatch,
        plantName: item?.plantName,
        processName: item?.processName,
        id: item?.id,
        farmerName: item?.farmerName,
        purchaseWeight: item?.purchaseWeight,
        purchaseUnitPrice: item?.purchaseUnitPrice,
        userName: item.userName,
        purchaseTime: dayjs(item.purchaseTime).format('YYYY-MM-DD HH:mm:ss'),
        state: item.state,
        productionId: item.productionId
        // userName: item.userName
    }));

    const listColumn: ColumnsType<any> = [
        {
            title: '收购批次',
            dataIndex: 'purchaseBatch',
            key: 'no',
            ellipsis: true
        },
        {
            title: '农作物类型',
            dataIndex: 'plantName',
            key: 'plantName',
            ellipsis: true
        },
        {
            title: '农户姓名',
            dataIndex: 'farmerName',
            key: 'farmerName',
            ellipsis: true
        },
        {
            title: '收购时间',
            dataIndex: 'purchaseTime',
            key: 'purchaseTime',
            ellipsis: true
        },
        {
            title: '收购重量(吨)',
            dataIndex: 'purchaseWeight',
            key: 'purchaseWeight',
            ellipsis: true
        },
        {
            title: '收购单价(元/吨)',
            dataIndex: 'purchaseUnitPrice',
            key: 'purchaseUnitPrice',
            ellipsis: true
        },
        {
            title: '收购对接人',
            dataIndex: 'userName',
            key: 'userName',
            ellipsis: true
        },

        // {
        //     title: '产品信息',
        //     dataIndex: 'operation',
        //     key: 'operation',
        //     ellipsis: true,
        //     render: (data: any, record: any) => (
        //         <span
        //             className={styles.configBtn}
        //             onClick={() => {
        //                 navigate('infoConfig', {
        //                     state: {
        //                         id: record
        //                     }
        //                 });
        //             }}
        //         >
        //             配置
        //         </span>
        //     )
        // },
        // {
        //     title: '溯源码信息',
        //     dataIndex: 'operation',
        //     key: 'operation',
        //     ellipsis: true,
        //     render: (data: any, record: any) => (
        //         <span
        //             className={styles.configBtn}
        //             onClick={() => {
        //                 navigate('source', {
        //                     state: {
        //                         id: record?.id
        //                     }
        //                 });
        //             }}
        //         >
        //             配置
        //         </span>
        //     )
        // },
        {
            title: '状态',
            dataIndex: 'state',
            key: 'state',
            ellipsis: true,
            render: (data: any) => (
                <span style={{ color: data ? '#F64041' : '#666666' }}>
                    <Badge
                        status={data ? 'error' : 'success'}
                        color={data ? '#F64041' : 'rgb(36, 171, 59)'}
                        text={data ? '不可用' : '可用'}
                    />
                </span>
            )
        },
        {
            width: 280,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle' className='operation'>
                    <BaseButton
                        // type='dashed'
                        className={record.state ? 'primaryBtn' : 'warnBtn'}
                        // ghost
                        onClick={() => {
                            // console.log("record",record)
                            const opp = foodstate.mutate({
                                opt: record?.state ? 'ENABLE' : 'DISABLE',
                                id: record?.id
                            });
                            console.log(opp);
                        }}
                    >
                        {record.state ? '启用' : '禁用'}
                    </BaseButton>
                    {record.state ? (
                        ''
                    ) : (
                        <BaseButton
                            type='text'
                            className='editBtn'
                            onClick={() => {
                                purchaseBatchExists.mutate({
                                    purchaseBatch: record?.purchaseBatch,
                                    id: record?.id
                                });
                                // const foodid = record?.id;
                                // navigate('edit', {
                                //     state: {
                                //         id: foodid
                                //     }
                                // });
                            }}
                        >
                            编辑
                        </BaseButton>
                    )}

                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            const foodid = record?.id;
                            navigate('detail', {
                                state: {
                                    id: foodid,
                                    type: 'type'
                                }
                            });
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    const searchConfig = {
        label: '',
        classname: 'searchConfig-input',
        handleSearch: (values: any) => {
            handlePaginationChange(1);
            purchase.refetch();
        },
        placeholder: '输入产品编号/产品名称',
        setSearchValue: (values: any) => {
            // console.log("values",values)
            querylist.current = values;
        }
    };

    return (
        <>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
                title={<PageTitle title='收购管理列表' bg='container chan' />}
            >
                <div
                // style={{
                //     display: 'flex',
                //     width: '100%'
                // }}
                >
                    {/* <SingleSearch {...searchConfig} /> */}
                    <Form
                        onFinish={(values) => {
                            handlePaginationChange(1);
                            console.log('values', values);
                            querylist.current = values;
                            purchase.refetch();
                        }}
                        form={search}
                        labelCol={{ span: 6 }}
                        className='label-title'
                    >
                        {/* <SearchForm /> */}
                        <Row
                            gutter={[24, 0]}
                            style={{
                                width: '100%'
                            }}
                        >
                            {/* <Col span={6}>
                  <Form.Item label='生产批次' name='productionBatch'>
                      <BaseInput placeholder='请输入生产批次'></BaseInput>
                  </Form.Item>
              </Col> */}
                            {/* <Col span={6}>
                  <Form.Item label='创建时间' name='createTime'>
                      <BaseDatePicker style={{ width: '100%' }}></BaseDatePicker>
                  </Form.Item>
              </Col> */}
                            {/* <Col span={6}>
                  <Form.Item label='质检结果' name='inspectionResults'>
                      <BaseSelect
                          placeholder='请选择'
                          options={[
                              { label: '合格', value: 1 },
                              { label: '不合格', value: 2 }
                          ]}
                      ></BaseSelect>
                  </Form.Item>
              </Col> */}
                            <Col span={6}>
                                <Form.Item label='收购批次' name='purchaseBatch'>
                                    <BaseInput placeholder='请输入收购批次'></BaseInput>
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <Form.Item label='收购时间' name='createTime'>
                                    <RangePicker
                                        style={{ width: '100%' }}
                                        getPopupContainer={(trigger: any) => trigger.parentNode}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <Form.Item label='农户姓名' name='farmerName'>
                                    <BaseInput placeholder='请输入农户姓名'></BaseInput>
                                </Form.Item>
                            </Col>
                            <Col span={4}>
                                <div
                                    style={{
                                        display: 'flex',
                                        justifyContent: 'end',
                                        marginLeft: 10
                                    }}
                                >
                                    <Space>
                                        <BaseButton
                                            htmlType='submit'
                                            type='primary'
                                            // className='searchBtn'
                                            // style={{width: 100}}
                                            className={`${styles.searchBtn} ${styles.baseBtn}`}
                                            icon={<SearchOutlined rev={undefined} />}
                                        >
                                            查询
                                        </BaseButton>
                                        <BaseButton
                                            type='dashed'
                                            className='primaryBtn'
                                            // style={{width: 100}}
                                            icon={<SyncOutlined rev={undefined} />}
                                            onClick={() => {
                                                querylist.current = null;
                                                purchase.refetch();
                                                search.resetFields();
                                            }}
                                        >
                                            重置
                                        </BaseButton>
                                        {/* <BaseButton
                              type='dashed'
                              icon={<PlusOutlined rev={undefined} />}
                              className='greenBtn'
                              onClick={() => {
                                  navigate('/qcqa/list/add');
                              }}
                          >
                              新建质检
                          </BaseButton> */}
                                    </Space>
                                </div>
                            </Col>
                            <Col span={2}>
                                <div style={{ textAlign: 'right' }}>
                                    <BaseButton
                                        type='dashed'
                                        icon={<PlusOutlined rev={undefined} />}
                                        className='bgBtn'
                                        onClick={() => {
                                            navigate('add');
                                        }}
                                    >
                                        新建收购
                                    </BaseButton>
                                </div>
                            </Col>
                        </Row>
                    </Form>
                </div>
                <BaseTable
                    rowKey='account'
                    className='food-table-operation'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return <TableHead />;
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={purchase?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={purchase?.data?.data?.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>
        </>
    );
};

export default WithPaginate(FoodList);

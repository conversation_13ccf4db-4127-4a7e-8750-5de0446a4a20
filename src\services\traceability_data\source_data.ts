import request from '../request';

export interface ISourceQuery {
    coreId: number,
    endTime: string,
    pageIndex: number,
    pageSize: number,
    productId: number,
    productionBatch: string,
    startTime: string,
    state: number
}

export enum SourceStateEnum {
    "可用",
    "已作废",
    "已入库（本来生活）"
}

export default class SourceDataService {
    public static async Query(data: ISourceQuery) {
        return request({
            url: '/traceData/productionPage',
            method: 'post',
            data
        });
    };

    public static async detail(id: number) {
        return request({
            url: '/traceData/productionDetail',
            method: 'get',
            params: {id}
        });
    }

    // 种植溯源

    public static async landList(data: ISourceQuery) {
      return request({
          url: '/production/landPlantPage',
          method: 'post',
          data
      });
  }
// 种植溯源详情

  public static async batchInfo(id: number) {
    return request({
        url: '/production/landPlantBatchInfo',
        method: 'get',
        params: {id}
    });
}
}



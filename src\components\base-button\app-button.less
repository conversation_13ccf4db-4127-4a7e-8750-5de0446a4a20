.AppButtonWrapper {
    .ant-btn {
        border-radius: 4px;
    }
    // .ant-btn:not(:disabled) {
    //     border: 1px solid #3d73ef;
    //     color: #3d73ef;
    // }
    //   .primaryBtn{
    //     border: 1px solid #fa5151;
    //     color: #fa5151;
    // }
    // .ant-btn-primary{
    //   color: #fa5151 ;
    // }
    .primaryBtn:hover {
        border: 1px solid #15ad31;
        color: #15ad31;
    }
    .primaryBtn:focus {
        border: 1px solid #fa5151;
        color: #fa5151;
    }
    .warnBtn:focus {
        border: 1px solid #fa5151;
        color: #fa5151;
        background: #faeae9;
    }
    .greenBtn:focus {
        border: 1px solid #15ad31;
        color: #15ad31;
    }
    .ant-btn-sm {
        height: 27px;
        padding: 0 10px;
    }
    .ant-btn-primary:not(:disabled) {
        color: white;
        background-color: #3d73ef;
    }
    .ant-btn-dangerous:not(:disabled) {
        border: 1px solid #fa5151;
        color: #fa5151;
    }
    .ant-btn-link:not(:disabled) {
        border-color: transparent;
        padding: 0;
        color: #3d73ef;
        line-height: 1;
        height: auto;
    }
}
.AddButtonWrapper {
    .ant-btn {
        border-radius: 4px;
    }
}
.mychange {
    margin-left: 65px;
}

import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Card, Form, message, Space, Row, Col, DatePicker } from 'antd';
import useUrlState from '@ahooksjs/use-url-state';

import { landPage, LandSourceService, LandSourceServiceUser } from '@services/land-test';
import { useMutation, useQuery } from 'react-query';
import dayjs from 'dayjs';
import { ReformChainError } from '@utils/errorCodeReform';
import { ColumnsType } from 'antd/lib/table';

import styles from './index.module.less';
import { useRef, useState } from 'react';
import PageTitle from '@components/page-title';
import { SearchOutlined, PlusOutlined, SyncOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { useAccountList } from '../../myhooks/useaccountlist';
import SingleSearch from '@components/single-search';
import { useNavigate } from 'react-router-dom';
import BaseModal from '@components/base-modal';
import FilterForm from '@components/filter-form';
import WithPaginate from '../../hoc/withpaginate';
import BaseInput from '@components/base-input';
import BaseDatePicker from '@components/base-date-picker';
import BaseSelect from '@components/base-select';
import PublicSourceService from '@services/traceability_data/public_source';

import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

const { RangePicker } = DatePicker;

interface IUrlState {
    pageIndex: number;
    pageSize: number;
}

const QcqaList = (props: any) => {
    console.log(props);
    const { pageInfo, handlePaginationChange } = props;
    const navigate = useNavigate();
    const querylist: any = useRef('');
    const [search]: any = Form.useForm();
    //分页数据
    const qualityquery = useQuery(
        ['qualityquery', pageInfo],
        () => {
            return landPage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                farmerId: Number(querylist?.current?.farmerName),
                landId: querylist?.current?.landName
                // productionBatch: querylist?.current?.landName?.trim() || undefined
                // startTime: querylist?.current?.createTime
                //     ? dayjs.utc(dayjs(querylist?.current?.createTime[0]).startOf('day')).format()
                //     : undefined,
                // endTime: querylist?.current?.createTime
                //     ? dayjs.utc(dayjs(querylist?.current?.createTime[1]).endOf('day')).format()
                //     : undefined
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    // 获取地块名
    const landProductSele = useQuery(['landProductSele'], () => LandSourceService());

    const landProductListData: any[] = landProductSele?.data?.data;

    const landProductSeleUser = useQuery(['landProductSeleUser'], () => LandSourceServiceUser());

    const landProductSeleUserData: any[] = landProductSeleUser?.data?.data;

    //列表数据
    const tableData = qualityquery?.data?.data?.records?.map((item: any) => ({
        landName: item.landName,
        productionBatch: item.productionBatch,
        latestFarmingTime: item.latestFarmingTime ? dayjs(item.latestFarmingTime).format('YYYY-MM-DD HH:mm:ss') : '',
        latestTypeName: item.latestTypeName === 'plant' ? '播种' : item.latestTypeName === 'harvest' ? '收割' : '',
        landId: item.landId,
        latestPlantName: item.latestPlantName, //质检id
        farmerName: item.farmerName, //食品id
        testRecord: item.testRecord //质检内容
    }));

    const listColumn: ColumnsType<any> = [
        {
            title: '地块名称',
            dataIndex: 'landName',
            key: 'landName',
            ellipsis: true
        },
        {
            title: '农户名称',
            dataIndex: 'farmerName',
            key: 'farmerName',
            ellipsis: true
        },
        {
            title: '农作物类型',
            dataIndex: 'latestPlantName',
            key: 'latestPlantName',
            ellipsis: true
        },
        {
            title: '最近农事作业类型',
            dataIndex: 'latestTypeName',
            key: 'latestTypeName',
            ellipsis: true
        },

        {
            title: '最新农事作业时间',
            dataIndex: 'latestFarmingTime',
            key: 'latestFarmingTime',
            ellipsis: true
        },
        {
            width: 120,
            title: '操作',
            // dataIndex: 'operation',
            // key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle'>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => navigate(`detail/${record?.landId}`)}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    return (
        <>
            <BaseCard
                title={<PageTitle title='种植管理列表' bg='container zhong' />}
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
            >
                <div
                    // className="searchContainer"
                    className={styles.searchContainer}
                >
                    <div style={{ textAlign: 'right' }} className={styles.searchContainerBtn}>
                        <BaseButton
                            type='primary'
                            // className='searchBtn'
                            // style={{width: 100}}
                            className={`${styles.searchBtn} ${styles.baseBtn}`}
                            // icon={<SearchOutlined rev={undefined}/>}
                        >
                            种植管理系统
                        </BaseButton>
                    </div>

                    <Form
                        onFinish={(values) => {
                            handlePaginationChange(1);
                            console.log('values', values);
                            querylist.current = values;
                            qualityquery.refetch();
                        }}
                        form={search}
                        labelCol={{ span: 6 }}
                        className='label-title'
                    >
                        {/* <SearchForm /> */}
                        <Row
                            gutter={[24, 0]}
                            style={{
                                width: '100%'
                            }}
                        >
                            {/* <Col span={6}>
                                <Form.Item label='生产批次' name='productionBatch'>
                                    <BaseInput placeholder='请输入生产批次'></BaseInput>
                                </Form.Item>
                            </Col> */}
                            {/* <Col span={6}>
                                <Form.Item label='创建时间' name='createTime'>
                                    <BaseDatePicker style={{ width: '100%' }}></BaseDatePicker>
                                </Form.Item>
                            </Col> */}
                            {/* <Col span={6}>
                                <Form.Item label='质检结果' name='inspectionResults'>
                                    <BaseSelect
                                        placeholder='请选择'
                                        options={[
                                            { label: '合格', value: 1 },
                                            { label: '不合格', value: 2 }
                                        ]}
                                    ></BaseSelect>
                                </Form.Item>
                            </Col> */}
                            <Col span={6}>
                                <Form.Item label='地块名称' name='landName'>
                                    <BaseSelect
                                        placeholder='请选择地块名称'
                                        options={landProductListData?.map((item) => ({
                                            label: item?.landName,
                                            value: item?.landId
                                        }))}
                                    ></BaseSelect>
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <Form.Item label='农户名称' name='farmerName'>
                                    <BaseSelect
                                        placeholder='请选择农户名称'
                                        options={landProductSeleUserData?.map((item) => ({
                                            label: item?.farmerName,
                                            value: item?.farmerId
                                        }))}
                                    ></BaseSelect>
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <div style={{ display: 'flex', justifyContent: 'end', marginLeft: 10 }}>
                                    <Space>
                                        <BaseButton
                                            htmlType='submit'
                                            type='primary'
                                            // className='searchBtn'
                                            // style={{width: 100}}
                                            className={`${styles.searchBtn} ${styles.baseBtn}`}
                                            icon={<SearchOutlined rev={undefined} />}
                                        >
                                            查询
                                        </BaseButton>
                                        <BaseButton
                                            type='dashed'
                                            className='primaryBtn'
                                            // style={{width: 100}}
                                            icon={<SyncOutlined rev={undefined} />}
                                            onClick={() => {
                                                querylist.current = null;
                                                qualityquery.refetch();
                                                search.resetFields();
                                            }}
                                        >
                                            重置
                                        </BaseButton>
                                        {/* <BaseButton
                                            type='dashed'
                                            icon={<PlusOutlined rev={undefined} />}
                                            className='greenBtn'
                                            onClick={() => {
                                                navigate('/qcqa/list/add');
                                            }}
                                        >
                                            新建质检
                                        </BaseButton> */}
                                    </Space>
                                </div>
                            </Col>
                        </Row>
                    </Form>
                </div>
                <BaseTable
                    rowKey='account'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return <TableHead />;
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={qualityquery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={qualityquery?.data?.data.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>
        </>
    );
};

export default WithPaginate(QcqaList);

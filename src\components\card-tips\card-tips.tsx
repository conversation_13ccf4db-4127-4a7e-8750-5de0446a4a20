/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-12 17:17:03
 * @LastEditTime: 2022-11-01 18:38:04
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import { Divider } from 'antd';
import React from 'react';
import './index.less';
import { CaretUpOutlined } from '@ant-design/icons';
import Top from '../../assets/icon/top.png';
interface tipsConfigInterface {
    title: string;
    num: number;
    monthDec: string;
    monthNum: number;
    dayDes: string;
    dayNum: number;
    className?: string;
    unit?: string; // 添加单位属性
}

const CardTips = ({ title, num, monthDec, monthNum, dayDes, dayNum, className, unit }: tipsConfigInterface) => {
    // 获取单位，优先使用传入的unit，否则根据className判断
    const getUnit = () => {
        if (unit) return unit;
        return className != 'downCardGoods' ? '次' : '批次';
    };

    // 根据数字大小动态调整字体大小
    const getFontSize = (number: number) => {
        const numStr = String(number || 0);
        const length = numStr.length;

        if (length <= 4) return '30px'; // 4位数及以下，保持原始大小
        if (length <= 6) return '26px'; // 5-6位数，稍微缩小
        if (length <= 10) return '22px'; // 7-8位数，进一步缩小
        return '20px'; // 9位数及以上，最小字体
    };
    return (
        <BaseCard className={`cardTips ${className}`}>
            <div className='columnTitleIcon'>{title}</div>
            <div className='titleNum fsz' style={{ fontSize: getFontSize(num) }}>
                {num || '0'}
                <span style={{ fontSize: '14px' }}>{getUnit()}</span>
            </div>
            <div>
                <div className='columnAddIcon'>
                    {monthDec || '0'} <img src={Top} className='increaseIcon' alt='' />
                    {/* <CaretUpOutlined className='increaseIcon' rev={undefined} /> */}
                </div>

                <div className='monthNum fsz' style={{ fontSize: getFontSize(monthNum) }}>
                    {monthNum || '0'}
                    <span style={{ fontSize: '14px' }}>{getUnit()}</span>
                </div>
            </div>
            <Divider />
            {className != 'downCardGoods' ? (
                <div>
                    <div className='columnAddIcon'>
                        {dayDes || '0'}
                        <img src={Top} className='increaseIcon' alt='' />
                        {/* <CaretUpOutlined className='increaseIcon' rev={undefined} /> */}
                    </div>
                    <div className='dayNum fsz' style={{ fontSize: getFontSize(dayNum) }}>
                        {dayNum || '0'}
                        <span style={{ fontSize: '14px', fontWeight: 600 }}>{getUnit()}</span>
                    </div>
                </div>
            ) : (
                ''
            )}
        </BaseCard>
        //     <BaseCard className={`cardTips ${className}`}>
        //     <div className='columnTitleIcon'>{title}</div>
        //     <div className='titleNum'>{num || "-"}</div>
        //     <div>
        //         <span className='monthDes'>{monthDec || "-"}</span>
        //         <CaretUpOutlined className='increaseIcon' rev={undefined}/>
        //         <span className='monthNum'>{monthNum || "-"}</span>
        //     </div>
        //     <Divider/>
        //     <div>
        //         <span className='dayDes'>{dayDes || "-"}</span>
        //         <span className='dayNum'>{dayNum || "-"}</span>
        //     </div>
        // </BaseCard>
    );
};

export default CardTips;

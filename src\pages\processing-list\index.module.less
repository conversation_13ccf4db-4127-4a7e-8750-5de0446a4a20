.searchContainer {
    display: flex;
    flex-direction: row;
    .ant-card-body {
        padding-top: 0px;
    }
    .ant-form-item {
        width: 200px !important;
        // .ant-picker.ant-picker-range {
        //     width: 200px;
        // }
    }
    .searchBtn {
        margin-right: 5px;
    }
    .baseBtn {
        background: @blueBtn;
    }
    :global {
        .ant-form-item {
            margin-bottom: 20px;
        }
    }
}

.coreFIrmContainer {
    .ant-table-title {
        padding: 0;
    }
    :global {
        .ant-table-title {
            padding-top: 0;
        }
    }
}

.resetContainer {
    display: flex;
    align-items: center;
}

.btn {
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    color: #909090;
    background: #f7f8fa;
    word-break: keep-all;
}

.disable {
    cursor: not-allowed;
}
.newPswBtn {
    margin: 0 8px 0 10px;
}

.active {
    color: #fff;
    background: @primary-color;
}

.pswInput {
    background: #f7f8fa;
    cursor: not-allowed;
}

.configBtn {
    color: @primary-color;
}

.warnBtn {
    color: @redBtn;
}

.primaryBtn {
    color: @primary-color;
}
.titleP {
    font-weight: 600;
    font-size: 18px;
    margin: 0 0 15px 0;
    position: relative; /* 确保伪元素相对于此元素定位 */
}
.titleP::before {
    content: ''; /* 必须设置内容，即使为空 */
    display: inline-block;
    width: 4px; /* 竖杠的宽度 */
    height: 20px; /* 竖杠的高度，这里设置为 100% 表示与文本高度相同 */
    background-color: #80a932; /* 竖杠的颜色 */
    margin-right: 10px; /* 竖杠与文本之间的间距 */
    vertical-align: middle; /* 垂直居中对齐 */
}

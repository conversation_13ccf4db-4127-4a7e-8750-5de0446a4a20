/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-09-21 11:35:57
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Button, Form } from 'antd';
import useUrlState from '@ahooksjs/use-url-state';

import { useQuery } from 'react-query';
import './index.less';
import { listColumn } from './config';
// import { requestMock } from './mock';
import SearchForm from '@components/search-form';
import WithPaginate from '../../hoc/withpaginate';
import { ReformChainError } from '@utils/errorCodeReform';
const AuditList = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;

    // const queryList: any = useQuery(['queryList', pageInfo.pageIndex, pageInfo.pageSize], () => requestMock(), {
    //     onError(err: any) {
    //         ReformChainError(err);
    //         console.log(err);
    //     },
    //     retry: false
    // });

    // const listData = queryList.data;
    // console.log('queryList', queryList);

    return (
        <BaseCard className='tab-card-wrap' mt24>
            {/* <BaseTable
                rowKey='account'
                btnDisplay={(checkData: any, resetSelect: any) => {
                    return (
                        <div className='searchContainer'>
                            <Form layout='inline' labelAlign='left'>
                                <SearchForm />
                            </Form>
                            <Button type='primary' className='searchBtn' onClick={() => {}}>
                                查询
                            </Button>
                            <Button type='dashed' onClick={() => {}}>
                                重置
                            </Button>
                        </div>
                    );
                }}
                columns={listColumn}
                dataSource={listData?.data}
                loading={queryList?.isLoading}
            />
            <BasePagination
                shouldShowTotal
                showQuickJumper
                showSizeChanger
                current={pageInfo.pageIndex}
                pageSize={pageInfo.pageSize}
                total={100}
                onShowSizeChange={handlePaginationChange}
                onChange={handlePaginationChange}
            /> */}
        </BaseCard>
    );
};

export default WithPaginate(AuditList);

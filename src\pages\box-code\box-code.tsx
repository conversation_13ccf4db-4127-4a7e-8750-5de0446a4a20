import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space, Card, DatePicker, Row, Col, Modal, Table } from 'antd';
import useUrlState from '@ahooksjs/use-url-state';

import { useMutation, useQuery } from 'react-query';
import dayjs from 'dayjs';
import { purchasePage, cancelPurchase } from '@services/purchase-controller';

import styles from './index.module.less';
import { useRef, useState } from 'react';
import PageTitle from '@components/page-title';
import { SearchOutlined, PlusOutlined, SyncOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { useAccountList } from '../../myhooks/useaccountlist';
import copyToClipboard from 'copy-to-clipboard';
import BaseInput from '@components/base-input';
import SingleSearch from '@components/single-search';
import { Navigate, useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import { ReformChainError } from '@utils/errorCodeReform';
import { ColumnsType } from 'antd/lib/table';
import FilterForm from '@components/filter-form/filter-form';
import BaseDatePicker from '@components/base-date-picker';
import BaseSelect from '@components/base-select';
import BaseModal from '@components/base-modal';
import { SelectTable } from '@components/base-table/base-table';

import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { renderStatusBadge } from './config';
import { boxCodePage, cancelBox, addBox, getBoxCodeDetails, downloadBoxCode } from '@services/box-code';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

const { RangePicker } = DatePicker;

interface IUrlState {
    pageIndex: number;
    pageSize: number;
}

const RawMaterialList = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;
    const navigate = useNavigate();
    const [addForm] = Form.useForm();
    const [search]: any = Form.useForm();
    const querylist: any = useRef('');
    const [modalVisible, setmodalVisible] = useState(false);
    const [more, setMore] = useState(false);
    const [checkId, setCheckId] = useState<any[]>([]);
    const [checkData, setCheckData] = useState([]);

    //作废
    const matermodiy = useMutation(cancelBox, {
        onSuccess(res) {
            message.success('作废成功');
            boxCodeQuery.refetch();
        },
        onError(err: any) {
            console.log(err, 'eee');
            if (err?.data?.code === 5001090002) {
                Modal.confirm({
                    title: '确定要禁用该箱码吗？',
                    okText: '确定',
                    cancelText: '取消',
                    icon: null,
                    content: <div>该箱码已有入库记录，作废后溯源码将与该箱码解绑</div>,
                    onOk() {
                        matermodiy.mutate({
                            id: JSON.parse(err?.config?.data)?.id,
                            confirm: true
                        });
                    },
                    onCancel() {}
                });
                return;
            }
            ReformChainError(err);
            boxCodeQuery.refetch();
        }
    });
    // 新建箱码
    const addBoxCode = useMutation(addBox, {
        onSuccess(res) {
            message.success('新建箱码成功');
            boxCodeQuery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            boxCodeQuery.refetch();
        }
    });
    //分页数据
    const boxCodeQuery = useQuery(
        ['boxCodeQuery', pageInfo],
        () => {
            return boxCodePage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                state: querylist?.current?.state,
                boxCode: querylist?.current?.boxCode?.trim() || undefined,
                traceCode: querylist?.current?.traceCode?.trim() || undefined,
                startTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[0]).startOf('day')).format()
                    : undefined,
                endTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[1]).endOf('day')).format()
                    : undefined
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );

    //列表数据
    const tableData = boxCodeQuery?.data?.data?.records?.map((item: any) => ({
        activateName: item?.activateName || '-',
        activateTime: item?.activateTime,
        createTime: item?.createTime || '-',
        boxCode: item?.boxCode || '-',
        optName: item?.optName || '-',
        id: item?.id || '-',
        productName: item?.productName || '-',
        state: item?.state
    }));

    const detailsColumns = [
        {
            title: '产品名',
            dataIndex: 'productName',
            key: 'productName'
        },
        {
            title: '溯源码',
            dataIndex: 'code',
            key: 'code'
        }
    ];

    const listColumn: ColumnsType<any> = [
        {
            title: '箱码',
            dataIndex: 'boxCode',
            key: 'boxCode',
            ellipsis: true
        },
        {
            title: '产品名称',
            dataIndex: 'productName',
            key: 'productName',
            ellipsis: true
        },

        {
            title: '创建人',
            dataIndex: 'optName',
            key: 'optName',
            ellipsis: true
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true,
            render: (_, row) => (row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-')
        },
        {
            title: '激活人',
            dataIndex: 'activateName',
            key: 'activateName',
            ellipsis: true
        },
        {
            title: '激活时间',
            dataIndex: 'activateTime',
            key: 'activateTime',
            ellipsis: true,
            render: (_, row) => (row.activateTime ? dayjs(row.activateTime).format('YYYY-MM-DD HH:mm:ss') : '-')
        },
        {
            title: '状态',
            dataIndex: 'state',
            key: 'state',
            ellipsis: true,
            render: (data: any) => renderStatusBadge(data as number)
        },
        {
            width: 200,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle'>
                    <BaseButton
                        danger
                        type='dashed'
                        className='primaryBtn'
                        disabled={record.state !== 1}
                        // className={record.status ? 'warnBtn' : 'disableBtn'}
                        onClick={() => {
                            matermodiy.mutate({
                                id: record?.id,
                                confirm: false
                            });
                        }}
                    >
                        作废
                    </BaseButton>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={async () => {
                            try {
                                const details = await getBoxCodeDetails({
                                    id: record?.id
                                });
                                Modal.confirm({
                                    title: '查看详情',
                                    okText: '确定',
                                    icon: null,
                                    content: (
                                        <div>
                                            <Table
                                                dataSource={details?.data?.traceCodeList}
                                                columns={detailsColumns}
                                                pagination={false}
                                            />
                                        </div>
                                    ),
                                    onOk() {},
                                    onCancel() {}
                                });
                            } catch (error: any) {
                                console.log(error);
                                if (error?.data?.code === 5001090004) {
                                    message.error('箱码未激活');
                                }
                            }
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    const addConfigs = {
        title: '新建箱码',
        visible: modalVisible,
        setVisible: setmodalVisible,
        okHandle: async () => {
            const results = await addForm.validateFields();
            console.log(results);
            addBoxCode.mutate({
                count: results?.count
            });
            setmodalVisible(false);
            addForm.resetFields();
        },
        onCancelHandle: () => {
            setmodalVisible(false);
            addForm.resetFields();
        }
    };

    const addConfig = [
        {
            label: '箱码数量',
            type: 'InputNumber',
            value: 'count',
            placeholder: '请输入箱码数量',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[1-9]\d{0,2}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入箱码数量！');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            callback('请输入正整数，并保持在3字符内');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            wide: 150
        }
    ];

    return (
        <>
            <Card
                // className="coreFIrmContainer"
                // className={styles.coreFIrmContainer}
                // mt24
                title={<PageTitle title='箱码列表' />}
                style={{ marginBottom: 10 }}
            >
                <div
                    // className="searchContainer"
                    className={styles.searchContainer}
                >
                    <Form
                        style={{ width: '100%' }}
                        form={search}
                        labelCol={{ span: 5 }}
                        className='label-title'
                        onFinish={(values) => {
                            handlePaginationChange(1);
                            querylist.current = values;
                            boxCodeQuery.refetch();
                        }}
                    >
                        <Row gutter={[24, 12]}>
                            <Col span={8}>
                                <Form.Item label='箱码' name='boxCode'>
                                    <BaseInput placeholder='请输入'></BaseInput>
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item label='溯源码' name='traceCode'>
                                    <BaseInput placeholder='请输入溯源码'></BaseInput>
                                </Form.Item>
                            </Col>

                            {more && (
                                <>
                                    <Col span={8}>
                                        <Form.Item label='创建时间' name='createTime'>
                                            <BaseDatePicker style={{ width: '100%' }}></BaseDatePicker>
                                        </Form.Item>
                                    </Col>
                                    <Col span={8}>
                                        <Form.Item label='状态' name='state'>
                                            <BaseSelect
                                                placeholder='请选择'
                                                options={[
                                                    {
                                                        label: '未激活',
                                                        value: 0
                                                    },
                                                    {
                                                        label: '已激活',
                                                        value: 1
                                                    },
                                                    {
                                                        label: '已作废',
                                                        value: 2
                                                    }
                                                ]}
                                            ></BaseSelect>
                                        </Form.Item>
                                    </Col>
                                </>
                            )}

                            <Col span={more ? 16 : 8}>
                                <div style={{ display: 'flex', justifyContent: 'end' }}>
                                    <Space>
                                        <BaseButton
                                            htmlType='submit'
                                            type='primary'
                                            // className='searchBtn'
                                            style={{ width: 100 }}
                                            className={`${styles.searchBtn} ${styles.baseBtn}`}
                                            icon={<SearchOutlined rev={undefined} />}
                                        >
                                            查询
                                        </BaseButton>
                                        <BaseButton
                                            type='dashed'
                                            className='primaryBtn'
                                            style={{ width: 100 }}
                                            icon={<SyncOutlined rev={undefined} />}
                                            onClick={() => {
                                                querylist.current = null;
                                                setCheckData([]);
                                                setCheckId([]);
                                                boxCodeQuery.refetch();
                                                search.resetFields();
                                            }}
                                        >
                                            重置
                                        </BaseButton>
                                        <BaseButton
                                            type='link'
                                            style={{ color: '#80a932' }}
                                            onClick={() => setMore(!more)}

                                            // style={{ width: 120, display: 'inlineBlock' }}
                                        >
                                            {more ? '收起' : '展开'}
                                            {more ? <UpOutlined rev={undefined} /> : <DownOutlined rev={undefined} />}
                                        </BaseButton>
                                    </Space>
                                </div>
                            </Col>
                        </Row>
                    </Form>
                </div>
            </Card>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
            >
                <SelectTable
                    rowKey='id'
                    preserveSelectedRowKeys={true}
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return (
                            <TableHead
                                LeftDom={<div></div>}
                                RightDom={
                                    <div
                                        style={{
                                            display: 'flex'
                                        }}
                                    >
                                        <Space>
                                            <BaseButton
                                                // icon={<PlusOutlined />}
                                                // className='greenBtn'
                                                disabled={checkId.length === 0}
                                                onClick={async () => {
                                                    const data: any = await downloadBoxCode({ boxIds: checkId });
                                                    console.log(data);
                                                    const blob = new Blob([data], { type: 'application/vnd.ms-excel' });
                                                    const url = URL.createObjectURL(blob);
                                                    const a = document.createElement('a');
                                                    a.href = url;
                                                    a.download = `箱码`;
                                                    a.click();
                                                    URL.revokeObjectURL(url);
                                                }}
                                            >
                                                下载箱码
                                            </BaseButton>
                                            <BaseButton
                                                type='dashed'
                                                icon={<PlusOutlined rev={undefined} />}
                                                className='greenBtn'
                                                onClick={() => {
                                                    setmodalVisible(true);
                                                }}
                                            >
                                                新建箱码
                                            </BaseButton>
                                        </Space>
                                    </div>
                                }
                            />
                        );
                    }}
                    setCheckId={setCheckId}
                    checkId={checkId}
                    checkData={checkData}
                    setCheckData={setCheckData}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={boxCodeQuery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={boxCodeQuery?.data?.data?.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />

                <BaseModal {...addConfigs}>
                    <Form name='addProductForm' form={addForm} className='edit-label-title'>
                        {<FilterForm itemConfig={addConfig} labelCol={10} />}
                    </Form>
                </BaseModal>
            </BaseCard>
        </>
    );
};

export default WithPaginate(RawMaterialList);

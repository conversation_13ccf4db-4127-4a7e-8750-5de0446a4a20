import { useLocation, useParams } from 'react-router-dom';
// import { useNavigate, useParams } from 'react-router-dom';
import dayjs from 'dayjs';
import React, { useState, useRef } from 'react';
import { Form, message, Image, Space, Badge, Modal } from 'antd';
import {
    staffPage,
    addStaff,
    userModiy,
    updateStaff,
    resetPassWord,
    staffModiy,
    resetStaffPassWord
} from '@services/user';
import WithPaginate from '../../hoc/withpaginate';
import { useMutation, useQuery } from 'react-query';
import BaseCard from '@components/base-card';
import BaseFormItem from '@components/base-form-item';
import PageTitle from '@components/page-title';
import { ReformChainError } from '@utils/errorCodeReform';
import BasePagination from '@components/base-pagination';
import BaseButton from '@components/base-button';
import { ColumnsType } from 'antd/lib/table';
import BaseTable from '@components/base-table';
import { landTestDetail, landDeit, landDetails } from '@services/land-test';

import { QcqaInfoConfig, chainInfoConfig } from './config';
import FilterForm from '@components/filter-form/filter-form';
import ChainDetailModal from '@components/chain_detail_modal';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { decryptedUrl } from '@utils';
const { confirm } = Modal;

function QcqaDetail(props: any) {
    const { pageInfo, handlePaginationChange } = props;
    const { state } = useLocation();
    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    const [inspectionAccessory, setInspectionAccessory] = useState<any>();
    const [inspectionReport, setInspectionReport] = useState<any>();
    const queryuser: any = useRef(null);
    const [transId, setTransId] = useState('');
    const param = useParams();
    console.log(param);
    console.log('state', state);
    //查看详情
    const qualityTestquery = useQuery(['landDetails'], () => {
        return landDetails(param.id);
    });

    const qualityTestdata = qualityTestquery?.data?.data;
    console.log(qualityTestdata);
    const [QcqaForm] = Form.useForm();
    const [ChainForm] = Form.useForm();

    const valuesForm = QcqaForm.getFieldsValue();
    console.log('SourceCodeForm', QcqaForm, valuesForm);

    const chainInfoConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Custom',
            children: qualityTestdata?.transactionId ? (
                <a onClick={() => setChainDetailModalVisible(true)}>{qualityTestdata?.transactionId}</a>
            ) : (
                '-'
            )
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];
    const QcqaInfoConfig = [
        {
            label: '地块名称',
            name: 'landName',
            value: 'landName',
            type: 'ShowText',
            span: 8
        },
        {
            label: '地块面积',
            name: 'landArea',
            value: 'landArea',
            type: 'ShowText',
            span: 8
        },
        {
            label: '所属区域',
            name: 'region',
            value: 'region',
            type: 'ShowText',
            span: 8
        },
        {
            label: '所属万亩方',
            name: 'wanmuId',
            value: 'wanmuId',
            type: 'ShowText',
            span: 8
        },

        {
            label: '地块详细地址',
            name: 'landPlace',
            value: 'landPlace',
            type: 'ShowText',
            span: 8
        }

        // {
        //     label: '质检结果',
        //     name: 'inspectionResults',
        //     value: 'inspectionResults',
        //     type: 'Status',
        //     span: 8,
        //     display: (
        //         <h3 className={qualityTestdata?.inspectionResults !== 2 ? 'SuccessStatus' : 'ErrorStatus'}>
        //             {qualityTestdata?.inspectionResults !== 2 ? '合格' : '不合格'}
        //         </h3>
        //     )
        // },
        // {
        //     label: '质检报告',
        //     name: 'inspectionReport',
        //     value: 'inspectionReport',
        //     type: 'Custom',
        //     span: 8,
        //     children: <Image width={70} src={inspectionReport}></Image>
        // },
        // {
        //     label: '附件',
        //     name: 'inspectionAccessory',
        //     value: '123',
        //     type: 'Custom',
        //     span: 8,
        //     children: qualityTestdata?.inspectionAccessory ? <a href={inspectionAccessory}>{'下载'}</a> : '-'
        // }
    ];

    const listColumn: ColumnsType<any> = [
        {
            title: '种植批次',
            dataIndex: 'plantBatch',
            key: 'plantBatch',
            ellipsis: true
        },
        {
            title: '农作物类型',
            dataIndex: 'plantName',
            key: 'plantName',
            ellipsis: true
        },

        {
            title: '播种时间',
            dataIndex: 'sowTime',
            key: 'sowTime',
            ellipsis: true,
            render: (_, row) => dayjs(row.sowTime).format('YYYY-MM-DD HH:mm:ss')
        },
        {
            title: '产量(吨)',
            dataIndex: 'harvestNum',
            key: 'harvestNum',
            ellipsis: true
        },
        {
            title: '收割时间',
            dataIndex: 'harvestTime',
            key: 'harvestTime',
            ellipsis: true,
            render: (_, row) => (row.harvestTime ? dayjs(row.harvestTime).format('YYYY-MM-DD HH:mm:ss') : '')
        },

        {
            title: '状态',
            dataIndex: 'status',
            ellipsis: true,
            key: 'status',
            render: (data: any) => (
                <span style={{ color: data == '1' ? '#F64041' : '#666666' }}>
                    <Badge
                        status={data == '1' ? 'error' : 'success'}
                        color={data == '1' ? '#F64041' : 'rgb(36, 171, 59)'}
                        text={data == '1' ? '禁用' : '可用'}
                    />
                </span>
            )
        },
        {
            width: 300,
            title: '操作',
            // dataIndex: 'operation',
            // key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle'>
                    <BaseButton
                        type='dashed'
                        className={record.status ? 'primaryBtn' : 'warnBtn'}
                        // onClick={() => {
                        //     const opp = staffmodiy.mutate({
                        //         state: !record?.status ? 1 : 0,
                        //         userId: record?.account
                        //     });
                        // }}
                        onClick={
                            record.status
                                ? () => {
                                      staffmodiy.mutate({
                                          id: record?.id,
                                          opt: 'ENABLE'
                                      });
                                  }
                                : () => {
                                      showConfirm(record);
                                  }
                        }
                    >
                        {record.status ? '启用' : '禁用'}
                    </BaseButton>

                    <BaseButton
                        type='dashed'
                        className={'primaryBtn'}
                        onClick={() => {
                            console.log(record);
                            setTransId(record.transactionId);
                            setChainDetailModalVisible(true);
                        }}
                    >
                        查看上链信息
                    </BaseButton>

                    {/* {
            label: '查询记录',
            name: 'record',
            value: '123',
            type: 'Url',
            span: 8,
            display: (
                <a
                    onClick={() => {
                        setIsModalOpen(true);
                    }}
                >
                    {'点击查看'}
                </a>
            )
        } */}
                    {/* <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            // setIsModalOpen(true);
                        }}
                    >
                        农事作业详情
                    </BaseButton> */}
                    {/* <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            console.log('record', record);
                            const paths = record?.menu;
                            const staffmenu = paths?.split(',')?.map((item: any) => {
                                return staff[item];
                            });
                            // console.log("staffmenu",staffmenu)
                            editEmployeesForm.setFieldsValue({
                                userName: record?.operation,
                                phoneNumber: record?.phone,
                                userId: record?.account,
                                limits: staffmenu
                            });
                            setEditId(record?.account);
                            setEditModelVisible(true);
                        }}
                    >
                        编辑
                    </BaseButton> */}
                    {/* <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            pswRef.current = record?.account;
                            setresetPswVisible(true);
                        }}
                    >
                        重置密码
                    </BaseButton> */}
                </Space>
            )
        }
    ];
    ChainForm.setFieldsValue({
        blockNum: qualityTestdata?.blockNum,
        transactionId: qualityTestdata?.transactionId,
        transactionTime: qualityTestdata?.transactionTime
            ? dayjs(qualityTestdata?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
            : null
    });
    QcqaForm.setFieldsValue({
        landName: qualityTestdata?.landName,
        landArea: qualityTestdata?.landArea + '亩',
        landPlace: qualityTestdata?.landPlace,
        region: qualityTestdata?.region,
        wanmuId: qualityTestdata?.wanmuId
    });
    const staffquery = useQuery(
        ['userquery', pageInfo],
        () => {
            return landTestDetail({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                // param: queryuser?.current?.name?.trim() || undefined,
                landId: param.id
                // isQueryMenu: 1
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );

    // 修改状态
    const staffmodiy = useMutation(landDeit, {
        onSuccess(res) {
            message.success('修改状态成功');
            staffquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            staffquery.refetch();
        }
    });
    //员工列表数据
    const tableData = staffquery?.data?.data?.records?.map((item: any) => ({
        harvestNum: item.harvestNum,
        sowTime: item.sowTime,
        harvestTime: item.harvestTime,
        // menu: item.menuName?.join(','),
        plantName: item.plantName,
        status: item.state,
        plantBatch: item.plantBatch,
        transactionId: item.transactionId,
        id: item.id
    }));

    //禁用
    const showConfirm = (record: any) => {
        confirm({
            title: '确定要禁用该批次吗？',
            okText: '停用',
            cancelText: '取消',
            icon: <ExclamationCircleFilled rev={undefined} />,
            // content: `禁用后该用户无法使用${sessionStorage.systemTitle}`,
            content: `禁用后该批次无法进行加工`,
            onOk() {
                staffmodiy.mutate({
                    id: record.id,
                    opt: 'DISABLE'
                });
            },
            onCancel() {}
        });
    };
    return (
        <>
            <BaseCard title={<PageTitle title='种植信息详情' bg='container zhong' />}>
                <Form form={QcqaForm}>
                    <PageTitle title='地块信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={QcqaInfoConfig} labelCol={false} />
                </Form>
                <Form form={ChainForm}>
                    <PageTitle title='农事作业详情信息' type='primaryIcon' bmagin={16} />
                    {/* <FilterForm showMode itemConfig={chainInfoConfig} labelCol={false} /> */}
                </Form>
                <BaseTable
                    rowKey='account'
                    columns={listColumn}
                    dataSource={tableData}
                    loading={staffquery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={staffquery?.data?.data?.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>

            <ChainDetailModal
                transactionId={transId}
                open={ChainDetailModalVisible}
                onCancel={() => setChainDetailModalVisible(false)}
            />
        </>
    );
}

export default WithPaginate(QcqaDetail);

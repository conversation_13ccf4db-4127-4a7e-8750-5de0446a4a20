.base-input {
    background: transparent;
    .ant-input,
    .ant-input-affix-wrapper {
        background: transparent !important;
    }
}
.base-input::placeholder {
    font-size: 14px;
    color: #b7b7b7;
}
.count {
    width: 280px;
}
.find {
    width: 230px;
}
.participants {
    width: 280px;
    display: inline-block;
}
.ant-input:disabled {
    cursor: text;
}
.ant-input:hover {
    border-color: #76ae55;
}
.ant-input:focus,
.ant-input-focused {
    border-color: #76ae55;
}
.ant-input:focus,
.ant-input-focused {
    border-color: #76ae55;
    box-shadow: 0 0 0 2px rgba(48, 110, 36, 0.2);
}

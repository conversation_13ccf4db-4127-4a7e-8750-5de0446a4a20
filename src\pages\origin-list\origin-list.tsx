/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-09 16:17:21
 * @LastEditTime: 2022-10-14 10:15:21
 * @LastEditors: PhilRandWu
 */
/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-10-09 16:16:29
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space, Button, Modal, Select } from 'antd';
import { getOriginList, updateOriginState, updateProduct } from '@services/origin';
import { foodPage } from '@services/food';
import useUrlState from '@ahooksjs/use-url-state';
import { useMutation, useQuery } from 'react-query';
import styles from './index.module.less';
import { useRef, useState } from 'react';
import PageTitle from '@components/page-title';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { useFoodList } from '../../myhooks/usefoodlist';
import copyToClipboard from 'copy-to-clipboard';
import BaseInput from '@components/base-input';
import SingleSearch from '@components/single-search';
import { Navigate, useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import { ReformChainError } from '@utils/errorCodeReform';
import { ColumnsType } from 'antd/lib/table';
import BaseModal from '@components/base-modal';
import FilterForm from '@components/filter-form/filter-form';
import { ExclamationCircleFilled } from '@ant-design/icons';
import PaginationSelect from '@components/pagination-select';
import { signData } from '@utils/blockChainUtils';
import { useDispatch, useSelector } from 'react-redux';
import { getProductSelectListInPlaceSelect } from '@services/food';

const FoodList = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [addOriginForm] = Form.useForm<any>();
    const [editEmployeesForm] = Form.useForm();
    const [addModalVisible, setAddModelVisible] = useState(false);
    const [editModalVisible, setEditModelVisible] = useState(false);
    const [placeProductId, setPlaceProductId] = useState();
    const querylist = useRef('');

    const queryList: any = useFoodList({
        pageIndex: pageInfo.pageIndex,
        pageSize: pageInfo.pageSize
    });

    console.log('queryList', queryList);
    //修改状态
    const originStateMutation = useMutation(updateOriginState, {
        onSuccess(res) {
            message.success('修改状态成功');
            originListQuery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            originListQuery.refetch();
        }
    });

    //产品选择(查询)
    const searchSelectProductsQuery = useQuery(
        ['searchSelectProductsQuery'],
        () => {
            return getProductSelectListInPlaceSelect({});
        },
        {
            onSuccess(data) {
                console.log(data);
            },
            onError(err: any) {
                ReformChainError(err);
            },
            enabled: addModalVisible || editModalVisible
        }
    );

    //配置所属产品
    const updateProductMutation = useMutation(updateProduct, {
        onSuccess(res) {
            message.success('配置所属产品成功');
            setEditModelVisible(false);
            addOriginForm.resetFields();
            originListQuery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            originListQuery.refetch();
        }
    });

    const originListQuery = useQuery(
        ['originListQuery', pageInfo],
        () => {
            // if (!userInfo.user?.organization_id) {
            //     message.error('未获取到机构id');
            //     return Promise.reject('未获取到机构id');
            // }
            return getOriginList({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                param: querylist?.current
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    const originListData = originListQuery?.data?.data?.records || [];

    const listColumn: ColumnsType<any> = [
        {
            title: '产地编号',
            dataIndex: 'id',
            key: 'id',
            ellipsis: true
        },
        {
            title: '产地名称',
            dataIndex: 'placeName',
            key: 'placeName',
            ellipsis: true
        },
        // {
        //     title: '产品编码',
        //     dataIndex: 'code',
        //     key: 'code',
        //     ellipsis: true
        // },
        {
            title: '产地地址',
            dataIndex: 'placeAddress',
            key: 'placeAddress',
            ellipsis: true
        },
        {
            title: '所属产品',
            dataIndex: 'products',
            key: 'products',
            ellipsis: true,
            render(products) {
                return products?.map?.((item: any) => item?.productName)?.join('；');
            }
        },
        {
            title: '配置产地信息',
            dataIndex: 'operation',
            key: 'operation',
            ellipsis: true,
            render: (data: any, record: any) => (
                <span
                    className={styles.configBtn}
                    onClick={() => {
                        navigate('infoConfig', {
                            state: {
                                id: record.id
                            }
                        });
                    }}
                >
                    配置
                </span>
            )
        },
        {
            title: '配置所属产品',
            dataIndex: 'operation',
            key: 'operation',
            ellipsis: true,
            render: (data: any, record: any) => (
                <span
                    className={styles.configBtn}
                    onClick={() => {
                        // TODO need id set to value
                        setPlaceProductId(record?.id);
                        addOriginForm.setFieldsValue({
                            product: record.products.map((item: any) => {
                                return {
                                    label: item?.productName,
                                    value: item?.id
                                };
                            })
                        });
                        setEditModelVisible(true);
                    }}
                >
                    配置
                </span>
            )
        },
        {
            title: '状态',
            dataIndex: 'state',
            key: 'state',
            ellipsis: true,
            render: (data: any) => (
                <span style={{ color: data ? '#F64041' : '#666666' }}>
                    <Badge
                        status={data ? 'error' : 'success'}
                        color={data ? '#F64041' : 'rgb(36, 171, 59)'}
                        text={data ? '禁用' : '可用'}
                    />
                </span>
            )
        },
        {
            width: 200,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle' className='operation'>
                    <BaseButton
                        // type='dashed'
                        className={record.state ? 'primaryBtn' : 'warnBtn'}
                        // ghost
                        onClick={() => {
                            // console.log("record",record)
                            if (!record.state) {
                                Modal.confirm({
                                    title: '确定要禁用该产地吗？',
                                    okText: '确定',
                                    icon: <ExclamationCircleFilled rev={undefined} />,
                                    content: '禁用后该产地信息将不再展示在溯源码中',
                                    onOk() {
                                        originStateMutation.mutate({
                                            placeId: record?.id
                                        });
                                    },
                                    onCancel() {}
                                });
                                return;
                            }
                            const opp = originStateMutation.mutate({
                                placeId: record?.id
                            });
                        }}
                    >
                        {record.state ? '启用' : '禁用'}
                    </BaseButton>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            const foodid = record?.id;
                            navigate('detail', {
                                state: {
                                    id: foodid
                                }
                            });
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    const searchConfig = {
        label: '',
        classname: 'searchConfig-input',
        handleSearch: (values: any) => {
            handlePaginationChange(1);
            originListQuery.refetch();
        },
        placeholder: '输入产地名称',
        setSearchValue: (values: any) => {
            // console.log("values",values)
            querylist.current = values;
        }
    };

    const addOriginConfig = {
        okText: '确定',
        title: '选择产品',
        visible: addModalVisible,
        setVisible: setAddModelVisible,
        okHandle: async () => {
            try {
                const results = await addOriginForm.validateFields();
                console.log(results);
                navigate('add', {
                    state: {
                        productIds: results.product.map((item: any) => item.value)
                    }
                });
            } catch (e) {}
        },
        onCancelHandle: () => {
            setAddModelVisible(false);
            addOriginForm.resetFields();
        }
    };

    const editOriginProductConfig = {
        okText: '确定',
        title: '选择产品',
        visible: editModalVisible,
        setVisible: setEditModelVisible,
        okHandle: async () => {
            try {
                const results = await addOriginForm.validateFields();
                console.log(results);
                const params: any = {
                    placeId: placeProductId,
                    productIds: results?.product?.map((item: any) => {
                        return item.value;
                    })
                };
                const paramStr = JSON.stringify(params);
                signData(dispatch, JSON.stringify(params), (error, result: any) => {
                    if (!error && result) {
                        updateProductMutation.mutate({
                            updatePlaceProductVo: params,
                            paramStr: paramStr,
                            signature: result
                        });
                    } else if (error !== 'misprivatekey') {
                        message.info('签名异常，请重试或联系管理员');
                    }
                });
            } catch (e) {}
        },
        onCancelHandle: () => {
            setEditModelVisible(false);
            addOriginForm.resetFields();
        }
    };

    const addOriginConfigs = [
        {
            label: '所属产品',
            type: 'Custom',
            value: 'product',
            rules: [{ required: true, message: '' }],
            children: (
                <Form.Item
                    name='product'
                    rules={[
                        {
                            required: true,
                            message: '请输入所属产品'
                        }
                    ]}
                >
                    <Select
                        mode='multiple'
                        style={{ width: '100%' }}
                        showArrow
                        showSearch={false}
                        placeholder='请选择'
                        labelInValue
                        options={searchSelectProductsQuery?.data?.data?.map((item: any) => {
                            return {
                                label: item?.productName,
                                value: item?.id
                            };
                        })}
                    />
                </Form.Item>
            )
            // fields: productListData.map((item:any)=>({label:item.productName,value:item.id}))
        }
    ];

    return (
        <>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
                title={<PageTitle title='产地列表' bg='container chandi' />}
            >
                <BaseTable
                    rowKey='account'
                    className='food-table-operation'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return (
                            <TableHead
                                LeftDom={<div></div>}
                                RightDom={
                                    <div
                                        style={{
                                            display: 'flex',
                                         marginBottom: '20px'
                                        }}
                                    >
                                        <SingleSearch {...searchConfig} />
                                        <BaseButton
                                            type='dashed'
                                            icon={<PlusOutlined rev={undefined} />}
                                            className='bgBtn'
                                            onClick={() => {
                                                setAddModelVisible(true);
                                            }}
                                        >
                                            新建产地
                                        </BaseButton>
                                    </div>
                                }
                            />
                        );
                    }}
                    columns={listColumn}
                    dataSource={originListData}
                    loading={originListQuery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={originListQuery?.data?.data?.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>

            <BaseModal {...addOriginConfig}>
                <Form name='addOriginForm' form={addOriginForm} className='edit-label-title'>
                    {<FilterForm itemConfig={addOriginConfigs} />}
                </Form>
            </BaseModal>

            <BaseModal {...editOriginProductConfig}>
                <Form name='editOriginProductForm' form={addOriginForm} className='edit-label-title'>
                    {<FilterForm itemConfig={addOriginConfigs} />}
                </Form>
            </BaseModal>
        </>
    );
};

export default WithPaginate(FoodList);

export function getAllIds(data: any) {
    let ids: any = [];

    function extractIds(menu: any) {
        ids.push(menu.id);

        if (menu.childrenMenu && Array.isArray(menu.childrenMenu)) {
            menu.childrenMenu.forEach((child: any) => {
                extractIds(child);
            });
        }
    }

    data.forEach((item: any) => {
        extractIds(item);
    });

    return ids;
}

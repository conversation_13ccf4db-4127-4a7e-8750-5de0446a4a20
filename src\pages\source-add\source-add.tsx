/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 18:07:55
 * @LastEditTime: 2022-11-01 18:22:49
 * @LastEditors: PhilRandWu
 */

import BaseButton from '@components/base-button';
import BaseCard from '@components/base-card';
import { addMaterial, alidFoodList, supplierList } from '@services/material';
import FilterForm from '@components/filter-form';
import { useMutation, useQuery } from 'react-query';
import PageTitle from '@components/page-title';
import { Form, message } from 'antd';
import {} from './config';
import { ReformChainError } from '@utils/errorCodeReform';
import styles from './index.module.less';
import { useNavigate } from 'react-router-dom';
const SourceAdd = () => {
    const navigate = useNavigate();
    //所属列表
    const candidatae = useQuery(
        ['candidatae'],
        () => {
            return alidFoodList({});
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    // console.log("candidatae",candidatae)
    //供应商列表
    const suppLier = useQuery(
        ['suppLierList'],
        () => {
            return supplierList();
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    console.log('candidatae', suppLier);
    const mapToEnum: any = {};
    (candidatae?.data?.data || [])?.forEach((item: any, index: any) => {
        mapToEnum[item.food_name] = item.id;
    });
    const mapToEnum_2: any = {};
    (suppLier?.data?.data || [])?.forEach((item: any, index: any) => {
        // console.log(7777777,item)
        mapToEnum_2[item.short_name] = item.company_id;
    });
    const addConfigs = [
        {
            type: 'Input',
            label: '原料名称',
            value: 'name',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,50}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入原料名称！');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else if (!verify) {
                            callback('请输入原料名称，支持中文、字母或数字!');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入原料名称'
        },
        {
            type: 'Select',
            label: '所属产品',
            value: 'type',
            showSearch: 'showSearch',
            mode: 'true',
            rules: [{ required: true, message: '请选择所属产品!' }],
            placeholder: '请选择所属产品',
            fields: [
                ...(candidatae?.data?.data || [])?.map((item: any, index: any) => {
                    // console.log(7777777,item)
                    const manager = {
                        value: item.food_name,
                        label: item.food_name
                    };
                    return manager;
                })
            ]
        },
        {
            type: 'Select',
            label: '供应商',
            value: 'supplier',
            mode: 'true',
            showSearch: 'showSearch',
            rules: [{ required: true, message: '请选择供应商!' }],
            placeholder: '请选择供应商',
            // defaultValue: ['zhejiang', 'hangzhou', 'xihu'],
            fields: [
                ...(suppLier?.data?.data || [])?.map((item: any, index: any) => {
                    // console.log(7777777,item)
                    const manager = {
                        value: item.short_name,
                        label: item.short_name
                    };
                    return manager;
                })
            ]
        }
    ];

    const addmaterial = useMutation(addMaterial, {
        onSuccess(res) {
            message.success('增加原料成功');
            navigate('/product-manage/source');
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    const onFinish_1 = (values: any) => {
        console.log('values', values, mapToEnum);
        const food_id = values.type.map((item: any, index: any) => {
            return mapToEnum[item];
        });
        const supplier_id = values.supplier.map((item: any, index: any) => {
            return mapToEnum_2[item];
        });
        console.log('supplier_id', supplier_id);
        addmaterial.mutate({
            materialName: values.name,
            foodList: food_id,
            supplierList: supplier_id
        });
    };
    // formRef = React.createRef<FormInstance>();
    // onReset = () => {
    //     this.formRef.current!.resetFields();
    // }

    // render() {
    return (
        <BaseCard title={<PageTitle title='新建原料' />}>
            <Form onFinish={onFinish_1} className='edit-label-title'>
                <FilterForm itemConfig={addConfigs} labelCol={3} wrapperCol={9} />

                <div className={styles.addBtnContainer}>
                    <Form.Item className={styles.saveBtn}>
                        <BaseButton type='primary' htmlType='submit' className={styles.submitBtn}>
                            保存
                        </BaseButton>
                    </Form.Item>
                    <Form.Item>
                        <BaseButton
                            htmlType='button'
                            // onClick={this.onReset}
                            type='dashed'
                            className={styles.primaryBtn}
                            onClick={() => {
                                navigate('/product-manage/source');
                            }}
                        >
                            取消
                        </BaseButton>
                    </Form.Item>
                </div>
            </Form>
        </BaseCard>
    );
};
// }

export default SourceAdd;

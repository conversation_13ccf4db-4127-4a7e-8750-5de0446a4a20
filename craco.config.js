const path = require('path');
const WebpackBar = require('webpackbar');
const WorkerPlugin = require('worker-plugin');
const AntdDayjsWebpackPlugin = require('antd-dayjs-webpack-plugin');
const CracoLessPlugin = require('craco-less');
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const resolve = (dir) => path.resolve(__dirname, dir);

module.exports = ({ env: webpackEnv }) => {
  return {

    webpack: {
      // publicPath: '/web', // 在这里添加 publicPath
      plugins: [
        // new WebpackBar({
        //     name: webpackEnv !== 'production' ? '正在启动' : '正在打包',
        //     color: '#3D73EF'
        // }),
        new WorkerPlugin(),
        new NodePolyfillPlugin({
          excludeAliases: ['console']
        }),
        new AntdDayjsWebpackPlugin()
        // new TerserPlugin({
        //     terserOptions: {
        //       ecma: undefined,
        //       warnings: false,
        //       parse: {},
        //       compress: {
        //         drop_console: process.env.NODE_ENV === "production", // 生产环境下移除控制台所有的内容
        //         drop_debugger: false, // 移除断点
        //         pure_funcs:
        //           process.env.NODE_ENV === "production" ? ["console.log"] : "", // 生产环境下移除console
        //       },
        //     },
        //   }),
      ],
      alias: {
        '@': resolve('src'),
        '@app': resolve('src/app'),
        '@pages': resolve('src/pages'),
        '@layout': resolve('src/layout'),
        '@components': resolve('src/components'),
        '@store': resolve('src/store'),
        '@utils': resolve('src/utils'),
        '@assets': resolve('src/assets'),
        '@router': resolve('src/router'),
        '@config': resolve('src/config'),
        '@services': resolve('src/services'),
        '@styles': resolve('src/styles'),
        '@myhooks': resolve('src/myhooks'),
        '@mock': resolve('src/mock'),
        '@hoc': resolve('src/hoc')
      },
      optimization: {
        minimizer: [
          new TerserPlugin({
            terserOptions: {
              // 这里可以设置Terser的选项
              // 例如，你可以启用mangle（混淆变量名）和compress（压缩代码）
              compress: {
                drop_console: true, // 删除所有的`console`语句
                // 还可以添加其他压缩选项...
              },
              mangle: {
                // 混淆选项
                // 例如，可以设置为true来启用变量名混淆
                // 注意：在生产环境中通常启用混淆
              },
              output: {
                comments: false, // 移除注释
              },
              // 提取注释到单独文件的选项（如果需要的话）
              extractComments: false,
            },
          }),
        ],
      },
    },
    plugins: [
      {
        plugin: CracoLessPlugin,
        options: {
          lessLoaderOptions: {
            lessOptions: {
              modifyVars: {
                hack: `true;@import "${require.resolve('./src/styles/public/index.less')}";`
              },
              javascriptEnabled: true
            }
          }
        }
      }
    ],
    style: {},
    buildOptions: {
      // 修改构建参数
      output: {
        publicPath: '/web',
      },
    },
    babel: {},
    devServer: {
      // open: true,
      proxy: {
        '/api': {
          // target: 'http://*************:9013',
          // target: 'http://***************:9022',
          // target: "http://***************:9018",
          // target: "http://************:9022",
          target: "http://**********:9099",
          // target: 'http://**************:9022',
          // target: "http://*************:9022",
          // target: "http://************:9022",
          changeOrigin: true,
          pathRewrite: { '/api': '' }
        }
        // '/online-api': {
        // **************:9013
        //     target: 'http://*************:9013',
        //     changeOrigin: true,
        //     pathRewrite: { '/online-api': '' }
        // }
      },
    }
  };
};

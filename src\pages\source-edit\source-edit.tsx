/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 18:07:55
 * @LastEditTime: 2022-11-01 18:23:44
 * @LastEditors: PhilRandWu
 */

import BaseButton from '@components/base-button';
import BaseCard from '@components/base-card';
import FilterForm from '@components/filter-form';
import PageTitle from '@components/page-title';
import { addMaterial, alidFoodList, supplierList, updateMaterial } from '@services/material';
import { useMutation, useQuery } from 'react-query';
import { Form, FormInstance, message } from 'antd';
import React, { Component, useEffect } from 'react';
import { addConfigs } from './config';
import styles from './index.module.less';
import { useLocation } from 'react-router-dom';
import { ReformChainError } from '@utils/errorCodeReform';
import { useNavigate } from 'react-router-dom';
const SourceEdit = () => {
    const navigate = useNavigate();
    const { state } = useLocation();
    console.log('state', state);
    const [editEmployeesForm] = Form.useForm();
    //编辑
    const matermodiy = useMutation(updateMaterial, {
        onSuccess(res) {
            message.success('编辑成功');
            navigate('/product-manage/source');
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    //所属列表
    const candidatae = useQuery(
        ['candidatae'],
        () => {
            return alidFoodList({});
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    // console.log("candidatae",candidatae)
    //供应商列表
    const suppLier = useQuery(
        ['suppLierList'],
        () => {
            return supplierList();
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    // console.log("suppLier",suppLier)
    const addConfigs = [
        {
            type: 'Input',
            label: '原料名称',
            value: 'name',
            disable: 'string',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback('请输入原料名称！');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入原料名称'
        },
        {
            type: 'TagsSelect',
            label: '所属产品',
            value: 'type',
            rules: [{ required: true, message: '请选择所属产品!' }],
            placeholder: '请选择所属产品',
            // defaultValue: ['zhejiang', 'hangzhou', 'xihu'],
            fields: [
                ...(candidatae?.data?.data || [])?.map((item: any, index: any) => {
                    console.log('7777777q123qweqwe', item);
                    const manager = {
                        value: item.id,
                        label: item.food_name
                    };
                    return manager;
                })
            ]
        },
        {
            type: 'Select',
            label: '供应商',
            value: 'supplier',
            rules: [{ required: true, message: '请选择供应商!' }],
            placeholder: '请选择供应商',
            mode: 'true',
            // defaultValue: ['zhejiang', 'hangzhou', 'xihu'],
            fields: [
                ...(suppLier?.data?.data || [])?.map((item: any, index: any) => {
                    // console.log('adadadad7777777', item);
                    const manager = {
                        value: item.company_id,
                        label: item.short_name
                    };
                    return manager;
                })
            ]
        }
    ];
    // const paths = state?.data?.suppliers.split(',');
    // console.log("candidatae",paths)
    useEffect(() => {
        const paths = state?.data?.supplierIds.split(',');
        const pathss = paths.map(Number);
        const food = state?.data?.foodIds.split(',');
        const foods = food.map(Number);
        console.log('foodsfoods', foods, pathss);
        editEmployeesForm.setFieldsValue({
            name: state?.data?.name,
            type: foods,
            supplier: pathss
        });
    }, []);
    const onFinish_1 = (values: any) => {
        console.log('values', values);
        matermodiy.mutate({
            materialId: state?.data?.id,
            materialName: values?.name,
            foodList: values?.type,
            supplierList: values?.supplier
        });
    };
    // formRef = React.createRef<FormInstance>();
    // onReset = () => {
    //     this.formRef.current!.resetFields();
    // }

    // render() {

    return (
        <BaseCard title={<PageTitle title='原料编辑' />}>
            <Form form={editEmployeesForm} onFinish={onFinish_1} className='edit-label-title'>
                <FilterForm itemConfig={addConfigs} labelCol={3} wrapperCol={9} />

                <div className={styles.addBtnContainer}>
                    <Form.Item className={styles.saveBtn}>
                        <BaseButton type='primary' htmlType='submit' className={styles.submitBtn}>
                            保存
                        </BaseButton>
                    </Form.Item>
                    <Form.Item>
                        <BaseButton
                            htmlType='button'
                            // onClick={this.onReset}
                            type='dashed'
                            className={styles.primaryBtn}
                            onClick={() => {
                                navigate('/product-manage/source');
                            }}
                        >
                            取消
                        </BaseButton>
                    </Form.Item>
                </div>
            </Form>
        </BaseCard>
    );
};
// }

export default SourceEdit;

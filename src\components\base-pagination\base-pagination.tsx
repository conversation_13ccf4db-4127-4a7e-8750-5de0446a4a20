/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-06-30 22:05:08
 * @LastEditTime: 2022-10-12 17:50:00
 * @LastEditors: PhilRandWu
 */
import React, { memo } from 'react';
import { Pagination } from 'antd';
import type { PaginationProps } from 'antd';

import './app-pagination.less';

export interface IAppPaginationProps extends Omit<PaginationProps, 'showTotal'> {
    shouldShowTotal?: boolean;
}

export default memo(function AppPagination(props: IAppPaginationProps) {
    const { shouldShowTotal = false, current, pageSize, ...restProps } = props;

    const showTotalText = (total: number) => {
        return `共 ${total} 条`;
    };

    return (
        <div className='AppPaginationContainer'>
            <Pagination
                showQuickJumper
                current={Number(current)}
                pageSize={Number(pageSize)}
                defaultCurrent={1}
                showTotal={shouldShowTotal ? showTotalText : undefined}
                {...restProps}
            ></Pagination>
        </div>
    );
});

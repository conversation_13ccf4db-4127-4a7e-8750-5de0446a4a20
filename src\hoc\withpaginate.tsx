/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-13 11:19:58
 * @LastEditTime: 2022-11-01 18:30:56
 * @LastEditors: PhilRandWu
 */
//WithCounter.js
import useUrlState from '@ahooksjs/use-url-state';
import React from 'react';

interface IUrlState {
    pageIndex: number;
    pageSize: number;
}

const WithPaginate = (OriginalComponent: any) => {
    const NewComponent = (props: any) => {
        const [urlState, setUrlState] = useUrlState<Partial<IUrlState>>({
            pageIndex: 1,
            pageSize: 10
        });

        const handlePaginationChange = (prePageNumber: number, prePageSize: number) => {
            setUrlState({
                pageIndex: prePageNumber,
                pageSize: prePageSize
            });
        };

        return <OriginalComponent pageInfo={urlState} handlePaginationChange={handlePaginationChange} {...props} />;
    };
    return NewComponent;
};

export default WithPaginate;

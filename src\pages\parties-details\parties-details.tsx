/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-11-02 14:35:27
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import styles from './index.module.less';
import PageTitle from '@components/page-title';
import { Form } from 'antd';
import { businessConfig, chainConfig, enterpriseConfig, headConfig } from './config';
import BaseButton from '@components/base-button';
import BaseFormItem from '@components/base-form-item';
import { useNavigate } from 'react-router-dom';
import { useLocation } from 'react-router-dom';
import { useMutation, useQuery } from 'react-query';
import { checkParticipantDetail } from '@services/participantPage';
import dayjs from 'dayjs';
import ChainDetailModal from '@components/chain_detail_modal';
import { useState } from 'react';

const CoreFlrm = () => {
    const { state } = useLocation();
    const navigate = useNavigate();
    console.log('state', state);

    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);

    const selectParticipantdetail = useQuery(['selectParticipantDetail'], () => {
        return checkParticipantDetail({
            partyId: state.data?.id
        });
    });

    const enterpriseConfig = [
        {
            label: '企业名称',
            name: 'name',
            displayDom: selectParticipantdetail?.data?.data?.companyName || '-'
        },
        {
            label: '统一社会信用代码',
            name: 'code',
            displayDom: selectParticipantdetail?.data?.data?.creditCode || '-'
        },
        {
            label: '地址',
            name: 'address',
            displayDom: selectParticipantdetail?.data?.data?.address || '-'
        }
    ];

    const ChainDetailModalConfig = {
        transactionId: selectParticipantdetail?.data?.data?.transactionId,
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };

    const headConfig = [
        {
            label: '姓名',
            name: 'accountName',
            displayDom: selectParticipantdetail?.data?.data?.principal || '-'
        },
        {
            label: '邮箱',
            name: 'email',
            displayDom: selectParticipantdetail?.data?.data?.email || '-'
        },
        {
            label: '手机号',
            name: 'phone',
            displayDom: selectParticipantdetail?.data?.data?.telephone || '-'
        }
    ];

    const chainConfig = [
        {
            label: '链上哈希',
            name: 'chainHash',
            tooltip: '信息的链上的哈希值',
            write: 'ture',
            type: 'custom',
            children: (
                <a onClick={() => setChainDetailModalVisible(true)}>
                    {selectParticipantdetail?.data?.data?.transactionId || '-'}
                </a>
            )
        },
        {
            label: '上链时间',
            name: 'chainTime',
            tooltip: '信息上链的时间',
            displayDom: selectParticipantdetail?.data?.data?.transactionTime
                ? dayjs(selectParticipantdetail?.data?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
                : '-'
        }
    ];

    return (
        <>
            <BaseCard title={<PageTitle title='参与方详情' />}>
                <Form name='basic' autoComplete='off'>
                    <PageTitle title='企业信息' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={enterpriseConfig} />

                    <PageTitle title='负责人信息' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={headConfig} />

                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={chainConfig} />
                </Form>
            </BaseCard>
            <ChainDetailModal {...ChainDetailModalConfig} />
        </>
    );
};

export default CoreFlrm;

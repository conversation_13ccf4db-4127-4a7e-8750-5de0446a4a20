/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:27:42
 * @LastEditTime: 2022-11-01 18:11:26
 * @LastEditors: PhilRandWu
 */
export const searchConfig = [
    {
        label: '企业名称',
        type: 'Input',
        value: 'name',
        placeholder: '请输入企业名称',
        span: 8,
        // className: 'find'
    },
    {
        label: '企业类型',
        type: 'Select',
        value: 'identity',
        placeholder: '请选择',
        span: 8,
        fields: [
            // { value: 1, label: '生产加工企业' },
            { value: 1, label: '生产加工企业' },
            // { value: 2, label: '供应商' },
            // { value: 3, label: '质检机构' },
            // { value: 4, label: '监管机构' },
            // { value: 5, label: '物流企业' }
            { value: 6, label: '运营方' }
        ]
        // className: 'find'
    },
    {
        label: '状态',
        type: 'Select',
        value: 'data',
        placeholder: '请选择',
        span: 8,
        className: 'find',
        fields: [
            {
                value: '1',
                label: '禁用'
            },
            {
                value: '0',
                label: '可用'
            }
        ]
    }
];
export const listColumn = [
    {
        title: '生产加工企业名称',
        dataIndex: 'account',
        key: 'account'
    },
    {
        title: '管理员',
        dataIndex: 'operation',
        key: 'operation'
    },
    {
        title: '管理员账号',
        dataIndex: 'name',
        key: 'name'
    },
    {
        title: '联系方式',
        dataIndex: 'ip',
        key: 'ip'
    },
    {
        title: '状态',
        dataIndex: 'time',
        key: 'time'
    },
    {
        title: '操作',
        dataIndex: 'time',
        key: 'time'
    }
];

export const addCoreFirm = [
    {
        label: '生产加工企业名称',
        type: 'Input',
        value: 'name',
        placeholder: '请输入生产加工企业名称',
        rules: [{ required: true, message: 'Please input your username!' }]
    },
    {
        label: '管理员账号',
        type: 'Select',
        value: 'account',
        placeholder: '请选择管理员账号',
        rules: [{ required: true, message: 'Please input your username!' }],
        fields: []
    }
];

export const editCoreFirm = [
    {
        label: '生产加工企业名称',
        type: 'Input',
        value: 'name',
        disabled: true,
        placeholder: 'erferferf',
        rules: [{ required: true, message: 'Please input your username!' }]
    },
    {
        label: '管理员账号',
        type: 'Select',
        value: 'account',
        placeholder: 'fefer',
        rules: [{ required: true, message: 'Please input your username!' }],
        fields: [
            {
                value: '12发给3',
                label: '123vfv123'
            },
            {
                value: '12dfvdfdf3',
                label: '1231vdfvdfvdd23'
            },
            {
                value: '12ddvsdfsffgnf3',
                label: '123gfbgfbfgb123'
            }
        ]
    }
];

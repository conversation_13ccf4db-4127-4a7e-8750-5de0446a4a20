import { Descriptions, DescriptionsProps, Modal, ModalProps } from 'antd';
import { useQuery } from 'react-query';
import PublicSourceService from '@services/traceability_data/public_source';
import dayjs from 'dayjs';

const ChainDetailModal = (
    props: ModalProps & {
        transactionId: string;
    }
) => {
    const queryChainDetail = useQuery(
        ['queryChainDetail', props?.transactionId],
        () => PublicSourceService.getChainHis(props?.transactionId),
        {
            enabled: props?.open
        }
    );
    const queryChainDetailData = queryChainDetail?.data?.data;
    const items: any[] = [
        {
            label: '所在链',
            children: '中移链'
        },
        {
            label: '所在区块',
            children: queryChainDetailData?.blockNum
        },
        {
            label: '链上哈希',
            children: queryChainDetailData?.transactionId
        },
        {
            label: '上链时间',
            children: dayjs(queryChainDetailData?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
        },
        {
            label: '出块时间',
            children: dayjs(queryChainDetailData?.blockTime).format('YYYY-MM-DD HH:mm:ss')
        }
    ];

    return (
        <Modal width={600} title='区块链信息' footer={false} {...props}>
            <Descriptions column={1}>
                {items?.map((item: any, index: number) => (
                    <Descriptions.Item label={item?.label}>{item?.children}</Descriptions.Item>
                ))}
            </Descriptions>
        </Modal>
    );
};

export default ChainDetailModal;

/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-11-01 18:17:36
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space, DatePicker } from 'antd';
import useUrlState from '@ahooksjs/use-url-state';

import { storageOutPage, cancelStorageOut } from '@services/storage-out';
import { useMutation, useQuery } from 'react-query';
import dayjs from 'dayjs';
import { ReformChainError } from '@utils/errorCodeReform';
import { ColumnsType } from 'antd/lib/table';

import styles from './index.module.less';
import { useRef, useState } from 'react';
import PageTitle from '@components/page-title';
import { PlusOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { useAccountList } from '../../myhooks/useaccountlist';
import SingleSearch from '@components/single-search';
import { useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import FilterForm from '@components/filter-form/filter-form';
import { SearchOutlined,SyncOutlined } from '@ant-design/icons';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

const { RangePicker } = DatePicker;

interface IUrlState {
    pageIndex: number;
    pageSize: number;
}

const PutOutStorageList = (props: any) => {
    const navigate = useNavigate();
    const querylist: any = useRef('');
    const [search]: any = Form.useForm();
    const { pageInfo, handlePaginationChange } = props;

    //分页
    const storageOutquery = useQuery(
        ['storageOutquery', pageInfo],
        () => {
            return storageOutPage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                outWarehouseNumber: querylist?.current?.outWarehouseNumber?.trim() || undefined,
                beginTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[0]).startOf('day')).format()
                    : undefined,
                endTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[1]).endOf('day')).format()
                    : undefined
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    // console.log("userquery",storageInquery)
    const tableData = storageOutquery?.data?.data?.records?.map((item: any) => ({
        id: item.id || '-',
        optName: item.optName || '-',
        createTime: dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss'),
        warehouseNumber: item.warehouseNumber
    }));

    const listColumn: ColumnsType<any> = [
        {
            title: '出库单号',
            dataIndex: 'warehouseNumber',
            key: 'warehouseNumber',
            ellipsis: true
        },
        {
            title: '操作人',
            dataIndex: 'optName',
            key: 'optName',
            ellipsis: true
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true,
            render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
        },
        {
            width: 190,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle'>
                    {/* <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        disabled={record.status}
                        onClick={() => {
                            storagemodiy.mutate({
                                storageOutId: record?.storageOutId
                            });
                        }}
                    >
                        作废
                    </BaseButton> */}
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            navigate('Outdetail', {
                                state: {
                                    data: record
                                }
                            });
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    const searchConfig = [
        {
            label: '出库单号',
            type: 'Input',
            value: 'outWarehouseNumber',
            placeholder: '请输入出库单号',
            span: 12,
            className: 'find'
        },
        {
            label: '创建时间',
            type: 'Custom',
            value: 'createTime',
            placeholder: '请选择',
            span: 12,
            className: 'find',
            children: <RangePicker style={{ width: 230 }} getPopupContainer={(trigger: any) => trigger.parentNode} />
        }
    ];

    return (
        <>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
                title={<PageTitle title='出库列表' />}
            >
                <BaseTable
                    rowKey='account'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return (
                            <TableHead
                                LeftDom={
                                    <div>
                                        {
                                            <div
                                                // className="searchContainer"
                                                className={styles.searchContainer}
                                            >
                                                <Form
                                                    onFinish={(values) => {
                                                        handlePaginationChange(1);
                                                        console.log('values', values);
                                                        querylist.current = values;
                                                        storageOutquery.refetch();
                                                    }}
                                                    layout='inline'
                                                    labelAlign='left'
                                                    form={search}
                                                    className='label-title'
                                                >
                                                    {/* <SearchForm /> */}
                                                    <FilterForm itemConfig={searchConfig} size={230} labelCol={6} />
                                                    <Space>
                                                        <BaseButton
                                                            htmlType='submit'
                                                            type='primary'
                                                            // className='searchBtn'
                                                            style={{ width: 100 }}
                                                            className={`${styles.searchBtn} ${styles.baseBtn}`}
                                                            icon={<SearchOutlined rev={undefined} />}
                                                        >
                                                            查询
                                                        </BaseButton>
                                                        <BaseButton
                                                            type='dashed'
                                                            className='primaryBtn'
                                                            style={{ width: 100 }}
                                                            icon={<SyncOutlined rev={undefined} />}
                                                            onClick={() => {
                                                                querylist.current = null;
                                                                storageOutquery.refetch();
                                                                search.resetFields();
                                                            }}
                                                        >
                                                            重置
                                                        </BaseButton>
                                                    </Space>
                                                </Form>
                                            </div>
                                        }
                                    </div>
                                }
                            />
                        );
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={storageOutquery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={storageOutquery?.data?.data.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>
        </>
    );
};

export default WithPaginate(PutOutStorageList);

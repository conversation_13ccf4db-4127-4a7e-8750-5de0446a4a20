/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-11-01 18:28:37
 * @LastEditTime: 2022-11-01 18:28:37
 * @LastEditors: PhilRandWu
 */
import request from '../request';
//出库分页
export const storageOutPage = (obj: any) => {
    return request({
        url: '/outWarehouse/getList',
        method: 'post',
        data: obj
    });
};
//作废
export const cancelStorageOut = (obj: any) => {
    return request({
        url: `/storage-out/cancelStorageOut?storageOutId=${obj.storageOutId}`,
        method: 'post',
        data: obj
    });
};
//出详情
export const storageOutDetail = (obj: any) => {
    return request({
        url: `/outWarehouse/getDetail`,
        method: 'get',
        params: obj
    });
};
//可以出库的列表
export const canOutFoodList = (obj: any) => {
    return request({
        url: '/storage-out/canOutFoodList',
        method: 'post',
        data: obj
    });
};
//可以出库的生产批次
export const canOutProductionList = (obj: any) => {
    return request({
        url: `/storage-out/canOutProductionList?foodId=${obj.foodId}`,
        method: 'post',
        data: obj
    });
};
//新增出库信息
export const addStorageOut = (obj: any) => {
    return request({
        url: '/storage-out/addStorageOut',
        method: 'post',
        data: obj
    });
};

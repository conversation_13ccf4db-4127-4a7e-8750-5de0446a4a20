/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-07-31 19:00:07
 * @LastEditTime: 2022-11-01 18:13:57
 * @LastEditors: PhilRandWu
 */
import { authTokenValid, getInfo } from '@services/login';
// import { loginAction, removeToken, resetPassword, setToken, validAuth } from '@store/moudle/user';
import codeImg from '@assets/icon/code.png';
import logonew from '@assets/icon/logonew.png';
import BaseModal from '@components/base-modal';
import { loginAction } from '@store/slice-reducer/user';
import { decryptStr } from '@utils';
import { ReformChainError } from '@utils/errorCodeReform';
import rsaEncrypt from '@utils/rsa';
import { Button, Form, Image, Input, message } from 'antd';
import axios from 'axios';
import { useEffect, useRef, useState } from 'react';
import { useMutation } from 'react-query';
import { useDispatch } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { LoGin, firstReset, getTitle } from '../../services/login';
import './index.less';

export default function Login(): any {
    const dispatch = useDispatch();
    const navigate: any = useNavigate();

    const [loginForm]: any = Form.useForm();
    const [initPassWordForm]: any = Form.useForm();
    const pswRef = useRef<any>();
    const [tipVisible, settipVisible] = useState(false);
    const [changePsw, setchangePsw] = useState(false);
    const [userId, setUserId] = useState(Number);
    const [userAccount, setUserAccount] = useState('');
    const location = useLocation();
    const [versionInfo, setversionInfo] = useState(false);
    const [followerCode, setFollowerCode] = useState<HTMLElement | any>();
    const [codeId, setCodeId] = useState<any>();
    const initJson: any = {
        title1: '欢迎使用',
        title2: '区块链溯源应用',
        title3: '区块链溯源应用'
    };
    const [titleJson, setTitleJson] = useState(initJson);

    // 修改之前登录代码,开发期间已做备份
    // const loginUser = useMutation(LoGin, {
    //     onSuccess(loginRes) {
    //         const jwt = loginRes?.data?.jwt;
    //         sessionStorage.setItem('jwt', jwt);
    //         const userinfo = {
    //             name: loginRes?.data?.userName,
    //             user_id: loginRes?.data?.userId,
    //             role_id: loginRes?.data?.roleId,
    //             circle_id: loginRes?.data?.circleId,
    //             company_id: loginRes?.data?.companyId,
    //             isFirst: loginRes?.data?.isFirst,
    //             telephone: loginRes?.data?.telephone
    //         };
    //         localStorage.setItem('userdata', JSON.stringify(userinfo));

    //         const userMenuPermissionList = (loginRes?.data?.menuList || []).map((menuId: any) => menuIdMap[menuId]);
    //         dispatch(
    //             loginAction({
    //                 userInfo: loginRes?.data,
    //                 menuPermissionList: userMenuPermissionList
    //             })
    //         );
    //         navigate('/');
    //         message.success('登录成功');
    //     },
    //     onError(err) {
    //         message.error('登录失败');
    //     }
    // });

    useEffect(() => {
        loginTitleInfo.mutate({
            titleType: 0
        });
        // getMyCode();
        // 校验 token 有效性
        const verifyToken = async (token: string) => {
            try {
                sessionStorage.clear();
                console.log('token', token);
                const verifyRet: any = await authTokenValid(token);
                console.log('verifyRet', verifyRet);
                if (verifyRet.code === 200) {
                    const jwt = verifyRet?.data;
                    sessionStorage.setItem('jwt', jwt);
                    const res = await getInfo();
                    // console.log(data);
                    if (res?.data?.firstFlag === 0) {
                        setUserId(res.data.userId);
                        setchangePsw(true);
                    } else {
                        //姓名电话解密
                        const telephone = await decryptStr(res?.data?.phoneNumber);
                        const userName = await decryptStr(res?.data?.userName);
                        const userinfo = {
                            name: userName,
                            user_id: res?.data?.userId,
                            role_id: res?.data?.identity,
                            circle_id: res?.data?.circleId,
                            company_id: res?.data?.orgId,
                            isFirst: res?.data?.firstFlag,
                            telephone: telephone,
                            privateKey: res?.data?.privateKey,
                            publicKey: res?.data?.publicKey,
                            isFill: res?.data?.isFill
                        };
                        sessionStorage.setItem('encryptKey', res?.data?.privateKey);
                        sessionStorage.setItem('publicKey', res?.data?.publicKey);
                        sessionStorage.setItem('isFill', res?.data?.isFill);
                        localStorage.setItem('userdata', JSON.stringify(userinfo));
                        await dispatch(
                            loginAction({
                                // userInfo: res?.data
                                userInfo: Object.assign({}, res?.data, userinfo)
                            })
                        );
                        if (!!!res?.data?.privateKey) {
                            navigate('/private');
                            return;
                        }
                        console.log('userinfo.role_id', userinfo.role_id, res?.data?.isFill);
                        if (userinfo.role_id === 1 && res?.data?.isFill === 0) {
                            //1为核心企业
                            navigate('/account/basicInfo');
                            return;
                        }
                        navigate('/');

                        message.success('登录成功');
                    }
                    return;
                } else {
                    message.error('验证token失败');
                }
            } catch (err: any) {
                message.error(err.data.message);
                let errcode = err?.response?.data?.code;
                if (errcode === 4000002 || errcode === 4000004) {
                    settipVisible(true);
                    return;
                } else if (errcode === 4000003) {
                    message.error('登录凭证已过期');
                    return;
                }
            }
        };
        const token = new URLSearchParams(location.search).get('token');
        const localtoken = sessionStorage.getItem('jwt');
        console.log('system token', token);
        if (token === '' && localtoken === null) {
            message.error('未找到登录凭证');
        } else if (token === null && localtoken !== null) {
            // 此处校验本地 token 有效性
            verifyToken(localtoken);
        } else if (token !== null) {
            // 此处校验 cmbass 跳转的 token 有效性
            verifyToken(`${token}`);
        }
    }, []);
    const loginTitleInfo = useMutation(getTitle, {
        onSuccess: async (res: any) => {
            console.log(res);
            if (res.code === 200) {
                const json = res?.data[0];
                sessionStorage.setItem('systemTitle', json.title3 || '区块链溯源应用');
                setTitleJson(json);
                return;
            }
        },
        onError(err: any) {
            console.log(err);
            sessionStorage.setItem('systemTitle', '区块链溯源应用');
        },
        retry: false
    });

    const fetchCaptcha = async () => {
        try {
            const response: any = await axios.get('/api/user/getImageCode', {
                responseType: 'blob'
            });
            const blob = response.data;

            let reader = new FileReader();

            reader.onload = function () {
                let text: any = reader.result;
                try {
                    let json = JSON.parse(text);
                    message.error(json.message);
                    console.log(json, '111111111');
                } catch (error) {
                    setCodeId(response?.headers?.['x-request-id']);
                    const localUrl = URL.createObjectURL(blob);
                    console.log(localUrl);
                    setFollowerCode(localUrl);
                }
            };
            reader.readAsText(blob);
        } catch (error) {
            console.error('Error fetching captcha image:', error);
        }
    };

    const resetImageCode = () => {
        loginForm.setFieldValue('passcode', '');
        fetchCaptcha();
    };

    useEffect(() => {
        fetchCaptcha();
    }, []);

    const loginUser = useMutation(LoGin, {
        onSuccess: async (res: any) => {
            console.log(res);
            const jwt = res?.data;
            sessionStorage.setItem('jwt', jwt);
            if (res.code === 200) {
                const res = await getInfo();
                // console.log(data);
                console.log('qwe', res);
                if (res?.data?.firstFlag === 0) {
                    setUserId(res.data.userId);
                    setchangePsw(true);
                } else {
                    //姓名电话解密
                    const telephone = await decryptStr(res?.data?.phoneNumber);
                    const userName = await decryptStr(res?.data?.userName);
                    const userinfo = {
                        name: userName,
                        user_id: res?.data?.userId,
                        role_id: res?.data?.identity,
                        circle_id: res?.data?.circleId,
                        company_id: res?.data?.orgId,
                        isFirst: res?.data?.firstFlag,
                        telephone: telephone,
                        privateKey: res?.data?.privateKey,
                        publicKey: res?.data?.publicKey,
                        isFill: res?.data?.isFill
                    };
                    sessionStorage.setItem('encryptKey', res?.data?.privateKey);
                    sessionStorage.setItem('publicKey', res?.data?.publicKey);
                    sessionStorage.setItem('isFill', res?.data?.isFill);
                    localStorage.setItem('userdata', JSON.stringify(userinfo));
                    dispatch(
                        loginAction({
                            // userInfo: res?.data
                            userInfo: Object.assign({}, res?.data, userinfo)
                        })
                    );
                    console.log('re-info', Object.assign({}, res?.data, userinfo));
                    if (!!!res?.data?.privateKey) {
                        navigate('/private');
                        return;
                    }
                    console.log('userinfo.role_id_loginFn', userinfo.role_id, res?.data?.isFill);
                    if (userinfo.role_id === 1 && res?.data?.isFill === 0) {
                        //1为核心企业
                        //message.info('温馨提示：请您及时填写企业信息，否则将无法使用平台其他功能');
                        navigate('/account/basicInfo');
                        return;
                    }
                    navigate('/');

                    message.success('登录成功');
                }
                return;
            }
        },
        onError(err: any) {
            console.log('err77888', err);
            resetImageCode();
            let errcode = err?.response?.data?.code;
            if (errcode === 4000002 || errcode === 4000004) {
                settipVisible(true);
                return;
            } else {
                ReformChainError(err);
            }
        },
        retry: false
    });

    const initPassWord = useMutation(firstReset, {
        onSuccess: (res: any) => {
            console.log('res: ', res);
            // loginUser.mutate({ account: userAccount, password: pswRef.current });
        }
    });

    const onFinish = async (values: any) => {
        const account = await rsaEncrypt(String(values?.username));
        if (account) {
            loginUser.mutate({
                phoneNumber: account,
                password: await rsaEncrypt(String(values?.password)),
                imageCode: values.passcode,
                'x-request-id': codeId
            });
            pswRef.current = String(values.password);
            setUserAccount(String(values?.username));
            if (!sessionStorage.systemTitle) {
                sessionStorage.setItem('systemTitle', titleJson.title3);
            }
        }
    };

    const onChangeFinish = (values: any) => {};

    return (
        <div className='bg'>
            <div className='loginCard' style={{ position: 'relative' }}>
                <div className='leftImg'>
                    <div className='leftImgFont'>
                        <p>{titleJson.title1}</p>
                        <p>{titleJson.title2}</p>
                        {/* <p className='p-line'></p> */}
                    </div>
                </div>
                <div className='rightForm'>
                    <img
                        style={{
                            display: 'inline-block',
                            width: '160px',
                            position: 'absolute',
                            right: '67px',
                            top: '50px'
                        }}
                        src={logonew}
                        alt=''
                    />
                    <div className='loginTitle'>用户登录</div>
                    <Form
                        name='login'
                        form={loginForm}
                        labelCol={{
                            span: 5
                        }}
                        wrapperCol={{
                            span: 19
                        }}
                        onFinish={onFinish}
                        autoComplete='off'
                    >
                        <Form.Item
                            name='username'
                            rules={[
                                {
                                    required: true,
                                    message: ''
                                },
                                () => ({
                                    validator: (_, value, callback) => {
                                        const regExp = new RegExp(/^[1][3,4,5,6.7,8,9][0-9]{9}$/);
                                        const verify = regExp.test(value);
                                        if (!value) {
                                            callback('请输入手机号');
                                        } else if (verify === false) {
                                            callback('请输入正确的手机号');
                                        } else {
                                            callback();
                                        }
                                    }
                                })
                            ]}
                        >
                            <Input
                                className='loginAccount'
                                placeholder='输入手机号'
                                maxLength={11}
                                prefix={<span className='accountIcon' />}
                            />
                        </Form.Item>

                        <Form.Item
                            name='password'
                            rules={[
                                {
                                    required: true,
                                    message: '请输入密码'
                                }
                            ]}
                        >
                            <Input.Password
                                className='loginPsw'
                                placeholder='登录密码'
                                prefix={<span className='passwordIcon' />}
                            />
                        </Form.Item>
                        <Form.Item
                            name='passcode'
                            rules={[
                                {
                                    required: true,
                                    message: '请输入验证码'
                                }
                            ]}
                        >
                            <Input
                                className='loginCode'
                                placeholder='验证码'
                                prefix={<img src={codeImg} />}
                                suffix={
                                    <div style={{ cursor: 'pointer' }} onClick={() => fetchCaptcha()}>
                                        {followerCode ? (
                                            <img width={110} height={34} src={followerCode}></img>
                                        ) : (
                                            <Image
                                                className='formImg'
                                                width={80}
                                                height={40}
                                                preview={false}
                                                src={followerCode}
                                                fallback='data:image/png;base64,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'
                                            />
                                        )}
                                    </div>
                                }
                            />
                        </Form.Item>

                        <div className='loginContainer'>
                            <Button className='loginBtn' type='primary' htmlType='submit' loading={loginUser.isLoading}>
                                登录
                            </Button>
                        </div>
                        {/* <span
                            className='otherLogin'
                            onClick={async () => {
                                // message.info('暂未开放');
                                const config = await axios.get(`${process.env.PUBLIC_URL}/config.json`);
                                console.log('config', config.data);
                                console.log(config.data.REACT_APP_FT_WEB_URL, 'config');
                                // window.open('http://10.248.201.215:30399/login');
                                window.open(`${config.data.REACT_APP_FT_WEB_URL}/login`);
                                // message.info('暂未开放');
                                // const config = await axios.get(`${process.env.PUBLIC_URL}/config.json`);
                                // console.log('config', config.data);
                                // console.log(config.data.REACT_APP_KXCC_WEB_CMBASS_URL, 'config');
                                // console.log(config.data.REACT_APP_FT_WEB_URL, 'config');
                                // window.open(`${config.data.REACT_APP_KXCC_WEB_CMBASS_URL}/login`);
                            }}
                        >
                            使用CMBaaS账号登录
                        </span> */}
                    </Form>
                </div>
            </div>

            <BaseModal
                visible={tipVisible}
                setVisible={settipVisible}
                centered
                onCancel={() => settipVisible(false)}
                width={515}
                title='提示'
                className='loginModal'
            >
                <div className='loginTips'>
                    <div className='noCountImg' />
                    <div>抱歉，您的账号不存在！</div>
                    <span
                        onClick={() => {
                            settipVisible(false);
                            loginForm.resetFields();
                        }}
                        className='returnLoginBtn'
                    >
                        返回登录
                    </span>
                </div>
            </BaseModal>

            <BaseModal
                visible={changePsw}
                setVisible={setchangePsw}
                centered
                okHandle={async () => {
                    try {
                        const { password } = await initPassWordForm.validateFields();
                        initPassWord.mutate(
                            {
                                userId: userId,
                                password: await rsaEncrypt(password)
                            },
                            {
                                onSuccess: async (res: any) => {
                                    console.log('res: ', res);
                                    // loginAction()
                                    // dispatch(loginAction({
                                    //     userInfo: {
                                    //         ...loginUser?.data?.data?.user_info,
                                    //         firstLogin: true
                                    //     },
                                    // }))
                                    // dispatch(resetPassword())
                                    message.success('初始化密码成功');
                                    setchangePsw(false);
                                    initPassWordForm.resetFields();
                                    loginForm.resetFields();
                                    sessionStorage.clear();
                                    fetchCaptcha();
                                    // loginUser.mutate({
                                    //     phoneNumber: await rsaEncrypt(userAccount?.trim()),
                                    //     password: await rsaEncrypt(password.trim())
                                    // });
                                    // navigate('/');
                                }
                            }
                        );
                        initPassWordForm.resetFields();
                    } catch {}
                }}
                onCancelHandle={() => {
                    setchangePsw(false);
                    // dispatch(removeToken())
                    sessionStorage.removeItem('jwt');
                    sessionStorage.removeItem('jwt');
                    initPassWordForm.resetFields();
                }}
                width={515}
                title='修改密码'
                className='changePswModal'
            >
                <div className='changePsw'>
                    <Form
                        name='basic'
                        form={initPassWordForm}
                        labelCol={{
                            span: 5
                        }}
                        labelAlign='left'
                        wrapperCol={{
                            span: 17
                        }}
                        onFinish={onChangeFinish}
                        autoComplete='off'
                    >
                        <Form.Item
                            label='新密码'
                            name='password'
                            rules={[
                                {
                                    required: true,
                                    message: ''
                                },
                                () => ({
                                    validator: (_, value, callback) => {
                                        const regExp = new RegExp(
                                            /^(?=.*[A-Z].*)(?=.*[a-z].*)(?=.*\d)(?=.*[!#$%])[A-Za-z\d!#$%]{8,20}$/
                                        );
                                        const verify = regExp.test(value);
                                        if (!value) {
                                            callback('请填写密码');
                                        } else if (verify === false) {
                                            callback('新密码不符合密码规则！');
                                        } else {
                                            callback();
                                        }
                                    }
                                })
                            ]}
                        >
                            <Input.Password
                                onChange={() => {
                                    initPassWordForm.validateFields(['enterPsw']);
                                }}
                            />
                        </Form.Item>
                        <Form.Item
                            label='确认密码'
                            name='enterPsw'
                            rules={[
                                {
                                    required: true,
                                    message: '请再次输入密码'
                                },
                                ({ getFieldValue }) => ({
                                    validator(rule, value) {
                                        if (!value || getFieldValue('password') === value) {
                                            return Promise.resolve();
                                        }
                                        return Promise.reject('两次密码输入不一致！');
                                    }
                                })
                            ]}
                        >
                            <Input.Password />
                        </Form.Item>
                    </Form>
                    <div className='loginpswTips'>
                        <div>密码规则：</div>
                        <div>1.长度为8-20位</div>
                        <div>2.必须包含数字0-9</div>
                        <div>3.必须包含一位大写字母和一位小写字母</div>
                        <div>4.必须至少包含特殊字符!#$%中的一个</div>
                    </div>
                </div>
            </BaseModal>
            <span
                className='versionInfo'
                style={{ opacity: versionInfo ? 1 : 0 }}
                onClick={() => {
                    setversionInfo(!versionInfo);
                }}
            >
                v1.0.5{process.env.MY_VERSION}
            </span>
        </div>
    );
}

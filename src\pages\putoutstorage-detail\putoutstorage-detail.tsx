/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-13 16:00:50
 * @LastEditTime: 2022-11-01 18:17:21
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BaseCollapse from '@components/base-collapse';
import BaseFormItem from '@components/base-form-item';
import BaseModal from '@components/base-modal';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';

import { storageOutDetail } from '@services/storage-out';
import dayjs from 'dayjs';
import TableHead from '@components/table-head';

import { Form, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { useLocation } from 'react-router-dom';
import FilterForm from '@components/filter-form/filter-form';
import ChainDetailModal from '@components/chain_detail_modal';

const PutOutStorageDetail = () => {
    const { state } = useLocation();
    console.log('state', state);
    const [SourceForm] = Form.useForm();
    const [FoodForm] = Form.useForm();
    const [ChainForm] = Form.useForm();

    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);

    const storageoutDetail = useQuery(
        ['storageoutDetail'],
        () => {
            return storageOutDetail({
                outWarehouseId: state?.data?.id
            });
        },
        {
            onSuccess(res) {
                console.log(res);
                SourceForm.setFieldsValue({
                    ...res?.data,
                    provinces: res?.data?.provinces?.map((item: any) => item?.province).join(',')
                });
            }
        }
    );

    useEffect(() => {}, []);

    const PutOutStorage = storageoutDetail?.data?.data;

    const SourceInfoConfig = [
        {
            label: '出库单号',
            name: 'outWarehouseNumber',
            value: 'outWarehouseNumber',
            type: 'ShowText',
            span: 8
        },
        {
            label: '经销商',
            name: 'dealer',
            value: 'dealer',
            type: 'ShowText',
            span: 8
        },
        {
            label: '经销区域',
            name: 'provinces',
            value: 'provinces',
            type: 'ShowText',
            span: 8
        },
        {
            label: '销售渠道',
            name: 'salesChannels',
            value: 'salesChannels',
            type: 'ShowText',
            span: 8
        },
        {
            label: '订单号',
            name: 'orderNumber',
            value: 'orderNumber',
            type: 'ShowText',
            span: 8
        },
        {
            label: '物流企业',
            name: 'logisticsEnterprises',
            value: 'logisticsEnterprises',
            type: 'ShowText',
            span: 8
        },
        {
            label: '物流单号',
            name: 'logisticsNumber',
            value: 'logisticsNumber',
            type: 'ShowText',
            span: 8
        }
    ];
    const tableData = PutOutStorage?.boxTo.map((item: any) => ({
        boxCode: item?.boxCode,
        productName: item?.productName
    }));

    const ChainDetailModalConfig = {
        transactionId: PutOutStorage?.transactionId,
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };

    const Storingfood = [
        {
            title: '箱码',
            dataIndex: 'boxCode',
            key: 'boxCode'
        },
        {
            title: '产品名称',
            dataIndex: 'productName',
            key: 'productName'
        }
    ];

    const chainInfoConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            type: 'custom',
            span: 24,
            tooltip: '信息的链上的哈希值',
            write: 'ture',
            children: <a onClick={() => setChainDetailModalVisible(true)}>{PutOutStorage?.transactionId}</a>
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            type: 'Display',
            span: 24,
            tooltip: '信息上链的时间',
            displayDom: dayjs(PutOutStorage?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
        }
    ];
    return (
        <>
            <BaseCard title={<PageTitle title='出库单详情' />}>
                <Form form={SourceForm} className='edit-label-title'>
                    <PageTitle title='出库单信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={SourceInfoConfig} />
                </Form>
                <Form form={FoodForm}>
                    <PageTitle title='箱码信息' type='primaryIcon' />
                    <BaseTable
                        style={{ width: '50%' }}
                        className='baseTable-title-nopadding'
                        rowKey='account'
                        btnDisplay={(checkData: any, resetSelect: any) => {
                            return (
                                <TableHead
                                    LeftDom={<div></div>}
                                    RightDom={
                                        <div
                                            style={{
                                                display: 'flex'
                                            }}
                                        ></div>
                                    }
                                />
                            );
                        }}
                        columns={Storingfood}
                        dataSource={tableData}
                    />
                </Form>
                <Form form={ChainForm}>
                    <PageTitle title='区块链信息' type='primaryIcon' size={25} bmagin={16} />
                    <BaseFormItem configs={chainInfoConfig} />
                </Form>
            </BaseCard>
            <ChainDetailModal {...ChainDetailModalConfig} />
        </>
    );
};

export default PutOutStorageDetail;

export const SourceInfoConfig = [
    {
        label: '原料名称',
        name: 'materialName',
        value: '123',
        type: 'Display',
        span: 8
    },
    {
        label: '数量',
        name: 'count',
        value: '123',
        type: 'Display',
        span: 8
    },
    {
        label: '批次号',
        name: 'batchNumber',
        value: '123',
        type: 'Display',
        span: 8
    },
    {
        label: '规格',
        name: 'specification',
        value: '123',
        type: 'Display',
        span: 8
    },
    {
        label: '生产日期',
        name: 'productionDate',
        value: '123',
        type: 'Display',
        span: 8
    },
    {
        label: '保质期',
        name: 'expirationDate',
        value: '123',
        type: 'Display',
        span: 8
    },
    {
        label: '产品合格证明材料',
        name: 'certificateQualification',
        value: '123',
        type: 'Url',
        tips: '下载',
        span: 8
    },
    {
        label: '生产批次',
        name: 'batch',
        value: '123',
        type: 'Modal',
        span: 8,
        tips:'点击查看'
    }
];

export const chainInfoConfig = [
    // {
    //     label: '区块号',
    //     name: 'blockNum',
    //     value: '123',
    //     type: 'Display',
    //     span: 24,
    //     tooltip: '信息上链时所在的区块编号'
    // },
    {
        label: '链上哈希',
        name: 'transactionId',
        value: '123',
        type: 'Display',
        span: 24,
        tooltip: '信息的链上的哈希值'
    },
    {
        label: '上链时间',
        name: 'transactionTime',
        value: '123',
        type: 'Display',
        span: 24,
        tooltip: '信息上链的时间'
    }
];

export const ProductInfoConfig = [
    {
        label: '食品名称',
        name: 'foodName',
        value: '123',
        type: 'Display',
        span: 8
    },
    {
        label: '生产批次',
        name: 'productionBatch',
        value: '123',
        type: 'Display',
        span: 8
    },
    {
        label: '生产日期',
        name: 'productionDate',
        value: '123',
        type: 'Display',
        span: 8
    },
    {
        label: '数量',
        name: 'count',
        value: '123',
        type: 'Display',
        span: 8
    }
];

export const QsqaInfoConfig = [
    {
        label: '食品名称',
        name: 'foodName',
        value: '123',
        type: 'Display',
        span: 8
    },
    {
        label: '生产批次号',
        name: 'productionBatch',
        value: '123',
        type: 'Display',
        span: 8
    },
    {
        label: '质检时间',
        name: 'testTime',
        value: '123',
        type: 'Display',
        span: 8
    },
    {
        label: '质检内容',
        name: 'testRecord',
        value: '123',
        type: 'Display',
        span: 8
    },
    {
        label: '质检结果',
        name: 'testResult',
        value: '123',
        type: 'Status',
        status: 'SuccessStatus',
        span: 8
    },
    {
        label: '质检报告',
        name: 'testReport',
        value: '123',
        type: 'Url',
        tips: '下载附件',
        span: 8
    }
];

export const ProductSourceInfoConfig = [
    {
        label: '辣椒',
        name: 'code',
        value: '123',
        type: 'Display',
        span: 12
    },
    {
        label: '盐',
        name: 'package',
        value: '123',
        type: 'Display',
        span: 12
    },
    {
        label: '糖',
        name: 'time',
        value: '123',
        type: 'Display',
        span: 12
    },
    {
        label: '油',
        name: 'time',
        value: '123',
        type: 'Display',
        span: 12
    }
];

.cardTips {
    color: #9b9b9b;
    background: #fff;
    border-radius: 5px;
    .titleNum {
        // color: #43425d;
        font-size: 30px;
        margin: 10px 0px 28px 0px;
        font-weight: 600;
    }
    .monthDes {
        margin-right: 14px;
    }
    .monthNum {
        // color: #333;
        font-weight: 600;
    }
    .dayDes {
        // color: #5b5b69;
        margin-right: 12px;
    }
    .dayNum {
        font-weight: 600;
        // color: #43425d;
    }
    .ant-divider {
        margin: 22px 0px 12px 0px;
        border-top: none !important;
    }
    .increaseIcon {
        color: #f5222d;
        width: 15px;
        height: 15px;
        margin-left: 10px;
        margin-top: 5px;
    }
    .ant-card-body {
        // padding: 25px 0px 13px 24px;
        padding: 0 0px 13px 24px;
    }
}
.columnTitleIcon {
    color: #80a932;
    font-size: 18px;
    // margin: 15px 0px 11px 0px;
    box-sizing: border-box;
    // padding-left: 40px;
    width: 170px;
    // background: url('../../assets/landicon/suyuan.png') no-repeat;
    background: url('https://gd-hbimg.huaban.com/5391199113a959ac476c57a327549224cfa3bea0e0a7-eGHHey_fw658') no-repeat;
    background: url('../../assets/home/<USER>') no-repeat;
    background-size: 100% 100%;
    font-weight: 600;
}
.columnAddIcon {
    display: flex;
    color: #80a932;
    font-size: 18px;
    // margin: 15px 0px 11px 0px;
    box-sizing: border-box;
    // padding-left: 40px;
    font-weight: 600;
    width: 150px;
    // background: url('../../assets/landicon/add.png') no-repeat;
    background: url('../../assets/home/<USER>') no-repeat;
    background-size: 100% 100%;
}
.fsz {
    height: 90px;
    width: 100%;
    text-align: center;
    line-height: 90px;
    font-size: 30px;
    margin-top: 15px;
    background: #fdeec9;
    border: 2px solid #ffb200;
    color: #ffb200;
    border-radius: 8px;
}

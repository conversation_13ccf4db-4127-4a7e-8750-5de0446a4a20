import { useQuery } from 'react-query';
/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-08 09:47:50
 * @LastEditTime: 2022-11-01 18:26:39
 * @LastEditors: PhilRandWu
 */
// import { requestfoodList } from '../mock/food-list';
import { useEffect, useState } from 'react';

interface searchConditionInterFace {
    pageIndex?: number;
    pageSize?: number;
}

export const useFoodList = function ({ pageIndex, pageSize }: searchConditionInterFace) {
    console.log('foodListRes');
    const [foodList, setFoodList] = useState([]);
    const [loading, setloading] = useState(true);
    useEffect(() => {
        (async () => {
            // const data: any = await requestfoodList();
            // const formatData = data?.map((item: any) => ({
            //     ...item,
            //     type: item?.type && item?.type?.join('>'),
            //     address: '产地A',
            //     addressDetail: '江苏省南京市',
            //     belong: '辣椒酱'
            // }));
            // setFoodList(formatData);
            setloading(false);
        })();
    }, [pageIndex, pageSize]);
    return {
        isLoading: loading,
        data: foodList
    };
};

FROM artifactory.dep.devops.cmit.cloud:20101/tools/base-images/node:14.18-alpine-v1 AS builder

ARG PROJECT_PATH
WORKDIR /app
COPY ./${PROJECT_PATH}/package-lock.json ./
COPY ./${PROJECT_PATH}/package.json ./
RUN npm config set registry https://artifactory.dep.devops.cmit.cloud:20101/artifactory/api/npm/npm/
RUN npm config set sass_binary_site=https://artifactory.dep.devops.cmit.cloud:20101/artifactory/npm-remote-nodesass/
RUN npm config set disturl=https://artifactory.dep.devops.cmit.cloud:20101/artifactory/npm/node/
RUN npm install
COPY ./${PROJECT_PATH}/. .
RUN npm run build

FROM artifactory.dep.devops.cmit.cloud:20101/tools/base-images/nginx:alpine AS final
COPY --from=builder /app/nginx/app.conf /etc/nginx/conf.d/app.template
COPY --from=builder /app/nginx/nginx.conf /etc/nginx/nginx.conf
COPY --from=builder /app/build /usr/share/nginx/html
ENV CM_RICETRACE_WEB_SERVER_HOST=http://*************:9013
ENV CM_RICETRACE_WEB_HOST=http://*************:4000
ENV RICETRACE_WEB_URL=http://baidu.com
EXPOSE 80
WORKDIR /etc/nginx/conf.d/
ENTRYPOINT envsubst '${CM_RICETRACE_WEB_SERVER_HOST}  ${CM_RICETRACE_WEB_HOST}' < app.template > app.conf && cat app.conf && cp /usr/share/nginx/html/config.json /usr/share/nginx/html/config.template && envsubst '${RICETRACE_WEB_URL}' < /usr/share/nginx/html/config.template > /usr/share/nginx/html/config.json && nginx -g 'daemon off;'

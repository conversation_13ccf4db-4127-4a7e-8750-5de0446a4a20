/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 18:07:55
 * @LastEditTime: 2024-09-25 10:11:55
 * @LastEditors: 吴山仁
 */

import BaseButton from '@components/base-button';
import BaseCard from '@components/base-card';
import FilterForm from '@components/filter-form';
import { useNavigate } from 'react-router-dom';
import { addQuality, selectBatchList, selectProductList } from '@services/quality-test';
import { useMutation, useQuery } from 'react-query';
import { productionBatchList } from '@services/storage-in';
import { signData } from '../../utils/blockChainUtils';
import { ReformChainError } from '@utils/errorCodeReform';
import { getLocalPrivatekey } from '@utils/blockChainUtils';
import PageTitle from '@components/page-title';
import { But<PERSON>, Divider, Form, FormInstance, message, Upload } from 'antd';
import React, { Component, useState, useRef } from 'react';
import { qcqaInfoConfigs } from './config';
import { useLocation } from 'react-router-dom';
import styles from './index.module.less';
import { useDispatch } from 'react-redux';
import ImgCropUpload from '@components/img-upload';

import { fileUpload, getFileUrlFormUploadedFile } from '@utils';
import { UploadOutlined } from '@ant-design/icons';
import PublicSourceService from '@services/traceability_data/public_source';
import { RoleEnum } from '@config';
const QcqaAdd = () => {
    const [form] = Form.useForm();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const { state } = useLocation();
    const [uploadImageUrl, setUploadImageUrl]: any = useState('');
    const [isUpLoading, setIsUpLoading]: any = useState();
    const [upfile, setupfile]: any = useState('');
    const data = useRef(true);
    console.log('state', state);
    const [foodId, setFoodId] = useState('');
    //新增
    const addquality = useMutation(addQuality, {
        onSuccess(res) {
            message.success('新增成功');
            navigate('/qcqa/list');
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    const disabledTime = () => {
        return data;
    };
    // 选择产品
    const selectProductsQuery = useQuery(
        ['selectProductsQuery'],
        () => {
            return selectProductList({
                valid: true
            });
        },
        {
            onSuccess(res) {
                getLocalPrivatekey(dispatch);
            }
        }
    );
    // 选择生产批次
    const selectBatchQuery = useQuery(
        ['selectBatchQuery', foodId],
        () => {
            return selectBatchList({
                id: form.getFieldValue('productId')
            });
        },
        {
            onSuccess(res) {
                getLocalPrivatekey(dispatch);
            },
            enabled: !!form.getFieldValue('productId')
        }
    );
    console.log('canInFoodlist', selectProductsQuery);
    const mapToEnum: any = {};
    (selectProductsQuery?.data?.data || [])?.forEach((item: any, index: any) => {
        // console.log(7777777,item)
        mapToEnum[item.food_name] = item.food_id;
    });
    const mapToEnum_2: any = {};
    (selectBatchQuery?.data?.data || [])?.forEach((item: any, index: any) => {
        // console.log(7777777,item)
        mapToEnum_2[item.production_batch] = item.id;
    });

    const productVideoAcceptTypes = ['.rar', '.zip', '.doc', '.docx', '.pdf', '.jpg'];
    const handleBeforeProductVideoUpload = (file: any) => {
        const fileType = file?.name?.split('.').at(-1).toLowerCase();
        console.log(fileType, file, 'fffffff');
        if (!productVideoAcceptTypes.includes('.' + fileType)) {
            message.error('文件格式不正确');
            return Upload.LIST_IGNORE;
        }
        if (file.size / 1024 / 1024 > 20) {
            message.error('附件最大上传20MB');
            return Upload.LIST_IGNORE;
        }
        return true;
    };
    //
    const queryOrgList = useQuery(['queryOrgList'], () =>
        PublicSourceService.getOrgList({
            identity: RoleEnum['质检机构']
        })
    );
    const queryOrgListData = queryOrgList?.data?.data;
    console.log(queryOrgListData);

    const qcqaInfoConfigs = [
        {
            type: 'Select',
            label: '产品名称',
            value: 'productId',
            placeholder: '请选择产品',
            span: 12,
            showSearch: 'showSearch',
            className: 'rawMaterial',
            required: 'required',
            filterOption: (inputValue: any, option: any) => {
                console.log(inputValue, option);
                return option?.children?.toLowerCase()?.indexOf(inputValue?.toLowerCase()) !== -1;
            },
            rules: [{ required: true, message: '请输入选择产品!' }],
            onChange: (option: any, input: any) => {
                //监听数据变化
                setFoodId(option);
                // if (option !== undefined) {
                //     selectBatchQuery.mutate({
                //         foodId: mapToEnum[option]
                //     });
                // }
                form.setFieldsValue({ productionId: null }); //清空生产批次输入框
                console.log('optionoption', option, form.getFieldValue('productId'));
            },
            fields: [
                ...(selectProductsQuery?.data?.data || [])?.map((item: any, index: any) => {
                    const materialdata = {
                        value: item.id,
                        label: item.productName
                    };
                    return materialdata;
                })
            ]
        },
        {
            type: 'Select',
            label: '生产批次',
            value: 'productionId',
            placeholder: foodId ? '请选择生产批次' : '请先选择产品名称',
            name: 'productionId',
            disabled: !foodId,
            rules: [{ required: true, message: '请输入生产批次!' }],
            span: 12,
            // showSearch: 'showSearch',
            className: 'rawMaterial',
            fields: [
                //数据列表
                ...(foodId
                    ? (selectBatchQuery?.data?.data || []).map((item: any, index: any) => {
                          // 循环增加数据
                          const materialdata = {
                              value: item.id,
                              label: item.productionBatch
                          };
                          console.log('materialdata', materialdata);
                          return materialdata;
                      })
                    : [])
            ]
        },
        {
            type: 'Input',
            label: '质检机构',
            value: 'inspectionOrgName',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback('请输入质检机构！');
                        } else if (value[0] === ' ' || value[value.length - 1] === ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 40) {
                            callback('请保持字符在40字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入质检机构',
            className: 'count',
            span: 12
        },
        // {
        //     type: 'Select',
        //     label: '质检机构',
        //     value: 'inspectionResults1',
        //     rules: [{ required: true, message: '请输入!' }],
        //     placeholder: '请选择',
        //     span: 12,
        //     className: 'rawMaterial',
        //     fields: [
        //         ...(queryOrgList?.data?.data || []).map((item: any, index: any) => {
        //             // 循环增加数据
        //             const materialdata = {
        //                 value: item.id,
        //                 label: item.shortName
        //             };
        //             console.log('materialdata', materialdata);
        //             return materialdata;
        //         })
        //     ]
        // },
        {
            type: 'Input',
            label: '质检内容',
            value: 'inspectionContent',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback('请输入质检内容！');
                        } else if (value[0] === ' ' || value[value.length - 1] === ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入质检内容',
            className: 'count',
            span: 12
        },
        {
            type: 'Select',
            label: '质检结果',
            value: 'inspectionResults',
            rules: [{ required: true, message: '请输入!' }],
            placeholder: '请选择',
            span: 12,
            className: 'rawMaterial',
            fields: [
                {
                    value: 1,
                    label: '合格'
                },
                {
                    value: 2,
                    label: '不合格'
                }
            ]
        },

        {
            type: 'Custom',
            label: '质检报告',
            value: 'inspectionReport',
            rules: [{ required: true, message: '请上传质检报告!' }],
            span: 12,
            children: (
                <ImgCropUpload
                    setIsUpLoading={setIsUpLoading}
                    isUpLoading={isUpLoading}
                    isCrop={false}
                    maxAmount={1}
                    tips={'大小不超过5MB'}
                ></ImgCropUpload>
            ),
            placeholder: '大小不超过5MB'
        },
        {
            type: 'Custom',
            label: '附件',
            value: 'inspectionAccessory',
            placeholder: '支持扩展名：.rar .zip .doc .docx .pdf .jpg...',
            span: 12,
            children: (
                <Form.Item noStyle>
                    <div className={styles.prodPic}>
                        <Form.Item
                            name='inspectionAccessory'
                            rules={[
                                {
                                    required: false,
                                    message: '请上传'
                                }
                            ]}
                            extra={
                                <div style={{ color: '#333', width: 280 }}>
                                    支持扩展名：.rar .zip .doc .docx .pdf .jpg...
                                </div>
                            }
                            valuePropName='fileList'
                            getValueFromEvent={(e: any) => {
                                if (Array.isArray(e)) {
                                    return e;
                                }
                                return e && e.fileList;
                            }}
                        >
                            <Upload
                                accept={productVideoAcceptTypes.join(',')}
                                customRequest={({ file, onError, onProgress, onSuccess }) => {
                                    // @ts-ignore
                                    fileUpload({
                                        ...{ file, onError, onProgress, onSuccess },
                                        isUploading: isUpLoading,
                                        setIsUpLoading: setIsUpLoading
                                    });
                                }}
                                beforeUpload={handleBeforeProductVideoUpload}
                                maxCount={1}
                            >
                                <Button icon={<UploadOutlined rev={undefined} />}>上传文件</Button>
                            </Upload>
                        </Form.Item>
                    </div>
                </Form.Item>
            )
        }
    ];
    const onFieldsChange = (values: any, errorFields: any) => {
        console.log('test111', '触发', values);
        // if(values[0].name[0]==='productName'){
        //     form.setFieldsValue({productionBatch: null })      //清空生产批次输入框
        // }else{
        // }
    };
    const onValuesChange = (changedValues: any, allValues: any) => {
        console.log('test222', '触发', changedValues);
        if (changedValues?.productName) {
            form.setFieldsValue({ productionBatch: null }); //清空生产批次输入框
        }
    };
    const onFinish = (values: any) => {
        if (isUpLoading) {
            message.warning('正在上传文件请稍等～');
            return;
        }
        console.log('values', values);
        const params: any = {
            inspectionAccessory: getFileUrlFormUploadedFile(values?.inspectionAccessory)?.[0],
            inspectionContent: values?.inspectionContent,
            inspectionReport: getFileUrlFormUploadedFile(values?.inspectionReport)?.[0],
            inspectionResults: values?.inspectionResults,
            productId: values?.productId,
            productionId: values?.productionId,
            inspectionOrgName: values?.inspectionOrgName
        };
        console.log('params888888888', JSON.stringify(params));
        const paramStr = JSON.stringify(params);
        signData(dispatch, JSON.stringify(params), (error, result: any) => {
            if (!error && result) {
                addquality.mutate({
                    addQualityVo: params,
                    paramStr: paramStr,
                    signature: result
                });
            } else if (error !== 'misprivatekey') {
                message.info('签名异常，请重试或联系管理员');
            }
        });
    };
    return (
        <BaseCard title={<PageTitle title='新建质检' bg='container zhi' />}>
            <Form
                onFinish={onFinish}
                onFieldsChange={onFieldsChange}
                form={form}
                onValuesChange={onValuesChange}
                className='edit-label-title custom-form-item-height'
            >
                <FilterForm itemConfig={qcqaInfoConfigs} labelCol={6} wrapperCol={9} />

                <div className={styles.addBtnContainer}>
                    <Form.Item className={styles.saveBtn}>
                        <BaseButton type='primary' htmlType='submit' className={styles.submitBtn}>
                            提交
                        </BaseButton>
                    </Form.Item>
                    <Form.Item>
                        <BaseButton
                            htmlType='button'
                            type='dashed'
                            className={styles.primaryBtn}
                            onClick={() => {
                                navigate('/qcqa/list');
                            }}
                        >
                            取消
                        </BaseButton>
                    </Form.Item>
                </div>
            </Form>
        </BaseCard>
    );
};
export default QcqaAdd;

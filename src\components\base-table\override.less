.ant-table {
    background: transparent;
    .ant-table-title {
        display: flex;
        // justify-content: space-between;
        align-items: center;
        padding: 0;
        font-size: 14px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
    }
    .ant-table-container .ant-table-content {
        background: #fcfdfd !important;
    }

    .ant-table-thead .ant-table-cell {
        background-color: #e3f1e3;
        font-size: 14px;
        font-weight: 600;
        color: #333333;

        &::before {
            display: none;
        }
    }

    .ant-table-tbody .ant-table-row .ant-table-cell {
        font-size: 14px;
        color: #666666;
        position: relative;
        &::after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 1px;
            background-color: #95e20757;
            transform: scaleY(0.5);
        }
    }
    .ant-table-tbody .ant-table-row:nth-child(odd) {
        background-color: #f2f8f2 !important; /* 偶数行背景颜色 */
    }
}
.ant-card {
    background: transparent !important;
}
.ant-table-thead {
    background: transparent !important;
}
// .ant-table-row{
//   border: 1px solid #7fa93257 !important;
// }
// .base-table .ant-pagination-item,
// .base-table button.ant-pagination-item-link {
//   background: linear-gradient(135deg, #e1e7ec 0%, #ffffff 100%);
//   box-shadow: 0px 2px 4px 0px #e8edf5;
//   border-radius: 2px;
//   border: 1px solid #ffffff !important;
// }
// .base-table .ant-pagination-item-active {
//   background: #3d73ef;
// }
// .base-table .ant-pagination-item-active a {
//   color: white;
// }
// .base-table .ant-pagination-total-text {
//   font-size: 14px;
//   font-weight: 400;
//   color: #666666;
//   margin-right: 12px;
// }
// /* reset sizeChanger */
// .base-table .ant-pagination-options-size-changer {
//   background: linear-gradient(135deg, #e1e7ec 0%, #ffffff 100%) !important;
//   box-shadow: 0px 2px 4px 0px #e8edf5 !important;
//   border-radius: 2px !important;
//   border: 1px solid #ffffff !important;
// }
// .base-table .ant-select-selector {
//   border: none !important;
//   background: transparent !important;
// }
// .base-table .ant-select-selection-item {
//   font-size: 14px;
//   color: rgba(0, 0, 0, 0.65);
// }
// /* reset quickJumper */
// .base-table .ant-pagination-options-quick-jumper {
//   font-size: 14px;
//   font-weight: 400;
//   color: #666666;
// }
// .base-table .ant-pagination-options-quick-jumper input {
//   background: linear-gradient(135deg, #e1e7ec 0%, #ffffff 100%) !important;
//   box-shadow: 0px 2px 4px 0px #e8edf5 !important;
//   border-radius: 2px !important;
//   border: 1px solid #ffffff !important;
// }

.ant-pagination {
    display: flex;
    justify-content: center;
}
.AppPaginationContainer .ant-pagination-item,
.AppPaginationContainer button.ant-pagination-item-link {
    // background: linear-gradient(135deg, #e1e7ec 0%, #ffffff 100%);
    box-shadow: 0px 2px 4px 0px #e8edf5;
    border-radius: 5px;
    border: 1px solid #D9D9D9 !important;
}
.AppPaginationContainer .ant-pagination-item-active {
    background: #3d73ef;
}
.AppPaginationContainer .ant-pagination-item-active a {
    color: white;
}
.ant-pagination-item a{
    font-family: Helvetica;
color: rgba(77, 79, 92, 0.5);
}
.AppPaginationContainer .ant-pagination-total-text {
    font-size: 14px;
    font-weight: 400;
    color: rgba(77, 79, 92, 0.5);
    margin-right: 12px;
}
.ant-pagination-options-quick-jumper{
    margin: 0 0 0 15px;
    color: rgba(77, 79, 92, 0.5);
}
.ant-pagination-prev .ant-pagination-item-link{
    color: #808495;
}
.AppPaginationContainer {
    padding-top: 24px;
    // text-align: right;
    display: flex;
    justify-content: flex-end;
}
.ant-pagination-options-quick-jumper input{
    width: 35px;
}

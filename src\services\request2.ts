/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-07-11 21:10:02
 * @LastEditTime: 2022-11-01 18:03:08
 * @LastEditors: PhilRandWu
 */
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { message } from 'antd';
import store from '@store';
import { logoutAction } from '@store/slice-reducer/user';

// import errorHandler from "@utils";
const dispatch = store.dispatch;

export const instance = axios.create({
    baseURL: '/api',
});
instance.interceptors.request.use(
    (config) => {
        const Authentication = sessionStorage.getItem('jwt');
        if (Authentication) {
            config.headers.token = Authentication;
        }
        return config;
    },
    (err) => {
        return Promise.reject(err);
    }
);
instance.interceptors.response.use(
    (response: AxiosResponse<any, any>) => {
        return new Promise((resolve, reject) => {
            console.log('instance.interceptors.response', response);

            if (response?.data?.code === 200 || typeof response?.data == 'string') {
                console.log('instance data');
                resolve(response.data);
            } else {
                console.log('instance.interceptors.response reject', response);
                reject(response);
            }
        });
    },
    (err) => {
        // errorHandler(err);
        console.log('response err', err);
        const timeout = err?.config?.timeout;
        if (err?.message === 'Request failed with status code 403') {
            message.error(err?.response?.data?.message)
            localStorage.clear();
            sessionStorage.clear();
            dispatch(logoutAction())
        }
        if (err?.message === 'Request failed with status code 401') {
            message.error('登录凭证失效，请重新登录');
            sessionStorage.removeItem('privateKey');
            message.destroy();
        }
        if (err?.message === `timeout of ${timeout}ms exceeded`) {
            message.error('请求超时');
            message.destroy();
        }
        return Promise.reject(err);
    }
);

export default instance.request;

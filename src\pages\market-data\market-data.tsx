/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-09 16:17:21
 * @LastEditTime: 2022-10-14 10:15:21
 * @LastEditors: PhilRandWu
 */
/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-10-09 16:16:29
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space, Button, Row, Col, DatePicker } from 'antd';
import { useMutation, useQuery } from 'react-query';
import styles from './index.module.less';
import { useRef, useState } from 'react';
import PageTitle from '@components/page-title';
import { SearchOutlined, PlusOutlined, SyncOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import BaseInput from '@components/base-input';
import { Navigate, useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import { ReformChainError } from '@utils/errorCodeReform';
import { ColumnsType } from 'antd/lib/table';
import BaseSelect from '@components/base-select';
import { traceData, getPlantNames } from '@services/market';
import dayjs from 'dayjs';
import BaseDatePicker from '@components/base-date-picker';
import { QueryTime } from '@utils';
const FoodList = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;
    const navigate = useNavigate();
    const querylist: any = useRef('');
    const [search]: any = Form.useForm();

    const traceDataquery = useQuery(
        ['traceDataquery', pageInfo],
        () => {
            // if (!userInfo.user?.organization_id) {
            //     message.error('未获取到机构id');
            //     return Promise.reject('未获取到机构id');
            // }
            const TimeArr = QueryTime(querylist?.current?.purchaseTime);
            console.log(TimeArr);
            return traceData({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                purchaseBatch: querylist?.current?.purchaseBatch,
                // purchaseTime: querylist?.current?.purchaseTime,
                startTime: TimeArr?.[0],
                endTime: TimeArr?.[1],
                plantName: querylist?.current?.plantName
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    console.log('staffquery', traceDataquery);

    const landProductSeleUser = useQuery(['landProductSeleUser'], () => getPlantNames());

    const landProductSeleUserData: any[] = landProductSeleUser?.data?.data;
    // const tableData = traceDataquery?.data?.data?.records;
    const tableData = traceDataquery?.data?.data?.records?.map((item: any) => ({
        purchaseBatch: item?.purchaseBatch,
        productName: item?.productName,
        productionBatch: item?.productionBatch,
        id: item?.id,
        plantName: item?.plantName,
        purchaseWeight: item?.purchaseWeight,
        purchaseUnitPrice: item?.purchaseUnitPrice,
        userName: item.userName,
        purchaseTime: dayjs(item.purchaseTime).format('YYYY-MM-DD HH:mm:ss')
    }));
    const listColumn: ColumnsType<any> = [
        {
            title: '生产批次',
            dataIndex: 'productionBatch',
            key: 'productionBatch',
            ellipsis: true
        },
        {
            title: '产品名称',
            dataIndex: 'productName',
            key: 'productName',
            ellipsis: true
        },
        {
            title: '收购批次',
            dataIndex: 'purchaseBatch',
            key: 'purchaseBatch',
            ellipsis: true
        },
        {
            title: '收购时间',
            dataIndex: 'purchaseTime',
            key: 'purchaseTime',
            ellipsis: true
        },
        {
            title: '收购重量(吨)',
            dataIndex: 'purchaseWeight',
            key: 'purchaseWeight',
            ellipsis: true
        },
        {
            title: '收购单价(元/吨)',
            dataIndex: 'purchaseUnitPrice',
            key: 'purchaseUnitPrice',
            ellipsis: true
        },
        {
            title: '农作物类型',
            dataIndex: 'plantName',
            key: 'plantName',
            ellipsis: true
        },

        // {
        //     title: '状态',
        //     dataIndex: 'state',
        //     key: 'state',
        //     ellipsis: true,
        //     render: (data: any) => (
        //         <span style={{ color: data ? '#F64041' : '#666666' }}>
        //             <Badge
        //                 status={data ? 'error' : 'success'}
        //                 color={data ? '#F64041' : 'rgb(36, 171, 59)'}
        //                 text={data ? '禁用' : '可用'}
        //             />
        //         </span>
        //     )
        // },
        {
            width: 210,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle' className='operation'>
                    {/* <BaseButton
                        // type='dashed'
                        className={record.state ? 'primaryBtn' : 'warnBtn'}
                        // ghost
                        onClick={() => {
                            // console.log("record",record)
                            const opp = foodstate.mutate({
                                opt: record?.state ? 'ENABLE' : 'DISABLE',
                                id: record?.id
                            });
                        }}
                    >
                        {record.state ? '启用' : '禁用'}
                    </BaseButton>
                    <BaseButton
                        type='text'
                        className='editBtn'
                        onClick={() => {
                            const foodid = record?.id;
                            navigate('edit', {
                                state: {
                                    id: foodid
                                }
                            });
                        }}
                    >
                        编辑
                    </BaseButton> */}
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            const foodid = record?.id;
                            navigate('detail', {
                                state: {
                                    id: foodid,
                                    productionBatch: record.productionBatch
                                }
                            });
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    const searchConfig = {
        label: '',
        classname: 'searchConfig-input',
        handleSearch: (values: any) => {
            handlePaginationChange(1);
            traceDataquery.refetch();
        },
        placeholder: '输入产品编号/产品名称',
        setSearchValue: (values: any) => {
            // console.log("values",values)
            querylist.current = values;
        }
    };

    return (
        <>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
                title={<PageTitle title='收购溯源列表' bg='container chan' />}
            >
                <div
                // style={{
                //     display: 'flex',
                //     width: '100%'
                // }}
                >
                    {/* <SingleSearch {...searchConfig} /> */}
                    <Form
                        onFinish={(values) => {
                            handlePaginationChange(1);
                            console.log('values', values);
                            querylist.current = values;
                            traceDataquery.refetch();
                        }}
                        form={search}
                        labelCol={{ span: 6 }}
                        className='label-title'
                    >
                        {/* <SearchForm /> */}
                        <Row
                            gutter={[24, 0]}
                            style={{
                                width: '100%'
                            }}
                        >
                            <Col span={6}>
                                <Form.Item label='收购批次' name='purchaseBatch'>
                                    <BaseInput placeholder='请输入收购批次'></BaseInput>
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <Form.Item label='收购时间' name='purchaseTime'>
                                    <BaseDatePicker style={{ width: '100%' }}></BaseDatePicker>
                                    {/* <DatePicker /> */}
                                </Form.Item>
                            </Col>
                            <Col span={7}>
                                <Form.Item label='农作物类型' name='plantName'>
                                    <BaseSelect
                                        placeholder='请选择农作物类型'
                                        options={landProductSeleUserData?.map((item) => ({
                                            label: item?.plantName,
                                            value: item?.plantName
                                        }))}
                                    ></BaseSelect>
                                </Form.Item>
                            </Col>
                            <Col span={4}>
                                <div
                                    style={{
                                        display: 'flex',
                                        justifyContent: 'end',
                                        marginLeft: 10
                                    }}
                                >
                                    <Space>
                                        <BaseButton
                                            htmlType='submit'
                                            type='primary'
                                            // className='searchBtn'
                                            // style={{width: 100}}
                                            className={`${styles.searchBtn} ${styles.baseBtn}`}
                                            icon={<SearchOutlined rev={undefined} />}
                                        >
                                            查询
                                        </BaseButton>
                                        <BaseButton
                                            type='dashed'
                                            className='primaryBtn'
                                            // style={{width: 100}}
                                            icon={<SyncOutlined rev={undefined} />}
                                            onClick={() => {
                                                querylist.current = null;
                                                traceDataquery.refetch();
                                                search.resetFields();
                                            }}
                                        >
                                            重置
                                        </BaseButton>
                                        {/* <BaseButton
                              type='dashed'
                              icon={<PlusOutlined rev={undefined} />}
                              className='greenBtn'
                              onClick={() => {
                                  navigate('/qcqa/list/add');
                              }}
                          >
                              新建质检
                          </BaseButton> */}
                                    </Space>
                                </div>
                            </Col>
                            {/* <Col span={4}>
                                <div style={{ textAlign: 'right' }}>
                                    <BaseButton
                                        type='dashed'
                                        icon={<PlusOutlined rev={undefined} />}
                                        className='bgBtn'
                                        onClick={() => {
                                            navigate('add');
                                        }}
                                    >
                                        新建收购
                                    </BaseButton>
                                </div>
                            </Col> */}
                        </Row>
                    </Form>
                </div>
                <BaseTable
                    rowKey='account'
                    className='food-table-operation'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return <TableHead />;
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={traceDataquery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={traceDataquery?.data?.data?.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>
        </>
    );
};

export default WithPaginate(FoodList);

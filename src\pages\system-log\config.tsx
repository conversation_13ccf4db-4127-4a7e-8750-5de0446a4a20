import { RoleEnum } from '@config';

export const actionType: any = {
    [RoleEnum.平台方]: {
        1: '新建',
        2: '编辑',
        3: '禁用',
        4: '启用',
        5: '作废',
        // 6: '激活',
        // 7: '卸货',
        8: '配置',
        // 9: '关联',
        10: '重置'
    },
    [RoleEnum.生产加工企业]: {
        1: '新建',
        2: '编辑',
        3: '禁用',
        4: '启用',
        5: '作废',
        // 6: '激活',
        // 7: '卸货',
        8: '配置',
        // 9: '关联',
        10: '重置'
    },
    [RoleEnum.供应商]: {
        1: '新建',
        2: '编辑',
        3: '禁用',
        4: '启用',
        5: '作废',
        10: '重置'
    },
    [RoleEnum.质检机构]: {
        1: '新建',
        2: '编辑',
        3: '禁用',
        4: '启用',
        10: '重置'
    },
    [RoleEnum.监管机构]: {
        1: '新建',
        2: '编辑',
        3: '禁用',
        4: '启用',
        10: '重置'
    },
    [RoleEnum.物流企业]: {
        1: '新建',
        2: '编辑',
        3: '禁用',
        4: '启用',
        5: '作废',
        // 7: '卸货',
        10: '重置'
    },
    [RoleEnum.运营方]: {
        1: '新建',
        2: '编辑',
        3: '禁用',
        4: '启用',
        5: '作废',
        // 6: '激活',
        // 7: '卸货',
        8: '配置',
        // 9: '关联',
        10: '重置'
    },
    [RoleEnum.农户]: {},
    [RoleEnum.销售企业]: {
        1: '新建',
        2: '编辑',
        3: '禁用',
        4: '启用',
        5: '作废',
        // 6: '激活',
        // 7: '卸货',
        8: '配置',
        // 9: '关联',
        10: '重置'
    }
};

export const moduleType: any = {
    [RoleEnum.平台方]: {
        101: '账号管理',
        102: '企业信息管理',
        103: '员工管理',
        104: '企业管理',
        // 105: '参与方管理',
        106: '产品管理',
        107: '产地管理',
        // 108: '原料管理',
        // 109: '原料采购管理',
        110: '生产过程管理',
        111: '生产加工管理',
        // 112: '箱码管理',
        // 113: '入库管理',
        // 114: '出库管理',
        115: '质检管理',
        // 116: '物流管理',
        117: '溯源码包管理',
        130: '种植管理',
        142: '收购管理',
        143: '销售管理',
        147: '用户反馈',
        149: '仓储管理'
    },
    [RoleEnum.生产加工企业]: {
        102: '企业信息管理',
        103: '员工管理',
        104: '企业管理',
        // 105: '参与方管理',
        106: '产品管理',
        107: '产地管理',
        // 108: '原料管理',
        // 109: '原料采购管理',
        110: '生产过程管理',
        111: '生产加工管理',
        // 112: '箱码管理',
        // 113: '入库管理',
        // 114: '出库管理',
        115: '质检管理',
        // 116: '物流管理',
        117: '溯源码包管理',
        130: '种植管理',
        131: '种植溯源数据',
        142: '收购管理',
        143: '销售管理',
        147: '用户反馈',
        149: '仓储管理',

    },
    [RoleEnum.供应商]: {
        102: '企业信息管理',
        103: '员工管理'
        // 109: '原料采购管理'
    },
    [RoleEnum.质检机构]: {
        102: '企业信息管理',
        103: '员工管理',
        115: '质检管理'
    },
    [RoleEnum.监管机构]: {
        102: '企业信息管理',
        103: '员工管理',
        115: '质检管理'
    },
    [RoleEnum.物流企业]: {
        102: '企业信息管理',
        103: '员工管理',
        116: '物流管理'
    },
    [RoleEnum.运营方]: {
        101: '账号管理',
        102: '企业信息管理',
        103: '员工管理',
        104: '企业管理',
        // 105: '参与方管理',
        106: '产品管理',
        107: '产地管理',
        // 108: '原料管理',
        // 109: '原料采购管理',
        110: '生产过程管理',
        111: '生产加工管理',
        // 112: '箱码管理',
        // 113: '入库管理',
        // 114: '出库管理',
        115: '质检管理',
        // 116: '物流管理',
        117: '溯源码包管理',
        130: '种植管理',
        142: '收购管理',
        143: '销售管理',
        147: '用户反馈',
        149: '仓储管理'
    },
    [RoleEnum.农户]: {},
    [RoleEnum.销售企业]: {
        101: '账号管理',
        102: '企业信息管理',
        103: '员工管理',
        104: '企业管理',
        // 105: '参与方管理',
        106: '产品管理',
        107: '产地管理',
        // 108: '原料管理',
        // 109: '原料采购管理',
        110: '生产过程管理',
        111: '生产加工管理',
        // 112: '箱码管理',
        // 113: '入库管理',
        // 114: '出库管理',
        115: '质检管理',
        // 116: '物流管理',
        117: '溯源码包管理',
        130: '种植管理',
        142: '收购管理',
        143: '销售管理',
        147: '用户反馈',
        149: '仓储管理',
        123: '系统日志'
    }
};

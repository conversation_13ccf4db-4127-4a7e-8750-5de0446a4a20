import { useRef, useState } from 'react';
import { Card, Col, Form, message, Row, Space } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';
import WithPaginate from '../../hoc/withpaginate';

import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';
import BaseInput from '@components/base-input/base-input';
import BaseSelect from '@components/base-select/base-select';
import BaseDatePicker from '@components/base-date-picker';

import { DownOutlined, UpOutlined, SearchOutlined, SyncOutlined } from '@ant-design/icons';

import styles from './index.module.less';
import { logPage } from '@services/log';
import { actionType, moduleType } from './config';
import { useAppDispatch, useAppSelector } from '@store';

import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

interface IUrlState {
    pageIndex: number;
    pageSize: number;
    // ...
}

const SystemLog = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;

    const userInfo = useAppSelector((store) => store.user);
    console.log('userInfo---', userInfo);
    console.log('userInfo.userInfo.identity---', userInfo.userInfo.identity);
    console.log('userInfo--actionType-', actionType, actionType[userInfo.userInfo.identity]);

    const [form] = Form.useForm();
    const navigate = useNavigate();

    const [isSimpleSearch, setIsSimpleSearch] = useState(true);

    const querylist: any = useRef('');

    const logQuery = useQuery(
        ['logQuery', pageInfo],
        () =>
            logPage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                type: querylist?.current?.type,
                userName: querylist?.current?.userName?.trim() || undefined,
                menuId: querylist?.current?.menuId,
                beginTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[0]).startOf('day')).format()
                    : undefined,
                endTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[1]).endOf('day')).format()
                    : undefined
            }),
        {
            onSuccess() {},
            onError() {}
        }
    );

    console.log(logQuery, 'querySystemLog querySystemLog');

    const columns: ColumnsType<any> = [
        {
            title: '操作ID',
            dataIndex: 'id',
            key: 'id',
            ellipsis: true
        },
        {
            title: '操作人',
            dataIndex: 'userName',
            key: 'userName',
            ellipsis: true
        },
        {
            title: '操作时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true,
            render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
        },
        {
            title: '操作模块',
            dataIndex: 'menuName',
            key: 'menuName',
            ellipsis: true
        },
        {
            title: '操作类型',
            dataIndex: 'type',
            key: 'type',
            ellipsis: true,
            render: (type) => {
                return actionType[userInfo.userInfo.identity][type];
            }
        },
        {
            title: '操作内容',
            dataIndex: 'content',
            key: 'content',
            ellipsis: true
        }
    ];

    const searchFormItems = [
        <Form.Item label='操作人' name='userName'>
            <BaseInput placeholder='请输入操作人'></BaseInput>
        </Form.Item>,
        <Form.Item label='操作模块' name='menuId'>
            <BaseSelect
                placeholder='请选择'
                options={Object.keys(moduleType[userInfo.userInfo.identity]).map((key: any) => {
                    return {
                        label: moduleType[userInfo.userInfo.identity][key],
                        value: key
                    };
                })}
            ></BaseSelect>
        </Form.Item>,
        <Form.Item label='操作类型' name='type'>
            <BaseSelect
                placeholder='请选择'
                options={Object.keys(actionType[userInfo.userInfo.identity]).map((key: any) => {
                    return {
                        label: actionType[userInfo.userInfo.identity][key],
                        value: key
                    };
                })}
            ></BaseSelect>
        </Form.Item>,
        <Form.Item label='操作时间' name='createTime'>
            <BaseDatePicker style={{ width: '100%' }}></BaseDatePicker>
        </Form.Item>
    ];

    return (
        <>
            <Card style={{ marginBottom: 10 }} title={<PageTitle title='系统日志' bg='container xi' />}>
                <Form
                    form={form}
                    labelCol={{ span: 5 }}
                    labelAlign='left'
                    onFinish={(values) => {
                        console.log(values, 'values');
                        handlePaginationChange(1);
                        querylist.current = values;
                        logQuery.refetch();
                    }}
                    className='label-title'
                >
                    <Row gutter={[36, 12]}>
                        {searchFormItems.slice(0, isSimpleSearch ? 2 : searchFormItems.length).map((searchFormItem) => (
                            <Col key={searchFormItem.key} span={8}>
                                {searchFormItem}
                            </Col>
                        ))}

                        <Col span={isSimpleSearch ? 8 : 16}>
                            <div style={{ display: 'flex', justifyContent: 'end' }}>
                                <Space>
                                    <BaseButton
                                        type='primary'
                                        htmlType='submit'
                                        icon={<SearchOutlined rev={undefined} />}
                                    >
                                        查询
                                    </BaseButton>
                                    <BaseButton
                                        type='dashed'
                                        className='primaryBtn'
                                        icon={<SyncOutlined rev={undefined} />}
                                        onClick={() => {
                                            querylist.current = null;
                                            logQuery.refetch();
                                            form.resetFields();
                                        }}
                                    >
                                        重置
                                    </BaseButton>
                                    <BaseButton
                                        type='link'
                                        style={{ color: '#80a932' }}
                                        onClick={() => {
                                            setIsSimpleSearch(!isSimpleSearch);
                                        }}
                                    >
                                        {isSimpleSearch ? '展开' : '收起'}
                                        {isSimpleSearch ? (
                                            <DownOutlined rev={undefined} />
                                        ) : (
                                            <UpOutlined rev={undefined} />
                                        )}
                                    </BaseButton>
                                </Space>
                            </div>
                        </Col>
                    </Row>
                </Form>
            </Card>
            <BaseCard className={styles.coreFIrmContainer}>
                <BaseTable
                    loading={logQuery?.isFetching}
                    columns={columns}
                    dataSource={logQuery?.data?.data?.records}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={logQuery?.data?.data.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>
        </>
    );
};

export default WithPaginate(SystemLog);

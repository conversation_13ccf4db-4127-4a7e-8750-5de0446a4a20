import React, { useState, useEffect, useRef } from 'react';
import { Form, Input, message, Spin, Modal, Button } from 'antd';
import copyToClipboard from 'copy-to-clipboard';
import { useSelector, useDispatch } from 'react-redux';
import { useMutation } from 'react-query';

// import { encryptionAndDecryption, decrypt } from './help';

import BaseCard from '@components/base-card';
import BaseButton from '@components/base-button';
import './index.less';
import BaseModal from '@components/base-modal';
import PageTitle from '@components/page-title';
import { IStore } from '@store';
import { requestValid } from '@services/user';
import { updatefirstLogin, updateKeyWord } from '@store/slice-reducer/user';
import { useNavigate } from 'react-router-dom';
import { generateValidPrivateKey, decryptPrivateKey } from '../../utils/blockChainUtils';
import { decrypt } from '@utils';
import { ReformChainError } from '@utils/errorCodeReform';
const InterfaceConfig = () => {
    const methodBtn: any = useRef();
    const pinRef: any = useRef();
    const navigate: any = useNavigate();
    const [decryptPrivatekey, setDecryptPrivatekey] = useState(false);
    const [resetModal, setResetModal] = useState(false);
    const [createForm] = Form.useForm();
    const [resetFormModal] = Form.useForm();
    const [decryptForm] = Form.useForm();
    const [modifyModal, setModifyModal] = useState(false);

    // const userInfo = {
    //     publicKey: null
    // };

    const dispatch = useDispatch();

    const userInfo: any = useSelector((store: IStore) => store.user);
    const [firstLogin, setfirstLogin] = useState(userInfo?.userInfo?.firstLogin);

    useEffect(() => {
        if (userInfo?.isLogin && firstLogin && userInfo?.publicKey) {
            if (
                userInfo.userInfo.role_id === 2 &&
                (!userInfo.userInfo.manager_org_id || userInfo.userInfo.manager_org_id === 'null')
            ) {
                navigate('/storage/now/tip');
                dispatch(updatefirstLogin());
                return;
            }
            navigate('/storage/now');
            dispatch(updatefirstLogin());
        }
    }, [
        userInfo?.isLogin,
        firstLogin,
        userInfo.userInfo.role_id,
        userInfo.userInfo.manager_org_id,
        userInfo?.publicKey,
        navigate,
        dispatch
    ]);

    const createPrivate = useMutation(requestValid, {
        // onSuccess: (res: any) => {
        //     if (res.code === 200) {
        //         // localStorage.setItem('privateKey', res.data.prikey);
        //         // sessionStorage.setItem('publicKey', res.data.pubkey);

        //         // const decryptData = decrypt(res.data.prikey, pinRef.current);
        //         // sessionStorage.setItem('privateKey', decryptData);
        //         setModifyModal(true);
        //         message.success('创建成功');
        //     }
        // },
        onError: (err: any) => {
            let errCode = err.response.data.code;
            if (errCode === 4000035) {
                message.error('您已经创建了公私钥');
            }
        }
    });

    const resetKeyWord = useMutation(requestValid, {
        // onSuccess(res: any) {
        //     // localStorage.setItem("privateKey", res.data.prikey);
        //     // sessionStorage.setItem("publicKey", res.data.pubkey ? res.data.pubkey : '');

        //     // const decryptData = decrypt(res.data.prikey, pinRef.current)
        //     // sessionStorage.setItem("privateKey", decryptData);
        // setModifyModal(true);
        // setResetModal(false);
        //     message.success('重置成功');
        // },
        onError() {
            message.error('口令密码错误请重试');
        },
        retry: false
    });

    const renderKey = () => {
        let privateKey = sessionStorage.getItem('privateKey');
        const publicKey = sessionStorage.getItem('publicKey');
        if (publicKey && privateKey && pinRef.current) {
            // const decryptData = decrypt(privateKey, pinRef.current);
            // console.log('decryptData89898998998', decryptData);
            return {
                publicKey,
                privateKey: privateKey
                // decryptData
            };
        } else {
            return null;
        }
    };
    const titles = userInfo?.privateKey == 'null' || userInfo?.privateKey == null ? '基础信息' : '当前账号';

    // console.log('userInfo: ', userInfo?.privateKey == 'null' || userInfo?.privateKey == null );
    return (
        <div className='interface-config'>
            <BaseCard mt24>
                <PageTitle title={titles} />
                {/* <Spin
                // spinning={queryUserBlockchain.isLoading}
                > */}
                {userInfo?.privateKey == 'null' || userInfo?.privateKey == null ? (
                    <>
                        <Form
                            className='interface-config-form'
                            form={createForm}
                            labelCol={{ span: 3 }}
                            wrapperCol={{ span: 8 }}
                            layout='vertical'
                            onFinish={async (kouling) => {
                                methodBtn.current = 'create';
                                console.log('kouling: ', kouling);
                                generateValidPrivateKey(kouling.operationKey, (error, result) => {
                                    if (!error && result) {
                                        console.log('operationKeyoperationKey: ', kouling, error, result);
                                        try {
                                            const user = JSON.parse(localStorage.getItem('userdata') || '');
                                            const { encryptedPrivateKey, privateKey, publicKey, operationKey } = result;
                                            const params = {
                                                userId: user.user_id,
                                                publicKey,
                                                privateKey: encryptedPrivateKey,
                                                operationKey: operationKey
                                            };
                                            createPrivate.mutate(params, {
                                                onSuccess: (res: any) => {
                                                    if (res.code === 200) {
                                                        setModifyModal(true);
                                                        message.success('创建成功');
                                                        // 私钥明文、公钥、 sessionStorage
                                                        sessionStorage.setItem('privateKey', privateKey);
                                                        sessionStorage.setItem('publicKey', publicKey);
                                                        setTimeout(() => {
                                                            dispatch(updateKeyWord());
                                                        }, 0);
                                                    }
                                                }
                                            });
                                            pinRef.current = kouling.operationKey;
                                        } catch (error) {
                                            message.error('环境异常，请退出重新登录');
                                        }
                                    } else {
                                        message.error('随机私钥生成失败，请重试或联系管理员');
                                    }
                                });
                                console.log('operationKeyoperationKey: ', kouling);
                                // createPrivate.mutate(operationKey);
                                pinRef.current = kouling.operationKey;
                            }}
                        >
                            <Form.Item
                                label='口令密码：'
                                name='operationKey'
                                rules={[
                                    { required: true, message: '' },
                                    () => ({
                                        validator: (_, value, callback) => {
                                            const regExp = new RegExp(/^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{8,}$/);
                                            const verify = regExp.test(value);
                                            if (!value) {
                                                callback('请填写口令密码');
                                            } else if (verify === false) {
                                                callback('长度至少8位,包含数字和大小写字母');
                                            } else {
                                                callback();
                                            }
                                        }
                                    })
                                ]}
                            >
                                <Input.Password
                                    placeholder='请填写口令密码'
                                    onChange={() => {
                                        createForm.validateFields(['operationKeys']);
                                    }}
                                />
                            </Form.Item>
                            <Form.Item
                                label='确认口令密码：'
                                name='operationKeys'
                                rules={[
                                    { required: true, message: '请确认口令密码!' },
                                    ({ getFieldValue }) => ({
                                        validator(_, value, callback) {
                                            if (!value || getFieldValue('operationKey') === value) {
                                                callback();
                                            } else {
                                                callback('两次输入不一致!');
                                            }
                                        }
                                    })
                                ]}
                            >
                                <Input.Password placeholder='再次填写口令密码' />
                            </Form.Item>

                            <div className='warnTips'>温馨提示：请您及时创建公私钥，否则将无法使用平台其他功能</div>
                            <Form.Item>
                                <BaseButton
                                    type='primary'
                                    className='my-200 create'
                                    htmlType='submit'
                                    loading={createPrivate.isLoading}
                                >
                                    创建
                                </BaseButton>
                            </Form.Item>
                        </Form>
                    </>
                ) : (
                    <>
                        <Form className='publicKeyContiner'>
                            <Form.Item label='公钥'>{userInfo?.publicKey}</Form.Item>
                        </Form>
                        <BaseButton
                            type='primary'
                            className='my-90 reset'
                            onClick={() => {
                                setResetModal(true);
                                methodBtn.current = 'reset';
                            }}
                        >
                            重置
                        </BaseButton>
                    </>
                )}
                {/* </Spin> */}
            </BaseCard>

            <Modal
                title='重置'
                maskClosable={false}
                visible={resetModal}
                destroyOnClose
                // confirmLoading={queryModifyUserBlock.isLoading}
                onCancel={() => setResetModal(false)}
                onOk={async () => {
                    const { verificationCode } = await resetFormModal.validateFields();
                    // resetKeyWord.mutate(verificationCode);
                    console.log('verificationCode: ', verificationCode);
                    generateValidPrivateKey(verificationCode, (error, result) => {
                        if (!error && result) {
                            console.log('operationKeyoperationKey: ', verificationCode, error, result);
                            try {
                                const user = JSON.parse(localStorage.getItem('userdata') || '');
                                const { encryptedPrivateKey, privateKey, publicKey, operationKey } = result;
                                const params = {
                                    userId: user.user_id,
                                    publicKey,
                                    privateKey: encryptedPrivateKey,
                                    operationKey
                                };
                                resetKeyWord.mutate(params, {
                                    onSuccess: (res: any) => {
                                        if (res.code === 200) {
                                            setModifyModal(true);
                                            setResetModal(false);
                                            message.success('重置成功');
                                            // 私钥明文、公钥、 sessionStorage
                                            sessionStorage.setItem('privateKey', privateKey);
                                            sessionStorage.setItem('publicKey', publicKey);
                                            sessionStorage.setItem('encryptKey', encryptedPrivateKey);
                                            setTimeout(() => {
                                                dispatch(updateKeyWord());
                                            }, 0);
                                        }
                                    }
                                });
                                pinRef.current = verificationCode;
                            } catch (error) {
                                message.error('环境异常，请退出重新登录');
                            }
                        } else {
                            message.error('随机私钥生成失败，请重试或联系管理员');
                        }
                    });
                }}
            >
                <Form preserve={false} labelCol={{ span: 7 }} wrapperCol={{ span: 12 }} form={resetFormModal}>
                    <Form.Item
                        label='口令密码'
                        name='verificationCode'
                        rules={[
                            { required: true, message: '' },
                            () => ({
                                validator: (_, value, callback) => {
                                    const regExp = new RegExp(/^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{8,}$/);
                                    const verify = regExp.test(value);
                                    if (!value) {
                                        callback('请填写新口令密码');
                                    } else if (verify === false) {
                                        callback('长度至少8位,包含数字和大小写字母');
                                    } else {
                                        callback();
                                    }
                                }
                            })
                        ]}
                    >
                        <Input.Password
                            placeholder='请填写新口令密码'
                            onChange={() => {
                                resetFormModal.validateFields(['verificationCodes']);
                            }}
                        />
                    </Form.Item>
                    <Form.Item
                        label='密码确认'
                        name='verificationCodes'
                        rules={[
                            { required: true, message: '请再次确认新口令密码!' },
                            ({ getFieldValue }) => ({
                                validator(_, value, callback) {
                                    if (!value || getFieldValue('verificationCode') === value) {
                                        callback();
                                    } else {
                                        callback('两次输入不一致!');
                                    }
                                }
                            })
                        ]}
                    >
                        <Input.Password placeholder='请再次确认新口令密码' />
                    </Form.Item>
                </Form>
            </Modal>

            <Modal
                wrapClassName='base-modal'
                title={methodBtn.current === 'reset' ? '重置成功' : '创建成功'}
                destroyOnClose
                maskClosable={false}
                visible={modifyModal}
                onCancel={() => {
                    // dispatch(updateKeyWord());
                    // navigate('/storage/now');
                    setModifyModal(false);
                }}
                footer={[
                    <>
                        <Button
                            className='cancel'
                            onClick={() => {
                                setModifyModal(false);
                                if (
                                    (userInfo.userInfo.identity === 1 || userInfo.userInfo.identity === 8) &&
                                    userInfo.isFill === 0
                                ) {
                                    //1为核心企业
                                    message.info('温馨提示：请您及时填写企业信息，否则将无法使用平台其他功能');
                                    navigate('/account/basicInfo');
                                    return;
                                }
                            }}
                        >
                            取消
                        </Button>
                        <Button
                            type='primary'
                            onClick={() => {
                                const copyRet = copyToClipboard(
                                    `公钥: ${renderKey()?.publicKey ? renderKey()?.publicKey : ''}\n私钥: ${
                                        renderKey()?.privateKey
                                    }`
                                );
                                copyRet ? message.success('复制成功') : message.error('复制失败');
                                if (
                                    copyRet &&
                                    (userInfo.userInfo.identity === 1 || userInfo.userInfo.identity === 8) &&
                                    userInfo.isFill === 0
                                ) {
                                    //1为核心企业
                                    message.info('温馨提示：请您及时填写企业信息，否则将无法使用平台其他功能');
                                    navigate('/account/basicInfo');
                                    return;
                                }
                            }}
                        >
                            复制
                        </Button>
                    </>
                ]}
            >
                <div className='privatekey-publickey-warp'>
                    <div className='key-wrap'>
                        <div className='key-name'>公钥:</div>
                        <div className='key-list'>
                            <div className='key'>{renderKey()?.publicKey}</div>
                        </div>
                    </div>
                    <div className='key-wrap'>
                        <div className='key-name'>私钥:</div>
                        <div className='key-list'>
                            <div className='key'>{renderKey()?.privateKey}</div>
                        </div>
                    </div>
                    <div className='key-tips'>公私钥仅展示一次，请点击复制并妥善保存！</div>
                </div>
            </Modal>
        </div>
    );
};

export default React.memo(InterfaceConfig);

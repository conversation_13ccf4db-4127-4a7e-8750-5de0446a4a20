/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:27:42
 * @LastEditTime: 2022-11-01 18:11:49
 * @LastEditors: PhilRandWu
 */
export const addEmployeesConfigs = [
    {
        label: '用户名',
        type: 'Input',
        value: 'name',
        placeholder: '请输入用户名',
        rules: [{ required: true, message: '请输入用户名!' }]
    },
    {
        label: '联系方式',
        type: 'Input',
        value: 'contact',
        placeholder: '请输入',
        rules: [{ required: true, message: '请输入联系方式' }]
    },
    {
        label: '权限',
        type: 'TagsSelect',
        value: 'permission',
        placeholder: '请选择',
        rules: [{ required: true, message: '请选择权限!' }],
        fields: [
            {
                value: '12发给3',
                label: '123vfv123'
            },
            {
                value: '12dfvdfdf3',
                label: '1231vdfvdfvdd23'
            },
            {
                value: '12ddvsdfsffgnf3',
                label: '123gfbgfbfgb123'
            }
        ]
    }
];

export const editEmployeesConfigs = [
    {
        label: '用户名',
        type: 'Input',
        value: 'name',
        placeholder: 'erferferf',
        rules: [{ required: true, message: 'Please input your username!' }]
    },
    {
        label: '联系方式',
        type: 'Input',
        value: 'phone',
        placeholder: 'erferferf',
        rules: [{ required: true, message: 'Please input your username!' }]
    },
    {
        label: '权限',
        type: 'TagsSelect',
        value: 'account',
        placeholder: 'fefer',
        rules: [{ required: true, message: 'Please input your username!' }],
        fields: [
            {
                value: '12发给3',
                label: '123vfv123'
            },
            {
                value: '12dfvdfdf3',
                label: '1231vdfvdfvdd23'
            },
            {
                value: '12ddvsdfsffgnf3',
                label: '123gfbgfbfgb123'
            }
        ]
    }
];

export const searchConfig = [
    {
        label: '搜索',
        type: 'Input',
        value: 'name',
        placeholder: '输入用户名/联系方式',
        span: 12,
        className: 'find'
    },
    {
        label: '状态',
        type: 'Select',
        value: 'data',
        placeholder: '请选择',
        span: 12,
        className: 'find',
        fields: [
            {
                value: '1',
                label: '禁用'
            },
            {
                value: '0',
                label: '可用'
            }
        ]
    }
];
export enum staff {
    物流管理 = 116,
    物流溯源数据 = 122,
    系统日志 = 123,
    质检溯源数据 = 121,
    原料溯源数据 = 119,
    生产溯源数据 = 120,
    溯源码包管理 = 117,
    溯源码列表 = 118,
    质检管理 = 115,
    出库管理 = 114,
    入库管理 = 113,
    原料采购管理 = 109,
    原料管理 = 108,
    产品管理 = 106,
    产地管理 = 107,
    箱码管理 = 112,
    参与方管理 = 105,
    生产加工管理 = 111,
    生产过程管理 = 110,
    种植管理=130,
    种植溯源数据=131,
    收购管理=142,
    销售管理=143,
    收购溯源数据=144,
    销售溯源数据=145,
    仓储溯源数据=150,
    用户反馈=147,
    仓储管理=149,

}

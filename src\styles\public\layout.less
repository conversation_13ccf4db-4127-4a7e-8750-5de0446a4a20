.flexContent(@direction: row,@mainShaft: center,@layShaft: center) {
    flex: 1;
    display: flex;
    flex-direction: @direction;
    justify-content: @mainShaft;
    align-items: @layShaft;
}

.text-layout(@mainShaft: center,@contentHeight: 14px) {
    text-align: @mainShaft;
    height: @contentHeight;
    line-height: @contentHeight;
}

.backgroundImg(@url,@size: contain,@repeat: no-repeat) {
    background: url(`${@url}`);
    background-size: @size;
    background-repeat: @repeat;
}

.warnBtn {
    border: 1px solid @redBtn;
    color: @redBtn;
    font-size: 14px;
    border-radius: 3.5px;
    background: #faeae9;
    &:hover {
        border: 1px solid @redBtn;
        color: @redBtn;
        background:#faeae9;
    }
}

.primaryBtn {
    border: 1px solid @primary-color;
    color: @primary-color;
    font-size: 14px;
    border-radius: 3.5px;
    background: #f2f8f2;
    &:hover {
        border: 1px solid @primary-color;
        background: #f2f8f2;
        color: @primary-color;
    }
}

.greenBtn {
    border: 1px solid @greenBtn;
    color: @greenBtn;
    font-size: 14px;
    border-radius: 3.5px;
    &:hover {
        border: 1px solid @greenBtn;
        color: @greenBtn;
    }
}
.bgBtn{
  background:#80a932 !important;
  border: 1px solid #80a932 !important;
  color: aliceblue  !important;
  &:hover {
    background:#80a932  !important;
  border: 1px solid #80a932  !important;
  color: aliceblue  !important;
}
}

.bgBtn:hover, .bgBtn:focus{
  background:#80a932 !important;
  border: 1px solid #80a932  !important;
  color: aliceblue  !important;
}

.editBtn{
  // background:#80a932;
  border: 1px solid #ff9826;
  color: #ff9826;
  background: #fdeec9;
  &:hover {
    // background:#ff9826;
  border: 1px solid #ff9826;
  color: #ff9826;
  background: #fdeec9;
}
}

.editBtn:hover, .editBtn:focus{
  border: 1px solid #ff9826;
  color: #ff9826;
  background: #fdeec9;
}

.primaryBtn:hover, .primaryBtn:focus{
  background:#f2f8f2;
  border: 1px solid #80a932;
  color: #80a932;
}

.ant-collapse-item{
  a{
    color: #80a932 !important;
  }
}
.ant-switch-checked{
  background-color: #80a932;
}

//去掉展开收起横线
.ant-card-bordered{
  border: none;
  position: relative;

}
.ant-upload-text-icon{
  display: none !important;
}
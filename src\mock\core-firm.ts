// /*
//  * @Description:
//  * @Author: PhilRandWu
//  * @Github: https://github/PhilRandWu
//  * @Date: 2022-06-28 10:36:58
//  * @LastEditTime: 2022-10-08 09:34:24
//  * @LastEditors: PhilRandWu
//  */
// import Mock, { Random } from 'mockjs';

// Mock.Random.extend({
//     phone: function () {
//         const phonePrefixs = ['132']; // 自己写前缀哈
//         return this.pick(phonePrefixs) + Mock.mock(/\d{8}/); //Number()
//     }
// });

// export const requestCoreFlrmList = () => {
//     const requestList = new Array(10).fill(1).map((item, index) => {
//         return Mock.mock({
//             account: '@phone',
//             name: '网关' + Random.string(),
//             time: Random.date('yyyy-MM-dd'),
//             ip: Random.ip(),
//             operation: '@cparagraph(1,2)',
//             status: Random.boolean()
//         });
//     });
//     return new Promise((resolve, reject) => {
//         setTimeout(() => {
//             resolve(requestList);
//         }, 1000);
//     });
// };

import React from 'react';
import AppLoading from '@components/app-loading';
import Login from '@pages/login';
import CodeDetail from '@pages/trace-source';
// @ts-ignore
// import OverviewIcon from '@assets/icon/sider/overview.png';
import parties from '../assets/icon/canyu.png';
// import qcqa from '../assets/icon/zhijian.png';
import raw from '../assets/icon/caigou.png';
// import traceability from '../assets/icon/suyuanma.png';
import cpsj from '../assets/icon/churuku.png';
import food from '../assets/icon/food.png';
// import product from '../assets/icon/product.png';
// import source from '../assets/icon/source.png';
// import productIcon from '../assets/icon/productIcon.svg';
// import account from '../assets/icon/account.png';
import prikey from '../assets/icon/private.png';
import pack from '../assets/icon/statistic.png';
// import systemLog from '../assets/icon/log.png';
import logistics from '../assets/icon/logistics.svg';

//大米
import OverviewIcon from '@assets/landicon/sider/overview.png';
import account from '@assets/landicon/sider/account.png'; // 账号管理
import productIcon from '@assets/landicon/sider/productIcon.png'; // 产品管理
import product from '@assets/landicon/sider/product.png'; // 生产管理
import qcqa from '@assets/landicon/sider/zhijian.png'; // 质检管理
import traceability from '@assets/landicon/sider/traceability.png'; // 溯源码管理
import source from '@assets/landicon/sider/source.png'; // 溯源码管理
import systemLog from '@assets/landicon/sider/systemLog.png'; // 日志管理

import land from '@assets/landicon/sider/land.png'; // 种植管理
import purchase from '@assets/landicon/sider/purchase.png'; // 收购管理
import sales from '@assets/landicon/sider/sales.png';
import feek from '@assets/landicon/sider/feek.png'; //
import warehouse from '@assets/landicon/sider/warehouse.png'; //
import xiao from '@assets/landicon/sider/xiao.png'; // 销售管理
import { BarChartOutlined, BookOutlined, WarningOutlined, FileTextOutlined } from '@ant-design/icons';

const BasicLayout = React.lazy(() => import('@layout'));

const LazyLoad = (path: string) => {
    const Element = React.lazy(() => import(`@pages/${path}`));
    return (
        <React.Suspense fallback={<AppLoading />}>
            <Element />
        </React.Suspense>
    );
};

export const openRoutes = [
    {
        path: 'login',
        element: <Login></Login>
    },
    {
        path: 'trace-source-code/codeDetail',
        element: <CodeDetail></CodeDetail>
    }
];

export const authRoutes = [
    {
        path: 'coreflrmaccount',
        label: '账号管理',
        icon: account,
        showInSider: true,
        canAuth: '平台方账号管理',
        element: LazyLoad('account-manager')
        // canAuth: (userStore: any) => {
        //     return userStore.menuPermissionList.includes('账号/员工管理') && userStore.userInfo?.roleId === 1;
        // },
        // children: [
        //     {
        //         index: true,
        //         label: '账号列表',
        //         element: LazyLoad('account-manager')
        //     }
        // ]
    },
    {
        path: 'traceability-overview',
        label: '溯源总览',
        showInSider: true,
        element: LazyLoad('traceability-overview'),
        canAuth: '溯源总览',
        icon: OverviewIcon
    },
    {
        path: 'account',
        label: '账号管理',
        showInSider: true,
        icon: account,
        children: [
            {
                path: 'basicInfo',
                label: '企业信息管理',
                showInSider: true,
                canAuth: '企业信息管理',
                children: [
                    {
                        index: true,
                        element: LazyLoad('basicInfo-manager')
                    },
                    {
                        path: 'basicInfoEdit',
                        label: '编辑',
                        element: LazyLoad('basicInfo-edit')
                    }
                ]
            },
            {
                path: 'employees',
                label: '员工管理',
                showInSider: true,
                canAuth: '员工管理',
                // canAuth: (userStore: any) => {
                //     return userStore.menuPermissionList.includes('账号/员工管理') && userStore.userInfo?.roleId !== 1;
                // },
                element: LazyLoad('employees-manager')
            }
        ]
    },
    {
        path: 'land',
        label: '种植管理',
        icon: land,
        canAuth: '种植管理',
        showInSider: true,
        children: [
            {
                path: 'list',
                label: '种植信息列表',
                canAuth: '种植管理',
                children: [
                    {
                        index: true,
                        label: '',
                        element: LazyLoad('land-list')
                    },
                    {
                        path: 'add',
                        label: '新建质检',
                        element: LazyLoad('land-add')
                    },
                    {
                        path: 'detail/:id',
                        label: '种植信息详情',
                        element: LazyLoad('land-detail')
                    }
                ]
            }
        ]
    },
    {
        path: 'purchase',
        label: '收购管理',
        icon: purchase,
        canAuth: '收购管理',
        showInSider: true,
        children: [
            {
                path: 'list',
                label: '收购管理列表',
                canAuth: '收购管理',
                children: [
                    {
                        index: true,
                        label: '',
                        element: LazyLoad('purchase-list')
                    },
                    {
                        path: 'add',
                        label: '新建收购信息',
                        element: LazyLoad('purchase-add')
                    },
                    {
                        path: 'edit',
                        label: '编辑收购信息',
                        element: LazyLoad('purchase-edit')
                    },
                    {
                        path: 'detail',
                        label: '收购信息详情',
                        element: LazyLoad('purchase-detail')
                    }
                ]
            }
        ]
    },

    {
        path: 'core-flrm',
        label: '企业管理',
        icon: OverviewIcon,
        showInSider: true,
        canAuth: '企业管理',
        children: [
            {
                index: true,
                label: '企业列表',
                element: LazyLoad('core-flrm')
            },
            {
                path: 'detail/:id',
                label: '企业详情',
                element: LazyLoad('core-flrm-details')
            }
        ]
    },
    // {
    //     path: 'parties',
    //     icon: parties,
    //     label: '参与方管理',
    //     showInSider: true,
    //     canAuth: '参与方管理',
    //     children: [
    //         {
    //             path: 'manage',
    //             label: '参与方列表',
    //             canAuth: '参与方管理',
    //             children: [
    //                 {
    //                     index: true,
    //                     label: '',
    //                     element: LazyLoad('parties-manage')
    //                 },
    //                 // {
    //                 //     path: 'add',
    //                 //     label: '编辑参与方',
    //                 //     element: LazyLoad('parties-edit')
    //                 // },
    //                 {
    //                     path: 'detail',
    //                     label: '参与方详情',
    //                     element: LazyLoad('parties-details')
    //                 }
    //             ]
    //         }
    //         // {
    //         //     index: true,
    //         //     label: '参与方列表',
    //         //     element: LazyLoad('parties-manage')
    //         // },
    //         // {
    //         //     path: 'edit',
    //         //     label: '编辑参与方',
    //         //     element: LazyLoad('parties-edit')
    //         // },
    //         // {
    //         //     path: 'details',
    //         //     label: '参与方详情',
    //         //     element: LazyLoad('parties-details')
    //         // }
    //     ]
    // },
    {
        path: 'product-manage',
        label: '产品管理',
        icon: productIcon,
        showInSider: true,
        children: [
            {
                path: 'food',
                label: '产品管理',
                showInSider: true,
                canAuth: '产品管理',
                children: [
                    {
                        index: true,
                        label: '产品列表',
                        element: LazyLoad('food-list')
                    },

                    {
                        path: 'add',
                        label: '新建产品',
                        element: LazyLoad('food-add')
                    },
                    {
                        path: 'infoConfig',
                        label: '配置产品信息',
                        element: LazyLoad('food-info-config')
                    },
                    {
                        path: 'source',
                        label: '配置溯源码信息',
                        element: LazyLoad('food-source-config')
                    },
                    {
                        path: 'detail',
                        label: '产品详情',
                        element: LazyLoad('food-detail')
                    }
                ]
            },
            {
                path: 'origin',
                label: '产地管理',
                showInSider: true,
                canAuth: '产地管理',
                children: [
                    {
                        index: true,
                        label: '产地列表',
                        element: LazyLoad('origin-list')
                    },

                    {
                        path: 'add',
                        label: '新建产地',
                        element: LazyLoad('origin-add')
                    },
                    {
                        path: 'infoConfig',
                        label: '配置产地信息',
                        element: LazyLoad('origin-info-config')
                    },
                    {
                        path: 'detail',
                        label: '产地详情',
                        element: LazyLoad('origin-detail')
                    }
                ]
            },
            {
                path: 'source',
                label: '原料管理',
                canAuth: '原料管理',
                showInSider: true,
                children: [
                    {
                        index: true,
                        label: '原料列表',
                        element: LazyLoad('source-list')
                    },
                    {
                        path: 'source-add',
                        label: '新建原料',
                        element: LazyLoad('source-add')
                    },
                    {
                        path: 'source-edit',
                        label: '原料编辑',
                        element: LazyLoad('source-edit')
                    }
                ]
            }
        ]
    },
    {
        path: 'raw',
        label: '原料采购管理',
        icon: raw,
        canAuth: '原料采购管理',
        showInSider: true,
        children: [
            {
                path: 'rawlist',
                label: '原料采购列表',
                children: [
                    {
                        index: true,
                        label: '',
                        element: LazyLoad('raw-material-list')
                    },
                    {
                        path: 'add',
                        label: '新建采购批次',
                        element: LazyLoad('raw-material-add')
                    },
                    {
                        path: 'detail',
                        label: '采购详情',
                        element: LazyLoad('raw-material-detail'),
                        upRouter: {
                            path: 'list',
                            label: '食品列表'
                        }
                    }
                ]
            }
        ]
    },
    {
        path: 'product',
        label: '生产管理',
        icon: product,
        canAuth: '生产管理',
        showInSider: true,
        children: [
            {
                path: 'product',
                label: '生产过程管理',
                // icon: product,
                canAuth: '生产过程管理',
                showInSider: true,
                children: [
                    {
                        index: true,
                        label: '生产过程列表',
                        element: LazyLoad('product-list')
                    },
                    {
                        path: 'detail',
                        label: '生产过程详情',
                        element: LazyLoad('product-detail')
                    }
                ]
            },
            {
                path: 'process',
                label: '生产加工管理',
                // icon: product,
                canAuth: '生产加工管理',
                showInSider: true,
                children: [
                    {
                        label: '生产批次列表',
                        index: true,
                        element: LazyLoad('processing-list')
                    },
                    {
                        path: 'add',
                        label: '新建生产批次',
                        element: LazyLoad('processing-add')
                    },
                    {
                        path: 'detail',
                        label: '生产批次详情',
                        element: LazyLoad('processing-detail')
                    }
                ]
            }
            // {
            //     index: true,
            //     label: '生产批次列表',
            //     element: LazyLoad('product-list')
            // },
            // {
            //     path: 'add',
            //     label: '新建生产批次',
            //     element: LazyLoad('product-add')
            // },
            // {
            //     path: 'detail',
            //     label: '生产批次详情',
            //     element: LazyLoad('product-detail')
            // }
        ]
    },
    {
        path: 'box-code',
        label: '装箱管理',
        icon: pack,
        canAuth: '装箱管理',
        showInSider: true,
        children: [
            {
                path: 'rawlist',
                label: '箱码列表',
                canAuth: '装箱管理',
                children: [
                    {
                        index: true,
                        label: '',
                        element: LazyLoad('box-code')
                    },
                    {
                        path: 'detail',
                        label: '采购详情',
                        element: LazyLoad('raw-material-detail'),
                        upRouter: {
                            path: 'list',
                            label: '食品列表'
                        }
                    }
                ]
            }
        ]
    },
    {
        path: 'cpsjsr',
        label: '出入库管理',
        icon: cpsj,
        showInSider: true,
        children: [
            {
                path: 'putIn',
                label: '入库管理',
                showInSider: true,
                canAuth: '入库管理',
                children: [
                    {
                        index: true,
                        element: LazyLoad('putinstorage-list')
                    },
                    {
                        path: 'add',
                        label: '入库信息填写',
                        element: LazyLoad('putinstorage-add')
                    },
                    {
                        path: 'detail',
                        label: '入库信息详情',
                        element: LazyLoad('putinstorage-detail')
                    }
                ]
            },
            {
                path: 'putOut',
                label: '出库管理',
                showInSider: true,
                canAuth: '出库管理',
                children: [
                    {
                        index: true,
                        element: LazyLoad('putoutstorage-list')
                    },
                    {
                        path: 'Outadd',
                        label: '出库信息填写',
                        element: LazyLoad('putoutstorage-add')
                    },
                    {
                        path: 'Outdetail',
                        label: '出库信息详情',
                        element: LazyLoad('putoutstorage-detail')
                    }
                ]
            }
        ]
    },

    {
        path: 'qcqa',
        label: '质检管理',
        icon: qcqa,
        canAuth: '质检管理',
        showInSider: true,
        children: [
            {
                path: 'list',
                label: '质检信息列表',
                canAuth: '质检管理',
                children: [
                    {
                        index: true,
                        label: '',
                        element: LazyLoad('qcqa-list')
                    },
                    {
                        path: 'add',
                        label: '新建质检',
                        element: LazyLoad('qcqa-add')
                    },
                    {
                        path: 'detail',
                        label: '质检信息详情',
                        element: LazyLoad('qcqa-detail')
                    }
                ]
            }
        ]
    },
    {
        path: 'warehouse',
        label: '仓储管理',
        icon: warehouse,
        canAuth: '仓储管理',
        showInSider: true,
        children: [
            {
                path: 'list',
                label: '仓储管理列表',
                canAuth: '仓储管理',
                children: [
                    {
                        index: true,
                        label: '',
                        element: LazyLoad('warehouse-list')
                    },
                    {
                        path: 'detail',
                        label: '仓储信息详情',
                        element: LazyLoad('warehouse-detail')
                    }
                ]
            }
        ]
    },

    {
        path: 'market',
        label: '销售管理',
        icon: xiao,
        canAuth: '销售管理',
        showInSider: true,
        children: [
            {
                path: 'list',
                label: '销售管理列表',
                canAuth: '销售管理',
                children: [
                    {
                        index: true,
                        label: '',
                        element: LazyLoad('market-list')
                    },
                    // {
                    //     path: 'add',
                    //     label: '新建收购信息',
                    //     element: LazyLoad('purchase-add')
                    // },
                    // {
                    //     path: 'edit',
                    //     label: '编辑收购信息',
                    //     element: LazyLoad('purchase-edit')
                    // },
                    {
                        path: 'detail',
                        label: '销售信息详情',
                        element: LazyLoad('market-detail')
                    }
                ]
            }
        ]
    },
    {
        path: 'feedback',
        label: '用户反馈',
        icon: feek,
        canAuth: '用户反馈',
        showInSider: true,
        children: [
            {
                path: 'list',
                label: '用户反馈列表',
                canAuth: '用户反馈',
                children: [
                    {
                        index: true,
                        label: '',
                        element: LazyLoad('feedback-list')
                    },
                    {
                        path: 'detail',
                        label: '用户反馈详情',
                        element: LazyLoad('feedback-detail')
                    }
                ]
            }
        ]
    },

    // {
    //     path: 'traceability',
    //     label: '溯源码管理',
    //     icon: traceability,
    //     showInSider: true,
    //     children: [
    //         {
    //             path: 'list',
    //             label: '溯源码包列表',
    //             showInSider: true,
    //             canAuth: '溯源码包列表',
    //             element: LazyLoad('traceability-list')
    //         },
    //         {
    //             path: 'code-list',
    //             label: '溯源码列表',
    //             showInSider: true,
    //             canAuth: '溯源码列表',
    //             children: [
    //                 {
    //                     index: true,
    //                     element: LazyLoad('back2sourcecode-list')
    //                 },
    //                 {
    //                     path: 'code-detail',
    //                     label: '溯源码详情',
    //                     element: LazyLoad('back2sourcecode-detail')
    //                 }
    //             ]
    //         }
    //     ]
    // },
    // {
    //     path: 'source-data',
    //     label: '溯源数据管理',
    //     icon: source,
    //     showInSider: true,
    //     canAuth: '溯源数据管理',
    //     children: [
    //         {
    //             path: 'manage',
    //             label: '溯源数据列表',
    //             canAuth: '溯源数据管理',
    //             children: [
    //                 {
    //                     index: true,
    //                     label: '',
    //                     element: LazyLoad('source-data-manage')
    //                 },
    //                 {
    //                     path: 'detail',
    //                     label: '溯源数据详情',
    //                     element: LazyLoad('source-data-detail')
    //                 }
    //             ]
    //         }
    //         // {
    //     index: true,
    //     label: '溯源数据列表',
    //     element: LazyLoad('source-data-manage')
    // },
    // {
    //     path: 'detail',
    //     label: '溯源数据详情',
    //     element: LazyLoad('source-data-detail')
    // }
    //     ]
    // },

    {
        path: 'logistics-manage',
        label: '物流管理',
        icon: logistics,
        canAuth: '物流管理',
        showInSider: true,
        children: [
            {
                path: 'list',
                label: '物流信息列表',
                canAuth: '物流管理',
                children: [
                    {
                        index: true,
                        element: LazyLoad('logistics-manage')
                    },
                    {
                        path: 'detail',
                        label: '物流管理详情',
                        element: LazyLoad('logistics-manage-detail')
                    }
                ]
            }
        ]
    },
    {
        path: 'code-manage',
        label: '溯源码管理',
        icon: traceability,
        showInSider: true,
        children: [
            {
                path: 'code-package',
                label: '溯源码包列表',
                canAuth: '溯源码包列表',
                showInSider: true,
                element: LazyLoad('code-package')
            },
            {
                path: 'code',
                label: '溯源码列表',
                canAuth: '溯源码列表',
                showInSider: true,
                children: [
                    { index: true, element: LazyLoad('code-list') },
                    {
                        path: 'detail',
                        label: '溯源码详情',
                        element: LazyLoad('code-list-detail')
                    }
                ]
            }
        ]
    },
    {
        path: 'source-data-manage',
        label: '溯源数据管理',
        icon: source,
        canAuth: '溯源数据管理',
        showInSider: true,
        children: [
            // {//大米 溯源关于原料的去除掉
            //     path: 'raw-material',
            //     label: '原料溯源数据',
            //     canAuth: '原料溯源数据',
            //     showInSider: true,
            //     children: [
            //         { index: true, element: LazyLoad('raw-material-data') },
            //         {
            //             path: 'detail/:id',
            //             label: '原料溯源数据详情',
            //             element: LazyLoad('raw-material-data-detail')
            //         }
            //     ]
            // },
            {
                path: 'land',
                label: '种植溯源列表',
                canAuth: '种植溯源数据',
                showInSider: true,
                children: [
                    { index: true, element: LazyLoad('land-data') },
                    {
                        path: 'detail/:id',
                        label: '种植溯源数据详情',
                        element: LazyLoad('land-data-detail')
                    }
                ]
            },
            {
                path: 'market',
                label: '收购溯源列表',
                canAuth: '收购溯源数据',
                showInSider: true,
                children: [
                    { index: true, element: LazyLoad('market-data') },
                    {
                        path: 'detail',
                        label: '收购溯源数据详情',
                        element: LazyLoad('market-data-detail')
                    }
                ]
            },
            {
                path: 'production',
                label: '生产溯源列表',
                canAuth: '生产溯源数据',
                showInSider: true,
                children: [
                    { index: true, element: LazyLoad('production-data') },
                    {
                        path: 'detail/:id',
                        label: '生产溯源数据详情',
                        element: LazyLoad('production-data-detail')
                    }
                ]
            },
            {
                path: 'inspect',
                label: '质检溯源列表',
                canAuth: '质检溯源数据',
                showInSider: true,
                children: [
                    { index: true, element: LazyLoad('inspect-data') },
                    {
                        path: 'detail/:id',
                        label: '质检溯源数据详情',
                        element: LazyLoad('inspect-data-detail')
                    }
                ]
            },
            {
                path: 'wars',
                label: '仓储溯源列表',
                canAuth: '仓储溯源数据',
                showInSider: true,
                children: [
                    { index: true, element: LazyLoad('wars-list') },
                    {
                        path: 'detail',
                        label: '仓储溯源数据详情',
                        element: LazyLoad('wars-detail')
                    }
                ]
            },
            {
                path: 'sales',
                label: '销售溯源列表',
                canAuth: '销售溯源数据',
                showInSider: true,
                children: [
                    { index: true, element: LazyLoad('sales-list') },
                    {
                        path: 'detail',
                        label: '销售溯源数据详情',
                        element: LazyLoad('sales-detail')
                    }
                ]
            }

            // {
            //     path: 'logistics',
            //     label: '物流溯源数据',
            //     canAuth: '物流溯源数据',
            //     showInSider: true,
            //     children: [
            //         { index: true, element: LazyLoad('logistics-data') },
            //         {
            //             path: 'detail/:id',
            //             label: '运输单详情',
            //             element: LazyLoad('logistics-data-detail')
            //         }
            //     ]
            // }
        ]
    },
    {
        path: 'private',
        label: '私钥管理',
        icon: prikey,
        // showInSider: true,
        children: [
            {
                index: true,
                element: LazyLoad('interface-config')
            }
        ]
    },
    {
        path: 'flee-warning',
        label: '窜货预警',
        icon: prikey,
        // showInSider: true,
        canAuth: '私钥管理',
        children: [
            {
                index: true,
                element: LazyLoad('flee-warning')
            },
            {
                path: 'detail',
                label: '窜货预警详情',
                element: LazyLoad('flee-warning-detail')
            }
        ]
    },
    {
        path: 'admin',
        label: '管理员管理',
        children: [
            {
                path: 'password-reset',
                label: '修改密码',
                element: LazyLoad('password-reset')
            }
        ]
    },

    {
        path: 'system-log',
        label: '系统日志',
        icon: systemLog,
        showInSider: true,
        canAuth: '系统日志',
        children: [
            {
                index: true,
                element: LazyLoad('system-log')
            }
        ]
    },
    {
        path: 'sensitive-words',
        label: '敏感词库',
        icon: FileTextOutlined,
        showInSider: true,
        canAuth: '敏感词库',
        children: [
            {
                path: 'management',
                label: '敏感词管理',
                showInSider: true,
                canAuth: '敏感词管理',
                children: [
                    {
                        index: true,
                        element: LazyLoad('sensitive-words/management')
                    }
                ]
            },
            {
                path: 'segmentation',
                label: '分词管理',
                showInSider: true,
                canAuth: '分词管理',
                children: [
                    {
                        index: true,
                        element: LazyLoad('sensitive-words/segmentation')
                    }
                ]
            },
            {
                path: 'whitelist',
                label: '白名单管理',
                showInSider: true,
                canAuth: '白名单管理',
                children: [
                    {
                        index: true,
                        element: LazyLoad('sensitive-words/whitelist')
                    }
                ]
            }
        ]
    }
];

export const basiceRouter: any = [
    {
        path: '/',
        hidden: true,
        element: <Login />
    },
    {
        path: '/login',
        hidden: true,
        element: <Login />
    },
    {
        path: '/coreflrm',
        label: '企业管理',
        element: <BasicLayout />,
        children: [
            {
                index: true,
                label: '企业列表',
                element: LazyLoad('core-flrm')
            }
        ]
    },
    {
        path: '/coreflrmaccount',
        label: '账号管理',
        element: <BasicLayout />,
        children: [
            {
                index: true,
                label: '账号列表',
                element: LazyLoad('account-manager')
            }
        ]
    },

    {
        path: '/traceability-overview',
        label: '溯源总览',
        element: <BasicLayout />,
        children: [
            {
                index: true,
                label: '溯源总览',
                element: LazyLoad('traceability-overview')
            }
        ]
    },

    // 生产加工企业路由
    {
        path: '/account',
        label: '账号管理',
        // icon: logo,
        jumpPage: 'basicInfo',
        element: <BasicLayout />,
        children: [
            {
                path: 'basicInfo',
                label: '企业信息管理',
                element: LazyLoad('basicInfo-manager')
            },
            {
                path: 'basicInfoEdit',
                label: '企业信息管理',
                hidden: true,
                element: LazyLoad('basicInfo-edit')
            },
            {
                path: 'employees',
                label: '员工管理',
                element: LazyLoad('employees-manager')
            }
        ]
    },

    // 参与方管理
    // {
    //     path: '/parties',
    //     icon: parties,
    //     label: '参与方管理',
    //     element: <BasicLayout />,
    //     children: [
    //         {
    //             index: true,
    //             label: '参与方管理',
    //             element: LazyLoad('parties-manage')
    //         },
    //         {
    //             path: 'edit',
    //             label: '编辑参与方',
    //             element: LazyLoad('parties-edit')
    //         }
    //     ]
    // },

    // 食品管理
    {
        path: '/food',
        label: '食品管理',
        jumpPage: 'list',
        element: <BasicLayout />,
        children: [
            {
                path: 'list',
                label: '食品列表',
                element: LazyLoad('food-list')
            },
            {
                path: 'add',
                label: '新建食品',
                hidden: true,
                element: LazyLoad('food-add'),
                upRouter: {
                    path: 'list',
                    label: '食品列表'
                }
            },
            {
                path: 'infoConfig',
                label: '配置食品信息',
                hidden: true,
                element: LazyLoad('food-info-config'),
                upRouter: {
                    path: 'list',
                    label: '食品列表'
                }
            },
            {
                path: 'source',
                label: '配置溯源码信息',
                hidden: true,
                element: LazyLoad('food-source-config'),
                upRouter: {
                    path: 'list',
                    label: '食品列表'
                }
            },
            {
                path: 'detail',
                label: '食品详情',
                hidden: true,
                element: LazyLoad('food-detail'),
                upRouter: {
                    path: 'list',
                    label: '食品列表'
                }
            },
            {
                path: 'source-list',
                label: '原料管理',
                element: LazyLoad('source-list')
            },
            {
                path: 'source-add',
                label: '新建原料',
                hidden: true,
                element: LazyLoad('source-add'),
                upRouter: {
                    path: 'source-list',
                    label: '原料管理'
                }
            },
            {
                path: 'source-edit',
                label: '原料编辑',
                hidden: true,
                element: LazyLoad('source-edit'),
                upRouter: {
                    path: 'source-list',
                    label: '原料管理'
                }
            }
        ]
    },

    // {
    //     path: '/traceability',
    //     label: '溯源码管理',
    //     jumpPage: 'list',
    //     element: <BasicLayout />,
    //     children: [
    //         {
    //             path: 'list',
    //             label: '溯源码包列表',
    //             element: LazyLoad('traceability-list')
    //         },
    //         {
    //             path: 'code-list',
    //             label: '溯源码列表',
    //             element: LazyLoad('back2sourcecode-list')
    //         },
    //         {
    //             path: 'code-detail',
    //             label: '溯源码详情',
    //             hidden: true,
    //             element: LazyLoad('back2sourcecode-detail'),
    //             upRouter: {
    //                 path: 'code-list',
    //                 label: '溯源码列表'
    //             }
    //         }
    //     ]
    // },
    // {
    //     path: '/source-data',
    //     label: '溯源数据管理',
    //     element: <BasicLayout/>,
    //     children: [
    //         {
    //             index: true,
    //             label: '溯源数据列表',
    //             element: LazyLoad('source-data-manage')
    //         },
    //         {
    //             path: 'detail',
    //             label: '溯源数据详情',
    //             element: LazyLoad('source-data-detail')
    //         }
    //     ]
    // },
    {
        path: '/raw',
        label: '原料采购管理',
        canAuth: '原料采购管理',
        icon: raw,
        element: <BasicLayout />,
        children: [
            {
                index: true,
                path: 'rawlist',
                label: '原料采购列表',
                element: LazyLoad('raw-material-list')
            },
            {
                path: 'add',
                label: '新建采购批次',
                element: LazyLoad('raw-material-add')
            },
            {
                path: 'detail',
                label: '采购详情',
                element: LazyLoad('raw-material-detail'),
                upRouter: {
                    path: 'rawlist',
                    label: '原料采购列表'
                }
            }
        ]
    },
    {
        path: '/product',
        label: '生产管理',
        element: <BasicLayout />,
        children: [
            {
                index: true,
                label: '生产批次列表',
                element: LazyLoad('product-list')
            },
            {
                path: 'add',
                label: '新建生产批次',
                element: LazyLoad('product-add'),
                upRouter: {
                    path: '',
                    label: '生产批次列表'
                }
            },
            {
                path: 'detail',
                label: '生产批次详情',
                element: LazyLoad('product-detail'),
                upRouter: {
                    path: '',
                    label: '生产批次列表'
                }
            }
        ]
    },
    {
        path: '/qcqa',
        label: '质检管理',
        icon: qcqa,
        element: <BasicLayout />,
        children: [
            {
                index: true,
                label: '质检信息列表',
                element: LazyLoad('qcqa-list')
            },
            {
                path: 'add',
                label: '新建质检',
                element: LazyLoad('qcqa-add'),
                upRouter: {
                    path: '',
                    label: '质检信息列表'
                }
            },
            {
                path: 'detail',
                label: '质检信息详情',
                element: LazyLoad('qcqa-detail'),
                upRouter: {
                    path: '',
                    label: '质检信息列表'
                }
            }
        ]
    },

    // {
    //     path: '/qcqa',
    //     label: '质检管理',
    //     icon: qcqa,
    //     element: <BasicLayout />,
    //     children: [
    //         {
    //             index: true,
    //             label: '质检信息列表',
    //             element: LazyLoad('qcqa-list')
    //         },
    //         {
    //             path: 'add',
    //             label: '新建质检',
    //             element: LazyLoad('qcqa-add'),
    //             upRouter: {
    //                 path: '',
    //                 label: '质检信息列表'
    //             }
    //         },
    //         {
    //             path: 'detail',
    //             label: '质检信息详情',
    //             element: LazyLoad('qcqa-detail'),
    //             upRouter: {
    //                 path: '',
    //                 label: '质检信息列表'
    //             }
    //         }
    //     ]
    // },

    {
        path: '/cpsjsr',
        label: '出入库管理',
        jumpPage: 'putIn',
        element: <BasicLayout />,
        children: [
            {
                path: 'putIn',
                label: '入库管理',
                element: LazyLoad('putinstorage-list')
            },
            {
                path: 'add',
                label: '入库信息填写',
                hidden: true,
                element: LazyLoad('putinstorage-add'),
                upRouter: {
                    path: 'putIn',
                    label: '入库管理'
                }
            },
            {
                path: 'detail',
                label: '入库信息详情',
                hidden: true,
                element: LazyLoad('putinstorage-detail'),
                upRouter: {
                    path: 'putIn',
                    label: '入库管理'
                }
            },
            {
                path: 'putOut',
                label: '出库管理',
                element: LazyLoad('putoutstorage-list')
            },
            {
                path: 'Outadd',
                label: '出库信息填写',
                hidden: true,
                element: LazyLoad('putoutstorage-add'),
                upRouter: {
                    path: 'putOut',
                    label: '出库管理'
                }
            },
            {
                path: 'Outdetail',
                label: '出库信息详情',
                hidden: true,
                element: LazyLoad('putoutstorage-detail'),
                upRouter: {
                    path: 'putOut',
                    label: '出库管理'
                }
            }
        ]
    },
    {
        path: '/admin',
        label: '管理员管理',
        hidden: true,
        element: <BasicLayout />,
        children: [
            {
                path: 'password-reset',
                label: '修改密码',
                element: LazyLoad('password-reset')
            }
        ]
    },

    {
        path: '/private',
        label: '私钥管理',
        element: <BasicLayout />,
        children: [
            {
                index: true,
                label: '私钥管理',
                element: LazyLoad('interface-config')
            }
        ]
    },
    {
        path: 'system-log',
        label: '系统日志',
        icon: systemLog,
        showInSider: true,
        canAuth: '系统日志',
        children: [
            {
                index: true,
                element: LazyLoad('system-log')
            }
        ]
    }
];

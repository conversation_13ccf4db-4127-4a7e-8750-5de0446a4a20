/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-11-01 18:23:28
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space } from 'antd';
import useUrlState from '@ahooksjs/use-url-state';
import { ColumnsType } from 'antd/lib/table';

import styles from './index.module.less';
import { addPartiesConfigs, editEmployeesConfigs, searchConfig } from './config';
import SearchForm from '@components/search-form';
import { useRef, useState } from 'react';
import BaseModal from '@components/base-modal';
import FilterForm from '@components/filter-form';
import PageTitle from '@components/page-title';
import { SearchOutlined, PlusOutlined,SyncOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { useAccountList } from '../../myhooks/useaccountlist';
import { useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import dayjs from 'dayjs';
import { traceSourceRecordPage } from '@services/trace-source-record';
import { useMutation, useQuery } from 'react-query';
import { ReformChainError } from '@utils/errorCodeReform';
interface IUrlState {
    pageIndex: number;
    pageSize: number;
}

const PartiesManager = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;
    const navigate = useNavigate();
    const [search]: any = Form.useForm();
    const queryuser: any = useRef(null);
    //数据列表
    const tracequery = useQuery(
        ['tracequery', pageInfo],
        () => {
            return traceSourceRecordPage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                batchNumber: queryuser?.current?.name?.trim() || undefined,
                startTime: queryuser?.current?.Time ? queryuser?.current?.Time[0] : '',
                endTime: queryuser?.current?.Time ? queryuser?.current?.Time[1] : ''
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    //列表数据
    const tableData = tracequery?.data?.data?.records?.map((item: any) => ({
        batchNumber: item.batchNumber,
        dataType: item.dataType,
        companyName: item.companyName,
        optName: item.optName,
        time: dayjs(item.time).format('YYYY-MM-DD HH:mm:ss'),
        hash: item.hash,
        id: item.id
    }));
    const listColumn: ColumnsType<any> = [
        {
            title: '批次号',
            dataIndex: 'batchNumber',
            key: 'batchNumber',
            ellipsis: true
        },
        {
            title: '数据类型',
            dataIndex: 'dataType',
            key: 'dataType',
            ellipsis: true
        },
        {
            title: '企业名称',
            dataIndex: 'companyName',
            key: 'companyName',
            ellipsis: true
        },
        {
            title: '操作人',
            dataIndex: 'optName',
            key: 'optName',
            ellipsis: true
        },
        {
            title: '时间',
            dataIndex: 'time',
            key: 'time',
            ellipsis: true
        },
        {
            title: '哈希',
            dataIndex: 'hash',
            key: 'hash',
            ellipsis: true
        },
        {
            ellipsis: true,
            width: 150,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='small'>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            navigate('/source-data/manage/detail', {
                                state: {
                                    data: record
                                }
                            });
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];
    //查询
    const onFinish = (values: any) => {
        handlePaginationChange(1);
        console.log('values', values);
        values.Time = Array.isArray(values.Time)
            ? [dayjs(values.Time[0]).startOf('date').toISOString(), dayjs(values.Time[1]).endOf('date').toISOString()]
            : values.Time;

        // values.Time[0] = dayjs(values?.Time[0]).startOf('date').toISOString();
        // values.Time[1] = dayjs(values?.Time[1]).startOf('date').toISOString();
        queryuser.current = values;
        console.log('queryuser', queryuser);
        tracequery.refetch();
    };
    return (
        <>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
                title={<PageTitle title='溯源数据列表' />}
            >
                <BaseTable
                    rowKey='account'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return (
                            <TableHead
                                LeftDom={
                                    <div
                                        // className="searchContainer"
                                        className={styles.searchContainer}
                                    >
                                        <Form layout='inline' labelAlign='left' onFinish={onFinish} form={search} className='label-title'>
                                            <FilterForm itemConfig={searchConfig} labelCol={6} />
                                            <BaseButton
                                                type='primary'
                                                // className='searchBtn'
                                                style={{ width: 100 }}
                                                className={`${styles.searchBtn} ${styles.baseBtn}`}
                                                icon={<SearchOutlined rev={undefined} />}
                                                htmlType='submit'
                                            >
                                                查询
                                            </BaseButton>
                                            <BaseButton
                                                type='dashed'
                                                className='primaryBtn'
                                                style={{ width: 100 }}
                                                icon={<SyncOutlined rev={undefined} />}
                                                onClick={() => {
                                                    queryuser.current = '';
                                                    tracequery.refetch();
                                                    search.resetFields();
                                                }}
                                            >
                                                重置
                                            </BaseButton>
                                        </Form>
                                    </div>
                                }
                                RightDom={<div></div>}
                            />
                        );
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={tracequery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={tracequery?.data?.data.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>
        </>
    );
};

export default WithPaginate(PartiesManager);

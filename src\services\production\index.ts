/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-11-01 18:28:55
 * @LastEditTime: 2022-11-01 18:28:55
 * @LastEditors: PhilRandWu
 */
import request from '../request';

// 种植过程分页

export const selectProcessPageLand = (obj: any) => {
  return request({
      url: '/LandPlantBatch/getAvailableNumList',
      method: 'post',
      data: obj
  });
};
//生产过程分页
export const productProcessPage = (obj: any) => {
    return request({
        url: '/process/page',
        method: 'post',
        data: obj
    });
};
//生产详情
export const productionDetail = (obj: any) => {
    return request({
        url: `/process/getDetail`,
        method: 'get',
        params: obj
    });
};
//生产批次详情
export const productionBatchDetail = (obj: any) => {
    return request({
        url: `/production/detail`,
        method: 'get',
        params: obj
    });
};
//生产作废
export const cancelProduction = (obj: any) => {
    return request({
        url: `/production/cancel`,
        method: 'post',
        data: obj
    });
};
//生产作废
export const materialAndPurchaseList = (obj: any) => {
    return request({
        url: `/product/materialAndPurchaseList`,
        method: 'get',
        params: obj
    });
};
//新增生产信息
export const addProduction = (obj: any) => {
    return request({
        url: '/production/add',
        method: 'post',
        data: obj
    });
};
//生产加工分页
export const productionPage = (obj: any) => {
    return request({
        url: '/production/page',
        method: 'post',
        data: obj
    });
};
//  可选择生产过程
export const selectProcessPage = (obj: any) => {
    return request({
        url: '/process/chose-page',
        method: 'post',
        data: obj
    });
};
//  作废生产过程
export const cancelProcess = (obj: any) => {
    return request({
        url: '/process/cancel',
        method: 'post',
        data: obj
    });
};


// 收购列表

export const cancelPurchase = (obj: any) => {
  return request({
      url: '/materialPurchase/getAvailablePurchaseNumList',
      method: 'post',
      data: obj
  });
};
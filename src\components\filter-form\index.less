.avatar-uploader {
    width: auto !important;
    max-width: 300px;
}

.uploadContainer {
    height: 112px;
    display: flex;
    align-items: center;
}
.butcolor {
    border-color: #3d73ef;
    color: #3d73ef;
}
.showtext-item {
    width: 100%; /* 指定宽度 */
    overflow-wrap: break-word; /* 换行处理 */
    white-space: pre-wrap;
    word-break: break-all;
    color: #757575;
}

.ShowScrollText-container {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    max-height: 300px;
    overflow: auto;
    // width: fit-content;
    width: 400px;
}
.tooltip-p {
    width: 400px;
    height: 50px;
    display: block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
}

.filterFormOnlyShow {
    .ant-form-item-label > label {
        height: auto;
    }
    .ant-form-item-control-input {
        min-height: auto;
    }
}
.ant-form {
    a {
        color: #76ae55 !important;
    }
}
.ant-select-disabled {
    background: #f5f5f5;
}

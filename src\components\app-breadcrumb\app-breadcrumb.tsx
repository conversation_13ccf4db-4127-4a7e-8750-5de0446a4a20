import React, { useEffect } from 'react';
import { Breadcrumb } from 'antd';
import { useLocation, useNavigate, useMatch, matchPath, matchRoutes, Link } from 'react-router-dom';

import { useAppDispatch, useAppSelector } from '@store';

import { getInitialRoute, getCurUserAuthRoute } from '@router';

import './app-breadcrumb.less';

const AppBreadcrumb = () => {
    const location = useLocation();
    const navigate = useNavigate();

    const userInfo = useAppSelector((store) => store.user);

    // 使用状态中的权限加载标识
    const isPermissionLoaded = !userInfo.isLogin || userInfo.isPermissionLoaded;

    const matchRoutesRet =
        matchRoutes(getCurUserAuthRoute(userInfo.menuPermissionList, userInfo, isPermissionLoaded), location) || [];
    return (
        <div className='app-breadcrumb-container'>
            <Breadcrumb separator={'>'}>
                {matchRoutesRet.map((item, index) => {
                    return (item.route as any)?.label ? (
                        <Breadcrumb.Item key={index}>
                            {/* @ts-ignore */}
                            {item.route?.element || item.route?.children?.[0]?.index ? (
                                <Link
                                    to={
                                        // TODO 可能有问题
                                        item.route.index ? item.pathnameBase : item.pathname
                                    }
                                >
                                    {(item.route as any)?.label}
                                </Link>
                            ) : (
                                (item.route as any)?.label
                            )}
                        </Breadcrumb.Item>
                    ) : null;
                })}
            </Breadcrumb>
        </div>
    );
};

export default AppBreadcrumb;

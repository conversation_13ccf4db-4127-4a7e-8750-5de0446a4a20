/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 17:15:57
 * @LastEditTime: 2024-10-09 15:51:14
 * @LastEditors: 吴山仁
 */
import BaseCard from '@components/base-card';
import BaseFormItem from '@components/base-form-item';
import PageTitle from '@components/page-title';
import React, { useState } from 'react';
import { Form, message, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useMutation, useQuery } from 'react-query';
import { foodDetail } from '@services/food';
import { enumeration } from './config';
import SwitchList from '@components/switch-list';
import styles from './index.module.less';
import { useLocation } from 'react-router-dom';
import { ReformChainError } from '@utils/errorCodeReform';
import dayjs from 'dayjs';
import { Image } from 'antd';
import FilterForm from '@components/filter-form';

import { FormItemImages, FormItemVideo } from '@components';
import ChainDetailModal from '@components/chain_detail_modal';
import { decryptedUrl, isArrayArr } from '@utils';
import { purchasePageDetail, getChainHisListDetail } from '@services/purchase';
import * as nzh from 'nzh/cn';

const FoodDetail = (props: any) => {
    const { state } = useLocation();
    const [detailForm] = Form.useForm();
    const [transId, setTransId] = useState('');
    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    // console.log("state",state)

    const detailquery = useQuery(
        ['detailquery9999'],
        () => {
            return purchasePageDetail({
                productId: state?.id
            });
        },
        {
            async onSuccess(res) {
                console.log('res0990099', res);
                const arrayData = await Promise.all(
                    isArrayArr(res?.data?.productImg)?.map((item: any) => {
                        return decryptedUrl(item);
                    })
                );
                // setPromotionPicData(arrayData)
                const videos = await decryptedUrl(res?.data?.productVideo);
                detailForm.setFieldsValue({
                    ...res.data,
                    productImg: arrayData && arrayData.length > 0 ? arrayData : null,
                    productVideo: videos ? videos : null,
                    purchaseWeight: res.data.purchaseWeight + '吨',
                    bagCount: res.data.bagCount + '袋',
                    purchaseUnitPrice: res.data.purchaseUnitPrice + '元/吨',
                    purchaseTime: res?.data?.purchaseTime
                        ? dayjs(res?.data?.purchaseTime).format('YYYY-MM-DD HH:mm:ss')
                        : '-'
                });
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    const getChainHisListDetailData = useQuery(
        ['getChainHisListDetail'],
        () => {
            return getChainHisListDetail({
                productId: state?.id
            });
        }
        // {
        //     async onSuccess(res) {
        //         console.log('res0990099', res);
        //         const arrayData = await Promise.all(
        //             isArrayArr(res?.data?.productImg)?.map((item: any) => {
        //                 return decryptedUrl(item);
        //             })
        //         );
        //         // setPromotionPicData(arrayData)
        //         const videos = await decryptedUrl(res?.data?.productVideo);
        //         detailForm.setFieldsValue({
        //             ...res.data,
        //             productImg: arrayData && arrayData.length > 0 ? arrayData : null,
        //             productVideo: videos ? videos : null,
        //             transactionTime: res?.data?.transactionTime
        //                 ? dayjs(res?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
        //                 : '-'
        //         });
        //     },
        //     onError(err: any) {
        //         ReformChainError(err);
        //     }
        // }
    );
    console.log('getChainHisListDetailData', getChainHisListDetailData);

    const foodInfoConfig = [
        {
            label: '地块名称',
            name: 'landName',
            value: 'landName',
            type: 'ShowScrollText',
            span: 8
        },
        {
            label: '农作物类型',
            name: 'plantName',
            value: 'plantName',
            type: 'ShowText',
            span: 8
        },
        {
            label: '收购对接人',
            name: 'userName',
            value: 'userName',
            type: 'ShowText',
            span: 8
        },
        {
            label: '收购批次',
            name: 'purchaseBatch',
            value: 'purchaseBatch',
            type: 'ShowText',
            span: 8
        },
        // {
        //     label: '产品编码',
        //     name: 'code',
        //     value: 'productCode',
        //     type: 'ShowText'
        // },
        {
            label: '收购日期',
            name: 'purchaseTime',
            value: 'purchaseTime',
            type: 'ShowText',
            span: 8
        },
        {
            label: '收购重量',
            name: 'purchaseWeight',
            value: 'purchaseWeight',
            type: 'ShowText',
            span: 8
        },
        {
            label: '装袋数量',
            name: 'bagCount',
            value: 'bagCount',
            type: 'ShowText',
            span: 8
        },
        {
            label: '收购单价',
            name: 'purchaseUnitPrice',
            value: 'purchaseUnitPrice',
            type: 'ShowText',
            span: 8
        },
        {
            label: '种植批次',
            name: 'plantBatch',
            value: 'plantBatch',
            type: 'ShowText',
            span: 8
        }
        // {
        //     label: '产品合格证明',
        //     name: 'productAptitude',
        //     value: 'productAptitude',
        //     type: 'Custom',
        //     children: (
        //         <Form.Item key='promotionPic' name='productAptitude'>
        //             <FormItemImages width={150} />
        //         </Form.Item>
        //     )
        // }
    ];

    const productionConfig = [
        {
            label: '农户姓名',
            name: 'farmerName',
            value: 'farmerName',
            type: 'ShowScrollText',
            span: 8
        },
        {
            label: '联系方式',
            name: 'phoneNumber',
            value: 'phoneNumber',
            type: 'ShowScrollText',
            span: 8
        },
        {
            label: '被收购次数',
            name: 'materialPurchaseCount',
            value: 'materialPurchaseCount',
            type: 'ShowScrollText',
            span: 8
        }
        // {
        //     label: '宣传图片',
        //     name: 'productImg',
        //     value: 'productImg',
        //     type: 'Custom',
        //     children: (
        //         <Form.Item key='productImg' name='productImg'>
        //             <FormItemImages height={100}></FormItemImages>
        //         </Form.Item>
        //     )
        // }
        // {
        //     label: '宣传视频',
        //     name: 'productVideo',
        //     value: 'productVideo',
        //     type: 'Custom',
        //     children: (
        //         <Form.Item key='productVideo' name='productVideo'>
        //             <FormItemVideo width={200} controls></FormItemVideo>
        //         </Form.Item>
        //     )
        // }
    ];

    const ChainDetailModalConfig = {
        transactionId: detailForm.getFieldValue('transactionId'),
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };

    const chainConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Link',
            onClick: () => {
                setChainDetailModalVisible(true);
            }
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];

    const onFinish = (values: any) => {
        console.log('Success:', values);
    };

    const onFinishFailed = (errorInfo: any) => {
        console.log('Failed:', errorInfo);
    };
    console.log('detailquery?.data?.data?.material', detailquery?.data?.data?.material);
    return (
        <div>
            <BaseCard title={<PageTitle title='收购管理详情' bg='container chan' />}>
                <Form
                    name='basic'
                    form={detailForm}
                    onFinish={onFinish}
                    onFinishFailed={onFinishFailed}
                    autoComplete='off'
                >
                    {/* <PageTitle title='食品信息' type='primaryIcon' />
                    <BaseFormItem configs={foodInfoConfig} /> */}
                    <PageTitle title='农户信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={productionConfig} labelCol={false} />

                    <PageTitle title='收购信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={foodInfoConfig} labelCol={false} />

                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <div>
                        {getChainHisListDetailData?.data?.data.map((item: any, index: any) => {
                            return index == 0 ? (
                                <div
                                    key={index}
                                    style={{
                                        display: 'flex',
                                        margin: '20px 0',
                                        color: '#9b9b9b',
                                        width: '1000px',
                                        justifyContent: 'space-between'
                                    }}
                                >
                                    <span style={{ marginRight: '40px' }}>
                                        最新链上哈希&nbsp;
                                        <Tooltip title='信息的链上的哈希值'>
                                            <QuestionCircleOutlined />
                                        </Tooltip>
                                        &nbsp; :&nbsp;&nbsp;&nbsp;{' '}
                                        <a
                                            onClick={() => {
                                                setTransId(item.transactionId);
                                                setChainDetailModalVisible(true);
                                            }}
                                        >
                                            {item.transactionId}
                                        </a>{' '}
                                    </span>

                                    <span>
                                        最新上链时间&nbsp;
                                        <Tooltip title='信息上链的时间'>
                                            <QuestionCircleOutlined />
                                        </Tooltip>
                                        &nbsp; :&nbsp;&nbsp;&nbsp;{' '}
                                        {dayjs(item.transactionTime).format('YYYY-MM-DD HH:mm:ss')}
                                    </span>
                                </div>
                            ) : (
                                <div
                                    key={index}
                                    style={{
                                        display: 'flex',
                                        margin: '20px 0',
                                        color: '#9b9b9b',
                                        width: '1000px',
                                        justifyContent: 'space-between'
                                    }}
                                >
                                    <span style={{ marginRight: '40px' }}>
                                        第{nzh.encodeS(item.order)}次链上哈希&nbsp;
                                        <Tooltip title='信息的链上的哈希值'>
                                            <QuestionCircleOutlined />
                                        </Tooltip>
                                        &nbsp; :&nbsp;&nbsp;&nbsp;{' '}
                                        <a
                                            onClick={() => {
                                                setTransId(item.transactionId);
                                                setChainDetailModalVisible(true);
                                            }}
                                        >
                                            {item.transactionId}
                                        </a>
                                    </span>

                                    <span>
                                        第{nzh.encodeS(item.order)}次上链时间&nbsp;
                                        <Tooltip title='信息上链的时间'>
                                            <QuestionCircleOutlined />
                                        </Tooltip>
                                        &nbsp; :&nbsp;&nbsp;&nbsp;{' '}
                                        {dayjs(item.transactionTime).format('YYYY-MM-DD HH:mm:ss')}
                                    </span>
                                </div>
                            );
                        })}
                        {/* console.log(nzh.encodeS(8)); */}
                    </div>
                    {/* <FilterForm showMode itemConfig={chainConfig} labelCol={false} /> */}
                    {/* <Form.Item
                        className={props?.className}

                        // labelCol={{ flex: 'auto' }}
                        labelCol={
                            props?.labelCol !== false
                                ? { span: props?.labelCol !== undefined ? props?.labelCol : 6 }
                                : {}
                        }
                        wrapperCol={{ span: props?.wrapperCol !== undefined ? props?.wrapperCol : 15 }}
                        key={config?.value}
                        label={config?.label}
                        name={config?.value}
                        rules={config?.rules}
                        tooltip={config?.title}

                    >
                        {config?.display ? config?.display : formItemBuilder(config)}

                    </Form.Item> */}
                </Form>
            </BaseCard>
            {/* <ChainDetailModal {...ChainDetailModalConfig} /> */}
            <ChainDetailModal
                transactionId={transId}
                open={ChainDetailModalVisible}
                onCancel={() => setChainDetailModalVisible(false)}
            />
        </div>
    );
};

export default FoodDetail;

# 敏感词库功能说明

## 功能概述

本次更新为系统添加了完整的敏感词库管理功能，包含三个子模块：
- 敏感词管理
- 分词管理  
- 白名单管理

## 功能特性

### 1. 敏感词管理
- **功能描述**: 管理系统中的敏感词汇，支持按分类和级别进行管理
- **主要功能**:
  - 敏感词的增删改查
  - 支持按状态筛选（启用/禁用）
  - 支持关键词搜索
  - 分页显示
  - 弹框式编辑

### 2. 分词管理
- **功能描述**: 管理文本分词规则，用于文本分析和处理
- **主要功能**:
  - 分词的增删改查
  - 支持按类型分类（名词、动词、形容词、副词）
  - 显示使用频次统计
  - 支持状态管理
  - 关键词搜索功能

### 3. 白名单管理
- **功能描述**: 管理不需要进行敏感词检测的词汇白名单
- **主要功能**:
  - 白名单词汇的增删改查
  - 支持按分类管理（专业术语、品牌名称、地名、人名等）
  - 记录添加原因
  - 状态管理功能
  - 搜索和筛选

## 技术实现

### 路由配置
在 `src/router/routers.tsx` 中添加了敏感词库的路由配置：

```typescript
{
    path: 'sensitive-words',
    label: '敏感词库',
    icon: FileTextOutlined,
    showInSider: true,
    canAuth: '敏感词库',
    children: [
        {
            path: 'management',
            label: '敏感词管理',
            showInSider: true,
            canAuth: '敏感词管理',
            children: [
                {
                    index: true,
                    element: LazyLoad('sensitive-words/management')
                }
            ]
        },
        // ... 其他子路由
    ]
}
```

### 页面组件
创建了三个主要页面组件：

1. **敏感词管理页面**: `src/pages/sensitive-words/management/index.tsx`
2. **分词管理页面**: `src/pages/sensitive-words/segmentation/index.tsx`
3. **白名单管理页面**: `src/pages/sensitive-words/whitelist/index.tsx`

### 组件特性
- 使用项目统一的基础组件（BaseCard、BaseTable、BaseModal等）
- 响应式设计，支持移动端适配
- 统一的样式风格，与项目整体风格保持一致
- 完整的表单验证和错误处理
- 支持分页和搜索功能

### 样式设计
每个页面都有对应的样式文件：
- `src/pages/sensitive-words/management/index.module.less`
- `src/pages/sensitive-words/segmentation/index.module.less`
- `src/pages/sensitive-words/whitelist/index.module.less`

样式特点：
- 使用CSS Modules避免样式冲突
- 响应式布局设计
- 统一的颜色主题和交互效果
- 良好的用户体验设计

## 数据结构

### 敏感词数据结构
```typescript
interface SensitiveWord {
    id: string;
    word: string;
    category: string;  // 分类：政治类、暴力类、色情类
    level: string;     // 级别：高、中、低
    status: 'active' | 'inactive';
    createTime: string;
    updateTime: string;
}
```

### 分词数据结构
```typescript
interface SegmentationWord {
    id: string;
    word: string;
    type: string;      // 类型：名词、动词、形容词、副词
    frequency: number; // 使用频次
    status: 'active' | 'inactive';
    createTime: string;
    updateTime: string;
}
```

### 白名单数据结构
```typescript
interface WhitelistWord {
    id: string;
    word: string;
    category: string;  // 分类：专业术语、品牌名称、地名、人名
    reason: string;    // 添加原因
    status: 'active' | 'inactive';
    createTime: string;
    updateTime: string;
}
```

## 使用说明

### 访问路径
- 敏感词管理: `/sensitive-words/management`
- 分词管理: `/sensitive-words/segmentation`
- 白名单管理: `/sensitive-words/whitelist`

### 操作流程
1. **查看列表**: 进入对应页面即可查看数据列表
2. **搜索筛选**: 使用顶部搜索框和筛选条件
3. **添加数据**: 点击"新建"按钮，填写表单信息
4. **编辑数据**: 点击列表中的"编辑"按钮
5. **删除数据**: 点击列表中的"删除"按钮

### 权限控制
系统支持基于角色的权限控制：
- `敏感词库`: 访问敏感词库模块的基础权限
- `敏感词管理`: 管理敏感词的权限
- `分词管理`: 管理分词的权限
- `白名单管理`: 管理白名单的权限

## 后续扩展

### API接口集成
当前使用模拟数据，后续可以集成真实的API接口：

```typescript
// 示例API调用
const fetchSensitiveWords = async (params: any) => {
    const response = await axios.get('/api/sensitive-words', { params });
    return response.data;
};
```

### 功能增强
- 批量导入/导出功能
- 敏感词检测API
- 统计分析功能
- 审核流程
- 版本管理

## 注意事项

1. **数据安全**: 敏感词数据需要严格的权限控制
2. **性能优化**: 大量数据时需要考虑分页和搜索优化
3. **用户体验**: 保持与项目整体风格的一致性
4. **扩展性**: 预留接口便于后续功能扩展

## 技术栈

- React 18
- TypeScript
- Ant Design 4.x
- Less/CSS Modules
- React Router
- React Query (用于数据管理)

该功能完全集成到现有项目中，遵循项目的代码规范和设计模式，可以无缝使用。

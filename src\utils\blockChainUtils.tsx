// import { showPasswordInput } from '@store/moudle/app';
import { showPasswordInput } from '@store/slice-reducer/app';
import { message } from 'antd';
import md5 from 'js-md5';
// var hashlib = require('hashlib');

// import CryptoJS from 'crypto-js';
// import ecc from 'eosjs-ecc';
// import eos from 'eosjs'
const CryptoJS = require('crypto-js'); //引用AES源码js
const ecc = require('eosjs-ecc');
const eos = require('eosjs');
const encryptedIv = 'tNM33MyJcLt0tCHv';

/**
 * 登录部分用于派发公私钥，验证登录状态
 * 校验密码有效性,随机生成私钥;之前场景下密码低于 8 位时会导致无法成功加解密私钥,此处需要进一步验证密码规则
 * @param password 口令密码
 * @param callback
 * @returns
 */
const generateValidPrivateKey = (password: string, callback: (error: any, result?: any) => void) => {
    if (!password) {
        callback('请输入有效的密码');
        return;
    }
    const encryptKey = password;
    ecc.randomKey()
        .then((privateKey: string) => {
            if (privateKey) {
                if (callback) {
                    const encryptedPrivateKey = encrypt(privateKey, encryptKey);
                    // 对私钥加密成功校验能否解密
                    const tryDecrypt = decrypt(encryptedPrivateKey, encryptKey);
                    if (privateKey === tryDecrypt) {
                        // 成功获取 公钥 和 加密后的私钥 后,传给服务端创建账户即可
                        callback(undefined, {
                            privateKey: privateKey,
                            encryptedPrivateKey: encryptedPrivateKey,
                            publicKey: ecc.privateToPublic(privateKey),
                            operationKey: md5(encryptKey)
                        });
                    } else {
                        message.error('密码无效,请重新设置');
                        callback(Error);
                    }
                }
            } else {
                message.error('随机密钥生成失败,请重试或联系管理员');
                if (callback) {
                    callback(Error);
                }
            }
        })
        .catch((error: any) => {
            if (callback) {
                callback(error);
            }
            message.error('随机密钥生成失败,请重试或联系管理员');
        });
};

/**
 * 解密私钥-此方法不应该手动在代码中调用,代码中直接调用下述方法 getLocalPrivatekey 接口获取本地私钥
 * @param password 口令密码
 * @returns
 */
const decryptPrivateKey = (password: string) => {
    // generateValidPrivateKey('password',(error,result) =>{
    //     if (!error && result) {
    //     }
    // })

    if (!password) {
        message.info('请输入密码');
        return false;
    }
    const localencryptKey = sessionStorage.getItem('encryptKey');
    if (!localencryptKey) {
        return false;
    }
    try {
        

        const privateKey = decrypt(localencryptKey, password);

        if (privateKey) {
            sessionStorage.setItem('privateKey', privateKey);
            return true;
        }
        return false;
    } catch (error) {
        return false;
    }
};

/**
 * 项目代码中调用此方法获取解密之后的私钥(内在逻辑:如果本地没有解密之后的私钥则弹出密码框让用户输入口令密码进行解密)
 * @param dispatch 此处根据具体项目配置
 * @returns
 */
const getLocalPrivatekey = (dispatch: any) => {
    const localprivatekey = sessionStorage.getItem('privateKey');
    if (!localprivatekey) {
        message.info('请输入密码');
        // 弹出密码输入框, 项目如果使用了 redux 可参考下列触发. (header-content 页面中进行了账号密码输入 UI)
        dispatch(
            showPasswordInput({
                showPasswordInput: true
            })
        );
        return '';
    }
    return localprivatekey;
};

/**
 * 加密方法-此方法外部无需调用
 * @param privatekey 原私钥
 * @param encryptKey 口令密码
 * @returns
 */
function encrypt(privatekey: string, encryptKey: string) {
    const passwordMd5 = md5(encryptKey);
    const key = CryptoJS.enc.Utf8.parse(passwordMd5); //十六位十六进制数作为密钥
    const iv = CryptoJS.enc.Utf8.parse(encryptedIv); //十六位十六进制数作为密钥偏移量
    let srcs = CryptoJS.enc.Utf8.parse(privatekey);
    let encrypted = CryptoJS.AES.encrypt(srcs, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.ciphertext.toString().toUpperCase();
}

/**
 * 加密方法-此方法外部无需调用
 * @param privatekey 加密后的私钥
 * @param encryptKey 口令密码
 * @returns
 */
function decrypt(privatekey: string | null, encryptKey: string) {
    const passwordMd5 = md5(encryptKey);
    const key = CryptoJS.enc.Utf8.parse(passwordMd5);
    const iv = CryptoJS.enc.Utf8.parse(encryptedIv); //十六位十六进制数作为密钥偏移量
    let encryptedHexStr = CryptoJS.enc.Hex.parse(privatekey);
    let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
    let decrypt = CryptoJS.AES.decrypt(srcs, key, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
    return decryptedStr.toString();
}

/**
 * 口令密码哈希处理
 * @param password 口令密码
 * @returns
 */
function toHash(password: string) {
    return CryptoJS.SHA256(password).toString(CryptoJS.enc.Hex);
}

/**
 * 签名数据
 * @param data 需要签名的数据
 * @param dispatch
 * @param callback
 */
function signData(dispatch: any, data: string, callback: (error: any, result?: any) => void) {
    try {
        // const localprivatekey = sessionStorage.getItem('privateKey');
        // const localprivatekey = '5JUsKf5q4EgdKsnwoSLsxVAjGp7h8rM6fJQkH5w4jbRc81J1DGk';
        const localprivatekey = getLocalPrivatekey(dispatch);
        if (!localprivatekey) {
            if (callback) callback('misprivatekey');
            return;
        }
        // const signature = eos.modules.ecc.sign(data, localprivatekey).toString()
        const signature = ecc.sign(data, localprivatekey);
        if (callback) callback(undefined, signature);
        // callback(undefined,'sdcsc')
    } catch (error) {
        if (callback) callback(error);
    }
}

/**
 * 检查账户密码
 * @param password
 * @returns
 */
export const checkAndSavePassword = (password: string) => {
    if (!password) {
        message.info('请输入密码');
        return '';
    }
    try {
        const privateKey = decryptPrivateKey(password);
        if (privateKey) {
            return privateKey;
        }
        return '';
    } catch (error) {
        return '';
    }
};

export { generateValidPrivateKey, decryptPrivateKey, getLocalPrivatekey, encrypt, decrypt, toHash, signData };

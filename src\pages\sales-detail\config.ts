/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 17:33:31
 * @LastEditTime: 2022-11-01 18:12:31
 * @LastEditors: PhilRandWu
 */
export const foodInfoConfig  = [
    {
        label: '食品编号',
        name: 'number'
    },
    {
        label: '食品名称',
        name: 'name'
    },
    {
        label: '食品编码',
        name: 'code'
    },
    {
        label: '食品品类',
        name: 'type'
    },
    {
        label: '食品单位',
        name: 'unit'
    },
    {
        label: '保质期',
        name: 'shelfLife'
    },
    {
        label: '食品规格',
        name: 'size'
    },
    // {
    //     label: '区块号',
    //     name: 'chainHeight'
    // },
    {
        label: '链上哈希',
        name: 'hash'
    }
];
export const enumeration:any  = { 
        productionDate:'生产日期',
        productionBatch : '生产批次',
        productionLine : '生产线',
        productionShift : '种植户',
        productionPlace : '生产地点',
        environmentInfo : '生产环境信息',
        personLiable : '责任人员',
        contactNumber : '联系电话',
        checkTime: '抽检时间',
        checkRecord : '抽检记录',
        sampleTime: '留样时间',
        sampleRecord : '留样记录',
        productionAccessory : '附件',
        foodNumber: '食品批号',
        testRecord : '质检内容',
        testTime: '质检时间',
        testResult : '质检结果',
        testPersonnel : '质检人员',
        testReport : '质检报告',
        phone: '联系电话',
        testAccessory : '附件'
}


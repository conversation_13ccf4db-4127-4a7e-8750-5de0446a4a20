/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-07-21 15:05:35n
 * @LastEditTime: 2022-11-01 18:28:13
 * @LastEditors: PhilRandWu
 */
import { roleIdentity } from '@config';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { userInfo } from 'os';

export interface IUserState {
    privateKey?: string;
    publicKey: any;
    isLogin: boolean;
    menuPermissionList: string[];
    userInfo: any;
    showPasswordInput: boolean;
    isFill?: any;
    identity: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8;
    role: '平台方' | '生产加工企业' | '供应商' | '质检机构' | '监管机构' | '物流企业'| '运营方'| '销售企业';
    isPermissionLoaded: boolean;
}
// const publicKey = sessionStorage.getItem('publicKey')!;
// console.log("publicKey9129399",publicKey)
// const privateKey = sessionStorage.getItem('privateKey')!;
const jwt = sessionStorage.getItem('jwt');

const initialState: IUserState = {
    isLogin: false,
    menuPermissionList: [],
    userInfo: {},
    privateKey: '',
    publicKey: '',
    showPasswordInput: false,
    isFill: 0,
    identity: 1,
    role: '生产加工企业',
    isPermissionLoaded: false,
};

const userSlice = createSlice({
    name: 'user',
    initialState,
    reducers: {
        loginAction: (
            state: IUserState,
            action: PayloadAction<{
                userInfo: IUserState['userInfo'];
            }>
        ) => {
            const payload = action.payload;
            state.isLogin = true;
            state.role = roleIdentity[payload.userInfo.identity as 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8] as '平台方' | '生产加工企业' | '供应商' | '质检机构' | '监管机构' | '物流企业' | '运营方' | '销售企业'
            state.userInfo = payload.userInfo;
            // sessionStorage.setItem('publicKey', payload.userInfo.public_key);
            state.publicKey = payload.userInfo.publicKey
            state.privateKey = payload.userInfo.privateKey
            state.isFill = payload.userInfo.isFill
            console.log( state.role)
        },
        logoutAction: (store: IUserState) => {
            return { ...initialState, isLogin: false, isPermissionLoaded: false };
        },
        updatefirstLogin: (store: IUserState) => {
            store.userInfo.firstLogin = false;
            const origionLoc = JSON.parse(sessionStorage.getItem('userInfo')!);
            sessionStorage.setItem('userInfo', JSON.stringify({
                ...origionLoc,
                firstLogin: store.userInfo.firstLogin
            }))
        },
        updataisFill: (store: IUserState) => {
            const isFill = sessionStorage.getItem('isFill');
            store.isFill = isFill;
        },
        permissionAction: (
            state: IUserState,
            action: PayloadAction<{
                menuPermissionList: IUserState['menuPermissionList'];
            }>) => {
            const payload = action.payload;
            state.menuPermissionList = payload.menuPermissionList;
            state.isPermissionLoaded = true;
        },
        updateKeyWord: (store: IUserState) => {
            const privateKey = sessionStorage.getItem('privateKey');
            const publicKey = sessionStorage.getItem('publicKey');
            store.privateKey = privateKey!;
            store.publicKey = publicKey!;
        },
        psdState(state: IUserState, action) {
            state.showPasswordInput = action.payload;
        },
    }
});

export const { loginAction, logoutAction, updatefirstLogin, updateKeyWord, updataisFill, permissionAction,
    psdState: psdStateAction, } = userSlice.actions;

export default userSlice.reducer;

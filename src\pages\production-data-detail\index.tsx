import { useEffect, useState } from 'react';
import { Col, Form, message, Row, Space, Descriptions, Table } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';

import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';
import BaseInput from '@components/base-input/base-input';
import BaseSelect from '@components/base-select/base-select';
import BaseDatePicker from '@components/base-date-picker';

import styles from './index.module.less';
import { useForm } from 'antd/lib/form/Form';
import FilterForm from '@components/filter-form';
import SourceDataService from '@services/traceability_data/source_data';
import ChainDetailModal from '@components/chain_detail_modal';
import BaseDescriptions from '@components/base-descriptions';
import { decryptedUrl } from '@utils';

const IconStyle: React.CSSProperties = {
    display: 'inline-block',
    width: 16,
    height: 16,
    fontSize: 12,
    background: '#cecece',
    color: '#fff',
    borderRadius: 100,
    textAlign: 'center',
    margin: '0 6px 0 6px'
};

const FleeWarning = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    const [productionAccessory, setProductionAccessory] = useState<any>();

    const productDetail = useQuery(['productDetail'], () => SourceDataService.detail(Number(id)), {
        onSuccess: async (res) => {
            const productionAccessory = await decryptedUrl(res?.data?.productionAccessory);
            setProductionAccessory(productionAccessory);
        }
    });
    const productDetailData = productDetail?.data?.data;

    const columns: ColumnsType<any> = [
        {
            title: '过程编号',
            dataIndex: 'id',
            key: 'id',
            ellipsis: true
        },

        {
            title: '过程名称',
            dataIndex: 'processName',
            key: 'processName',
            ellipsis: true
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true,
            render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
        }
    ];

    const columnsLand: ColumnsType<any> = [
        {
            title: '地块名称',
            dataIndex: 'landName',
            key: 'landName',
            ellipsis: true
        },
        {
            title: '种植批次',
            dataIndex: 'plantBatch',
            key: 'plantBatch',
            ellipsis: true
        },
        {
            title: '农作物类型',
            dataIndex: 'plantName',
            key: 'plantName',
            ellipsis: true
        },

        {
            title: '播种时间',
            dataIndex: 'sowTime',
            key: 'sowTime',
            ellipsis: true,
            render: (time) => {
                return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
            }
        },
        {
            title: '收割时间',
            dataIndex: 'harvestTime',
            key: 'harvestTime',
            ellipsis: true,
            render: (time) => {
                return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '';
            }
        }
        // {
        //     title: '可加工数量',
        //     dataIndex: 'availableNum',
        //     key: 'availableNum',
        //     ellipsis: true
        // }
    ];

    const columnsPurchase: ColumnsType<any> = [
        {
            title: '收购批次',
            dataIndex: 'purchaseBatch',
            key: 'purchaseBatch',
            ellipsis: true
        },

        {
            title: '收购时间',
            dataIndex: 'purchaseTime',
            key: 'purchaseTime',
            ellipsis: true,
            render: (time: any) => {
                return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
            }
        },
        {
            title: '农作物类型',
            dataIndex: 'plantName',
            key: 'plantName',
            ellipsis: true
        },
        {
            title: '收割时间',
            dataIndex: 'harvestTime',
            key: 'harvestTime',
            ellipsis: true,
            render: (time: any) => {
                return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
            }
        },

        {
            title: '可加工数量(吨)',
            dataIndex: 'availablePurchaseWeight',
            key: 'availablePurchaseWeight',
            ellipsis: true
        }
    ];
    const [ChainForm] = useForm();
    const chainConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Link',
            onClick() {
                setChainDetailModalVisible(true);
            }
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];

    useEffect(() => {
        ChainForm.setFieldsValue({
            transactionId: productDetailData?.transactionId || '-',
            transactionTime: dayjs(productDetailData?.transactionTime).format('YYYY-MM-DD HH:mm:ss') || '-'
        });
    }, [productDetailData]);

    const ChainDetailModalConfig = {
        transactionId: productDetailData?.transactionId,
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };

    return (
        <>
            <BaseCard
                className={styles.coreFIrmContainer}
                title={<PageTitle title='生产溯源数据详情' bg='container pulic' />}
            >
                {productDetailData?.materialList && productDetailData.materialList.length > 0 && (
                    <PageTitle title='原料信息' type='primaryIcon' bmagin={16} />
                )}
                {productDetailData?.materialList && productDetailData.materialList.length > 0 && (
                    <BaseDescriptions style={{ marginBottom: 36 }}>
                        {productDetailData?.materialList?.map((item: any, index: number) => (
                            <Descriptions.Item label={`原料${index + 1}`}>{item?.materialName}</Descriptions.Item>
                        ))}
                    </BaseDescriptions>
                )}
                <PageTitle title='生产加工信息' type='primaryIcon' bmagin={16} />
                <BaseDescriptions style={{ marginBottom: 36 }}>
                    <Descriptions.Item label='产品名称'>{productDetailData?.productName}</Descriptions.Item>
                    <Descriptions.Item label='生产批次'>{productDetailData?.productionBatch}</Descriptions.Item>
                    <Descriptions.Item label='数量'>{productDetailData?.amount}</Descriptions.Item>
                    <Descriptions.Item label='生产线'>{productDetailData?.line || '-'}</Descriptions.Item>
                    <Descriptions.Item label='种植户'>{productDetailData?.grower || '-'}</Descriptions.Item>
                    <Descriptions.Item label='附件'>
                        {productDetailData?.productionAccessory && productionAccessory ? (
                            <a href={productionAccessory}>下载</a>
                        ) : (
                            '-'
                        )}
                    </Descriptions.Item>
                </BaseDescriptions>
                <PageTitle title='生产过程' type='primaryIcon' bmagin={16} />
                <Descriptions style={{ marginBottom: 8 }}></Descriptions>
                <Table
                    columns={columns}
                    dataSource={productDetailData?.processList}
                    style={{ width: '50%', marginBottom: 36 }}
                    pagination={false}
                />

                <PageTitle title='收购过程' type='primaryIcon' bmagin={16} />
                <Descriptions style={{ marginBottom: 8 }}></Descriptions>
                <Table
                    columns={columnsPurchase}
                    dataSource={productDetailData?.materialPurchaseList}
                    style={{ width: '80%', marginBottom: 36 }}
                    pagination={false}
                />

                <PageTitle title='种植过程' type='primaryIcon' bmagin={16} />
                <Descriptions style={{ marginBottom: 8 }}></Descriptions>
                <Table
                    columns={columnsLand}
                    dataSource={productDetailData?.landPlantBatchList}
                    style={{ width: '80%', marginBottom: 36 }}
                    pagination={false}
                />

                <Form form={ChainForm}>
                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={chainConfig} labelCol={false} />
                </Form>
            </BaseCard>
            <ChainDetailModal {...ChainDetailModalConfig} />
        </>
    );
};

export default FleeWarning;

/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:27:42
 * @LastEditTime: 2022-11-01 18:14:03
 * @LastEditors: PhilRandWu
 */
export const partiesConfig = [
    {
        label: '参与方名称',
        name: 'name',
        type: 'Input',
        value: 'name',
        placeholder: '请输入',
        rules: [{ required: true, message: '' },
        () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,40}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入参与方名称!');
                        } else if (verify === false) {
                            callback('请保持字符在40字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
        ]
    },
    {
        label: '参与方类型',
        name: 'type',
        type: 'Radio',
        value: 'type',
        placeholder: '请选择',
        rules: [{ required: true, message: '请选择' }],
        fields: [
            {
                value: 2,
                label: '供应商'
            },
            {
                value: 3,
                label: '质检机构'
            },
            {
                value: 4,
                label: '监管机构'
            }
        ]
    },
    {
        label: '备注',
        name: 'note',
        showCount: true,
        type: 'TextArea',
        value: 'permission',
        placeholder: '请选择',
        rules: [
        () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\x21-\x2F\x3A-\x40\x5B-\x60\x7B-\x7E\u4e00-\u9fa5_a-zA-Z0-9_]{0,200}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            return;
                        } else if (verify === false) {
                            callback('请保持字符在200字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
        ]
    }
];

export const managerConfig = [
    {
        label: '管理员名称',
        name: 'managerName',
        className: 'manager',
        type: 'Input',
        value: 'managerName',
        placeholder: '请输入',
        rules: [
            { required: true, message: '' },
            () => ({
                validator: (_: any, value: any, callback: any) => {
                    const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,30}$/);
                    const verify = regExp.test(value);
                    if (!value) {
                        callback('请输入管理员名称!');
                    } else if (verify === false) {
                        if (value.length >= 30) {
                            callback('请保持字符在30字符以内!');
                        } else {
                            callback('请输入管理员名称，支持中文、字母或数字!');
                        }
                    } else {
                        callback();
                    }
                }
            })
        ]

    },
    {
        label: '联系方式',
        className: 'manager',
        name: 'phone',
        type: 'Input',
        value: 'phone',
        placeholder: '请输入',
        rules: [
            { required: true, message: '' },
            () => ({
                validator: (_: any, value: any, callback: any) => {
                    const regExp = new RegExp(/^(?:(?:\+|00)86)?1[3-9]\d{9}$/);
                    const verify = regExp.test(value);
                    if (!value) {
                        callback('请输入联系方式')
                    } else if (verify === false) {
                        callback('请输入正确的手机号');
                    } else {
                        callback();
                    }
                }
            })
        ]
    }
];

export const managerDisplayConfig = [
    {
        label: '管理员名称',
        name: 'managerName'
    },
    {
        label: '联系方式',
        name: 'phone'
    },
    {
        label: '管理员账号',
        name: 'managerAccount'
    }
];

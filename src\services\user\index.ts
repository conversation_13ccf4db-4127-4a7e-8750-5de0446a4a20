/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-11-01 18:28:26
 * @LastEditTime: 2022-11-01 18:28:26
 * @LastEditors: PhilRandWu
 */
import request from '../request';
//生产加工企业员工接口
export const Staffadd = (obj: any) => {
    return request({
        url: '/user/addStaff',
        method: 'post',
        data: obj
    });
};
//生产加工企业员工分页查询
export const staffPage = (obj: any) => {
    // console.log("obj",obj)
    return request({
        url: '/user/staffPage',
        method: 'post',
        data: obj
    });
};
//核心员工添加
export const addStaff = (obj: any) => {
    // console.log("obj",obj)
    return request({
        url: '/user/addStaff',
        method: 'post',
        data: obj
    });
};
//编辑员工账号
export const updateStaff = (obj: any) => {
    // console.log("obj",obj)
    return request({
        url: '/user/updateStaff',
        method: 'post',
        data: obj
    });
};
//用户添加
export const UserAdd = (obj: any) => {
    return request({
        url: '/user/addAdmin',
        method: 'post',
        data: obj
    });
};

//用户列表查询
export const userPage = (obj: any) => {
    return request({
        url: '/user/adminPage',
        method: 'post',
        data: obj
    });
};
//修改用户状态
export const userModiy = (obj: any) => {
    // console.log("obj",obj)
    return request({
        url: `/user/updateState`,
        method: 'post',
        data: obj
    });
};
//修改员工状态
export const staffModiy = (obj: any) => {
    // console.log("obj",obj)
    return request({
        url: `/user/updateStaffState`,
        method: 'post',
        data: obj
    });
};
//编辑用户
export const uptdateUser = (obj: any) => {
    // console.log("obj",obj)
    return request({
        url: '/user/updateAdmin',
        method: 'post',
        data: obj
    });
};
//重置密码
export const resetPassWord = (obj: any) => {
    // console.log("obj",obj)
    return request({
        url: '/user/resetAdminPassword',
        method: 'post',
        data: obj
    });
};
//重置员工密码
export const resetStaffPassWord = (obj: any) => {
    // console.log("obj",obj)
    return request({
        url: '/user/resetStaffPassword',
        method: 'post',
        data: obj
    });
};
//修改密码
export const modifyPassword = (obj: any) => {
    // console.log("obj",obj)
    return request({
        url: '/user/modifyPassword',
        method: 'post',
        data: obj
    });
};
//设置私钥
export const requestValid = (obj: any) => {
    // console.log("obj",obj)
    return request({
        url: '/user/modifyPk',
        method: 'post',
        data: obj
    });
};

//获取用户信息
export const getUserInfo = () => {
    return request({
        url: '/user/info',
        method: 'get',
    });
};
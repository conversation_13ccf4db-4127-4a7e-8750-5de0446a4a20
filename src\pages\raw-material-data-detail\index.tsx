import {useEffect, useState} from 'react';
import {Col, Form, Image, message, Row, Space, Descriptions, ModalProps} from 'antd';
import {ColumnsType} from 'antd/lib/table';
import {useNavigate, useParams} from 'react-router-dom';

import BaseCard from '@components/base-card';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';

import styles from './index.module.less';
import BaseModal from '@components/base-modal';
import {useForm} from 'antd/lib/form/Form';
import FilterForm from '@components/filter-form';
import RawMaterialService from "@services/traceability_data/raw_material";
import {useMutation, useQuery} from "react-query";
import dayjs from "dayjs";
import ChainDetailModal from "@components/chain_detail_modal";
import BaseDescriptions from '@components/base-descriptions';

const IconStyle: React.CSSProperties = {
    display: 'inline-block',
    width: 16,
    height: 16,
    fontSize: 12,
    background: '#cecece',
    color: '#fff',
    borderRadius: 100,
    textAlign: 'center',
    margin: '0 6px 0 6px'
};

const FleeWarning = () => {
    const {id} = useParams();
    const [visible, setVisible] = useState(false);
    const [ChainFormA] = useForm();
    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);

    const rawMaterialDetail = useQuery(["rawMaterialDetail"], () => RawMaterialService.detail(Number(id)));
    const rawMaterialDetailDate = rawMaterialDetail?.data?.data;

    const listByPurchaseIdMutation = useMutation(RawMaterialService.getListByPurchaseId);

    console.log("listByPurchaseIdMutation", listByPurchaseIdMutation)
    useEffect(() => {
        ChainFormA.setFieldsValue({
            "transactionId": rawMaterialDetailDate?.transactionId || '-',
            "transactionTime": dayjs(rawMaterialDetailDate?.transactionTime).format("YYYY-MM-DD HH:mm:ss") || '-',
        })
    }, [rawMaterialDetailDate]);

    const columns: ColumnsType<any> = [
        {title: '产品名称', dataIndex: 'productName', key: 'productName'},
        {title: '批次号', dataIndex: 'productionBatch', key: 'productionBatch'}
    ];
    const chainConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Link',
            onClick() {
                setChainDetailModalVisible(true);
            }
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];

    const ChainDetailModalConfig = {
        transactionId: rawMaterialDetailDate?.transactionId,
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false),
    }

    return (
        <>
            <BaseCard className={styles.coreFIrmContainer} title={<PageTitle title='原料批次详情' />}>
                <PageTitle title='原料信息' type='primaryIcon' bmagin={16} />
                <BaseDescriptions>
                    <Descriptions.Item label='原料名称'>{rawMaterialDetailDate?.materialName}</Descriptions.Item>
                    <Descriptions.Item label='原料采购批次'>{rawMaterialDetailDate?.purchaseBatch}</Descriptions.Item>
                    <Descriptions.Item label='生产日期'>
                        {dayjs(rawMaterialDetailDate?.productionDate).format('YYYY-MM-DD HH:mm:ss') as any}
                    </Descriptions.Item>
                    <Descriptions.Item label='保质期'>{rawMaterialDetailDate?.expiration}</Descriptions.Item>
                    <Descriptions.Item label='数量'>{rawMaterialDetailDate?.count}</Descriptions.Item>
                    <Descriptions.Item label='规格'>{rawMaterialDetailDate?.specification}</Descriptions.Item>
                    <Descriptions.Item label='合格证明'>
                        {rawMaterialDetailDate?.certificate && (
                            <Image width={64} src={rawMaterialDetailDate?.certificate}></Image>
                        )}
                    </Descriptions.Item>
                    <Descriptions.Item label='原料图片'>
                        {rawMaterialDetailDate?.materialImg && (
                            <Image width={64} src={rawMaterialDetailDate?.materialImg}></Image>
                        )}
                    </Descriptions.Item>
                    <Descriptions.Item label='附件'>
                        {rawMaterialDetailDate?.purchaseAccessory && (
                            <a
                                href={rawMaterialDetailDate?.purchaseAccessory}
                                onClick={() => {
                                    message.success('正在下载');
                                }}
                            >
                                下载
                            </a>
                        )}
                    </Descriptions.Item>
                    <Descriptions.Item label='生产批次'>
                        <a
                            onClick={() => {
                                setVisible(true);
                                listByPurchaseIdMutation.mutate(Number(id));
                            }}
                        >
                            点击查看
                        </a>
                    </Descriptions.Item>
                </BaseDescriptions>
                <Form form={ChainFormA}>
                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={chainConfig} labelCol={false} />
                </Form>
                <BaseModal footer={false} title='生产批次' visible={visible} onCancelHandle={() => setVisible(false)}>
                    <BaseTable
                        style={{ marginTop: -40 }}
                        loading={listByPurchaseIdMutation.isLoading}
                        columns={columns}
                        dataSource={listByPurchaseIdMutation?.data?.data}
                    />
                </BaseModal>
            </BaseCard>
            <ChainDetailModal {...ChainDetailModalConfig} />
        </>
    );
};

export default FleeWarning;

import { useEffect, useState } from 'react';
import { Col, Form, message, Row, Space, Descriptions, Table } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';

import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';
import BaseInput from '@components/base-input/base-input';
import BaseSelect from '@components/base-select/base-select';
import BaseDatePicker from '@components/base-date-picker';

import styles from './index.module.less';
import { useForm } from 'antd/lib/form/Form';
import FilterForm from '@components/filter-form';
import SourceDataService from '@services/traceability_data/source_data';
import ChainDetailModal from '@components/chain_detail_modal';
import BaseDescriptions from '@components/base-descriptions';
import { decryptedUrl } from '@utils';

const IconStyle: React.CSSProperties = {
    display: 'inline-block',
    width: 16,
    height: 16,
    fontSize: 12,
    background: '#cecece',
    color: '#fff',
    borderRadius: 100,
    textAlign: 'center',
    margin: '0 6px 0 6px'
};

const FleeWarning = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    const [productionAccessory, setProductionAccessory] = useState<any>();

    const productDetail = useQuery(['productDetail'], () => SourceDataService.batchInfo(Number(id)), {
        onSuccess: async (res) => {
            const productionAccessory = await decryptedUrl(res?.data?.productionAccessory);
            setProductionAccessory(productionAccessory);
        }
    });
    const productDetailData = productDetail?.data?.data;

    const columns: ColumnsType<any> = [
        {
            title: '过程编号',
            dataIndex: 'id',
            key: 'id',
            ellipsis: true
        },

        {
            title: '过程名称',
            dataIndex: 'processName',
            key: 'processName',
            ellipsis: true
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true,
            render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
        }
    ];
    const [ChainForm] = useForm();
    const chainConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Link',
            onClick() {
                setChainDetailModalVisible(true);
            }
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];

    useEffect(() => {
        ChainForm.setFieldsValue({
            transactionId: productDetailData?.transactionId || '-',
            transactionTime: dayjs(productDetailData?.transactionTime).format('YYYY-MM-DD HH:mm:ss') || '-'
        });
    }, [productDetailData]);

    const ChainDetailModalConfig = {
        transactionId: productDetailData?.transactionId,
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };

    return (
        <>
            <BaseCard
                className={styles.coreFIrmContainer}
                title={<PageTitle title='种植溯源数据详情' bg='container zhong' />}
            >
                {/* {productDetailData?.materialList && productDetailData.materialList.length > 0 && (
                    <PageTitle title='原料信息' type='primaryIcon' bmagin={16} />
                )}
                {productDetailData?.materialList && productDetailData.materialList.length > 0 && (
                    <BaseDescriptions style={{ marginBottom: 36 }}>
                        {productDetailData?.materialList?.map((item: any, index: number) => (
                            <Descriptions.Item label={`原料${index + 1}`}>{item?.materialName}</Descriptions.Item>
                        ))}
                    </BaseDescriptions>
                )} */}
                <PageTitle title='生产加工信息' type='primaryIcon' bmagin={16} />
                <BaseDescriptions style={{ marginBottom: 36 }}>
                    <Descriptions.Item label='产品名称'>{productDetailData?.productName || '-'}</Descriptions.Item>
                    <Descriptions.Item label='生产批次'>{productDetailData?.productionBatch || '-'}</Descriptions.Item>
                    <Descriptions.Item label='数量'>{productDetailData?.amount || '-'}</Descriptions.Item>
                    <Descriptions.Item label='生产线'>{productDetailData?.line || '-'}</Descriptions.Item>
                    {/* <Descriptions.Item label='种植户'>{productDetailData?.grower || '-'}</Descriptions.Item>
                    <Descriptions.Item label='附件'>
                        {productDetailData?.productionAccessory && productionAccessory ? (
                            <a href={productionAccessory}>下载</a>
                        ) : (
                            '-'
                        )}
                    </Descriptions.Item> */}
                </BaseDescriptions>

                <PageTitle title='地块信息' type='primaryIcon' bmagin={16} />
                <BaseDescriptions style={{ marginBottom: 36 }}>
                    <Descriptions.Item label='地块名称'>{productDetailData?.landName || '-'}</Descriptions.Item>
                    <Descriptions.Item label='地块面积'>
                        {productDetailData?.landArea ? productDetailData?.landArea + '亩' : '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label='所属区域'>{productDetailData?.regionFullName || '-'}</Descriptions.Item>
                    <Descriptions.Item label='地块详细地址'>{productDetailData?.landPlace || '-'}</Descriptions.Item>
                    {/* <Descriptions.Item label='种植户'>{productDetailData?.grower || '-'}</Descriptions.Item>
                    <Descriptions.Item label='附件'>
                        {productDetailData?.productionAccessory && productionAccessory ? (
                            <a href={productionAccessory}>下载</a>
                        ) : (
                            '-'
                        )}
                    </Descriptions.Item> */}
                </BaseDescriptions>
                {/* 收购信息 */}
                <PageTitle title='收购信息' type='primaryIcon' bmagin={16} />
                <BaseDescriptions style={{ marginBottom: 36 }}>
                    <Descriptions.Item label='农作物类型'>{productDetailData?.plantName || '-'}</Descriptions.Item>
                    <Descriptions.Item label='收购对接人'>{productDetailData?.optName || '-'}</Descriptions.Item>
                    <Descriptions.Item label='收购批次'>{productDetailData?.purchaseBatch || '-'}</Descriptions.Item>
                    <Descriptions.Item label='收购日期'>
                        {' '}
                        {productDetailData?.purchaseTime
                            ? dayjs(productDetailData?.purchaseTime).format('YYYY-MM-DD HH:mm:ss')
                            : '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label='收购重量'>
                        {productDetailData?.purchaseWeight ? productDetailData?.purchaseWeight + '吨' : '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label='收购单价'>
                        {productDetailData?.purchaseUnitPrice ? productDetailData?.purchaseUnitPrice + '元/吨' : '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label='装袋数量'>
                        {productDetailData?.bagCount ? productDetailData?.bagCount + '袋' : '-'}
                    </Descriptions.Item>
                    {/* <Descriptions.Item label='种植户'>{productDetailData?.grower || '-'}</Descriptions.Item>
                    <Descriptions.Item label='附件'>
                        {productDetailData?.productionAccessory && productionAccessory ? (
                            <a href={productionAccessory}>下载</a>
                        ) : (
                            '-'
                        )}
                    </Descriptions.Item> */}
                </BaseDescriptions>

                <PageTitle title='农事作业详情信息' type='primaryIcon' bmagin={16} />
                <div>
                    <p style={{ margin: '20px 0' }}>播种作业详情:</p>
                    <div className={styles.td}>
                        <p className={styles.title}>作业名称</p>
                        <p>播种</p>
                    </div>
                    <div className={styles.td}>
                        <p className={styles.title}>农作物类型</p>
                        <p>{productDetailData?.plantName || '-'}</p>
                    </div>
                    <div className={styles.td}>
                        <p className={styles.title}>播种时间</p>
                        <p>
                            {productDetailData?.sowTime
                                ? dayjs(productDetailData?.sowTime).format('YYYY-MM-DD HH:mm:ss')
                                : '-'}
                        </p>
                    </div>
                    {/* <div>
                        <p>种子类型:{productDetailData?.plantName}</p>
                    </div>
                    <div>
                        <p>播种时间:{dayjs(productDetailData?.sowTime).format('YYYY-MM-DD HH:mm:ss')}</p>
                    </div> */}
                </div>
                <div style={{ marginTop: '20px' }}>
                    <p style={{ margin: '20px 0' }}>收割作业详情:</p>
                    <div className={styles.td}>
                        <p className={styles.title}>作业名称</p>
                        <p>收割</p>
                    </div>
                    <div className={styles.td}>
                        <p className={styles.title}>产量(吨)</p>
                        <p>{productDetailData?.harvestNum || '-'}</p>
                    </div>
                    <div className={styles.td}>
                        <p className={styles.title}>收割时间</p>
                        <p>
                            {productDetailData?.harvestTime
                                ? dayjs(productDetailData?.harvestTime).format('YYYY-MM-DD HH:mm:ss')
                                : '-'}
                        </p>
                    </div>
                </div>
                {/* <PageTitle title='生产过程' type='primaryIcon' bmagin={16} />
                <Descriptions style={{ marginBottom: 8 }}></Descriptions>
                <Table
                    columns={columns}
                    dataSource={productDetailData?.processList}
                    style={{ width: '50%', marginBottom: 36 }}
                    pagination={false}
                /> */}
                <Form form={ChainForm} style={{ marginTop: 36 }}>
                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={chainConfig} labelCol={false} />
                </Form>
            </BaseCard>
            <ChainDetailModal {...ChainDetailModalConfig} />
        </>
    );
};

export default FleeWarning;

import React, { useState, useEffect } from 'react';
import { Form, message, Badge, Space, Row, Col } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { SearchOutlined, PlusOutlined, SyncOutlined } from '@ant-design/icons';

import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';

import BaseModal from '@components/base-modal';
import FilterForm from '@components/filter-form';
import BaseInput from '@components/base-input/base-input';
import BaseSelect from '@components/base-select/base-select';

import styles from './index.module.less';

interface WhitelistWord {
    id: string;
    word: string;
    category: string;
    reason: string;
    status: 'active' | 'inactive';
    createTime: string;
    updateTime: string;
}

const WhitelistManagement: React.FC = () => {
    const [form] = Form.useForm();
    const [searchForm] = Form.useForm();

    const [loading, setLoading] = useState(false);
    const [dataSource, setDataSource] = useState<WhitelistWord[]>([]);
    const [total, setTotal] = useState(0);
    const [current, setCurrent] = useState(1);
    const [pageSize, setPageSize] = useState(10);

    const [modalVisible, setModalVisible] = useState(false);
    const [editingRecord, setEditingRecord] = useState<WhitelistWord | null>(null);
    const [modalType, setModalType] = useState<'add' | 'edit'>('add');

    // 模拟数据
    const mockData: WhitelistWord[] = [
        {
            id: '1',
            word: '白名单1',
            category: '专业术语',
            reason: '医学专业术语',
            status: 'active',
            createTime: '2025-05-01 12:00:00',
            updateTime: '2025-05-01 12:00:00'
        },
        {
            id: '2',
            word: '白名单2',
            category: '品牌名称',
            reason: '知名品牌名称',
            status: 'active',
            createTime: '2025-04-01 12:00:00',
            updateTime: '2025-04-01 12:00:00'
        },
        {
            id: '3',
            word: '白名单3',
            category: '地名',
            reason: '正常地理名称',
            status: 'inactive',
            createTime: '2025-04-01 12:00:00',
            updateTime: '2025-04-01 12:00:00'
        },
        {
            id: '4',
            word: '白名单4',
            category: '专业术语',
            reason: '技术专业术语',
            status: 'active',
            createTime: '2025-02-01 12:00:00',
            updateTime: '2025-02-01 12:00:00'
        },
        {
            id: '5',
            word: '白名单5',
            category: '人名',
            reason: '历史人物名称',
            status: 'active',
            createTime: '2025-01-20 12:00:00',
            updateTime: '2025-01-20 12:00:00'
        },
        {
            id: '6',
            word: '白名单6',
            category: '品牌名称',
            reason: '合作品牌名称',
            status: 'inactive',
            createTime: '2025-01-10 12:00:00',
            updateTime: '2025-01-10 12:00:00'
        }
    ];

    useEffect(() => {
        fetchData();
    }, [current, pageSize]);

    const fetchData = async () => {
        setLoading(true);
        try {
            // 模拟API调用
            setTimeout(() => {
                setDataSource(mockData);
                setTotal(mockData.length);
                setLoading(false);
            }, 500);
        } catch (error) {
            message.error('获取数据失败');
            setLoading(false);
        }
    };

    const handleSearch = (values: any) => {
        console.log('搜索条件:', values);
        fetchData();
    };

    const handleReset = () => {
        searchForm.resetFields();
        fetchData();
    };

    const handleAdd = () => {
        setModalType('add');
        setEditingRecord(null);
        form.resetFields();
        setModalVisible(true);
    };

    const handleEdit = (record: WhitelistWord) => {
        setModalType('edit');
        setEditingRecord(record);
        form.setFieldsValue(record);
        setModalVisible(true);
    };

    const handleDelete = (record: WhitelistWord) => {
        message.success('删除成功');
        fetchData();
    };

    const handleToggleStatus = (record: WhitelistWord) => {
        const newStatus = record.status === 'active' ? 'inactive' : 'active';
        const statusText = newStatus === 'active' ? '启用' : '禁用';
        message.success(`${statusText}成功`);
        fetchData();
    };

    const handleModalOk = async () => {
        try {
            const values = await form.validateFields();
            console.log('表单数据:', values);

            if (modalType === 'add') {
                message.success('添加成功');
            } else {
                message.success('编辑成功');
            }

            setModalVisible(false);
            fetchData();
        } catch (error) {
            console.error('表单验证失败:', error);
        }
    };

    const handleModalCancel = () => {
        setModalVisible(false);
        form.resetFields();
        setEditingRecord(null);
    };

    // 表格列配置
    const columns: ColumnsType<WhitelistWord> = [
        {
            title: '白名单词汇',
            dataIndex: 'word',
            key: 'word',
            width: 150
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            width: 180
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: 100,
            render: (status: string) => (
                <Badge
                    status={status === 'active' ? 'success' : 'error'}
                    text={status === 'active' ? '启用' : '禁用'}
                />
            )
        },
        {
            title: '操作',
            key: 'action',
            width: 200,
            render: (_, record) => (
                <Space size='middle'>
                    <BaseButton
                        type='dashed'
                        className={record.status === 'active' ? 'warnBtn' : 'primaryBtn'}
                        onClick={() => handleToggleStatus(record)}
                    >
                        {record.status === 'active' ? '禁用' : '启用'}
                    </BaseButton>
                    <BaseButton type='dashed' className='editBtn' onClick={() => handleEdit(record)}>
                        编辑
                    </BaseButton>
                    <BaseButton type='dashed' className='warnBtn' onClick={() => handleDelete(record)}>
                        删除
                    </BaseButton>
                </Space>
            )
        }
    ];

    // 表单配置
    const formConfig = [
        {
            type: 'Input',
            label: '白名单词汇',
            value: 'word',
            placeholder: '请输入白名单词汇',
            rules: [{ required: true, message: '请输入白名单词汇' }],
            span: 24
        }
    ];

    return (
        <>
            <BaseCard title={<PageTitle title='白名单管理' />} className={styles.coreFIrmContainer}>
                <div className={styles.searchContainer}>
                    <Form onFinish={handleSearch} form={searchForm} labelCol={{ span: 6 }} className='label-title'>
                        <Row gutter={[24, 0]} style={{ width: '100%' }}>
                            <Col span={6}>
                                <Form.Item label='状态' name='status'>
                                    <BaseSelect
                                        placeholder='请选择状态'
                                        options={[
                                            { label: '启用', value: 'active' },
                                            { label: '禁用', value: 'inactive' }
                                        ]}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <Form.Item label='白名单词' name='word'>
                                    <BaseInput placeholder='请输入白名单词' />
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <Form.Item>
                                    <BaseButton
                                        htmlType='submit'
                                        type='primary'
                                        className={`${styles.searchBtn} ${styles.baseBtn}`}
                                        icon={<SearchOutlined rev={undefined} />}
                                    >
                                        查询
                                    </BaseButton>
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <div style={{ display: 'flex', justifyContent: 'end' }}>
                                    <Space>
                                        <BaseButton
                                            type='dashed'
                                            className='primaryBtn'
                                            icon={<SyncOutlined rev={undefined} />}
                                            onClick={handleReset}
                                        >
                                            重置
                                        </BaseButton>
                                        <BaseButton
                                            type='dashed'
                                            icon={<PlusOutlined rev={undefined} />}
                                            className='bgBtn'
                                            onClick={handleAdd}
                                        >
                                            新建白名单
                                        </BaseButton>
                                    </Space>
                                </div>
                            </Col>
                        </Row>
                    </Form>
                </div>
                <BaseTable rowKey='id' loading={loading} dataSource={dataSource} columns={columns} />

                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={current}
                    pageSize={pageSize}
                    total={total}
                    onShowSizeChange={(page, size) => {
                        setCurrent(page);
                        setPageSize(size || 10);
                    }}
                    onChange={(page, size) => {
                        setCurrent(page);
                        setPageSize(size || 10);
                    }}
                />
            </BaseCard>

            <BaseModal
                title={modalType === 'add' ? '新建白名单' : '编辑白名单'}
                visible={modalVisible}
                onOk={handleModalOk}
                onCancel={handleModalCancel}
                width={600}
            >
                <Form form={form} layout='vertical'>
                    <FilterForm itemConfig={formConfig} labelCol={false} wrapperCol={24} />
                </Form>
            </BaseModal>
        </>
    );
};

export default WhitelistManagement;

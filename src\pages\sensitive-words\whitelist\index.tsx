import React, { useState, useEffect } from 'react';
import { Form, message, Badge, Space, Button } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useNavigate } from 'react-router-dom';
import { SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';

import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';
import BaseInput from '@components/base-input/base-input';
import BaseSelect from '@components/base-select/base-select';
import BaseModal from '@components/base-modal';
import FilterForm from '@components/filter-form';
import TableHead from '@components/table-head';
import SingleSearch from '@components/single-search';

import styles from './index.module.less';

interface WhitelistWord {
    id: string;
    word: string;
    category: string;
    reason: string;
    status: 'active' | 'inactive';
    createTime: string;
    updateTime: string;
}

const WhitelistManagement: React.FC = () => {
    const navigate = useNavigate();
    const [form] = Form.useForm();
    const [searchForm] = Form.useForm();

    const [loading, setLoading] = useState(false);
    const [dataSource, setDataSource] = useState<WhitelistWord[]>([]);
    const [total, setTotal] = useState(0);
    const [current, setCurrent] = useState(1);
    const [pageSize, setPageSize] = useState(10);

    const [modalVisible, setModalVisible] = useState(false);
    const [editingRecord, setEditingRecord] = useState<WhitelistWord | null>(null);
    const [modalType, setModalType] = useState<'add' | 'edit'>('add');

    // 模拟数据
    const mockData: WhitelistWord[] = [
        {
            id: '1',
            word: '白名单1',
            category: '专业术语',
            reason: '医学专业术语',
            status: 'active',
            createTime: '2025-05-01 12:00:00',
            updateTime: '2025-05-01 12:00:00'
        },
        {
            id: '2',
            word: '白名单2',
            category: '品牌名称',
            reason: '知名品牌名称',
            status: 'active',
            createTime: '2025-04-01 12:00:00',
            updateTime: '2025-04-01 12:00:00'
        },
        {
            id: '3',
            word: '白名单3',
            category: '地名',
            reason: '正常地理名称',
            status: 'inactive',
            createTime: '2025-04-01 12:00:00',
            updateTime: '2025-04-01 12:00:00'
        },
        {
            id: '4',
            word: '白名单4',
            category: '专业术语',
            reason: '技术专业术语',
            status: 'active',
            createTime: '2025-02-01 12:00:00',
            updateTime: '2025-02-01 12:00:00'
        },
        {
            id: '5',
            word: '白名单5',
            category: '人名',
            reason: '历史人物名称',
            status: 'active',
            createTime: '2025-01-20 12:00:00',
            updateTime: '2025-01-20 12:00:00'
        },
        {
            id: '6',
            word: '白名单6',
            category: '品牌名称',
            reason: '合作品牌名称',
            status: 'inactive',
            createTime: '2025-01-10 12:00:00',
            updateTime: '2025-01-10 12:00:00'
        }
    ];

    useEffect(() => {
        fetchData();
    }, [current, pageSize]);

    const fetchData = async () => {
        setLoading(true);
        try {
            // 模拟API调用
            setTimeout(() => {
                setDataSource(mockData);
                setTotal(mockData.length);
                setLoading(false);
            }, 500);
        } catch (error) {
            message.error('获取数据失败');
            setLoading(false);
        }
    };

    const handleSearch = (values: any) => {
        console.log('搜索条件:', values);
        fetchData();
    };

    const handleReset = () => {
        searchForm.resetFields();
        fetchData();
    };

    const handleAdd = () => {
        setModalType('add');
        setEditingRecord(null);
        form.resetFields();
        setModalVisible(true);
    };

    const handleEdit = (record: WhitelistWord) => {
        setModalType('edit');
        setEditingRecord(record);
        form.setFieldsValue(record);
        setModalVisible(true);
    };

    const handleDelete = (record: WhitelistWord) => {
        message.success('删除成功');
        fetchData();
    };

    const handleModalOk = async () => {
        try {
            const values = await form.validateFields();
            console.log('表单数据:', values);

            if (modalType === 'add') {
                message.success('添加成功');
            } else {
                message.success('编辑成功');
            }

            setModalVisible(false);
            fetchData();
        } catch (error) {
            console.error('表单验证失败:', error);
        }
    };

    const handleModalCancel = () => {
        setModalVisible(false);
        form.resetFields();
        setEditingRecord(null);
    };

    // 搜索配置
    const searchConfig = {
        placeholder: '请输入白名单词汇',
        handleSearch: () => {
            console.log('搜索');
            fetchData();
        },
        setSearchValue: (value: string) => {
            console.log('搜索值:', value);
        }
    };

    // 筛选表单配置
    const filterConfig = [
        {
            type: 'Select',
            label: '状态',
            value: 'status',
            placeholder: '请选择状态',
            fields: [
                { label: '启用', value: 'active' },
                { label: '禁用', value: 'inactive' }
            ],
            span: 8
        }
    ];

    // 表格列配置
    const columns: ColumnsType<WhitelistWord> = [
        {
            title: '白名单词汇',
            dataIndex: 'word',
            key: 'word',
            width: 150
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            width: 180
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: 100,
            render: (status: string) => (
                <Badge
                    status={status === 'active' ? 'success' : 'error'}
                    text={status === 'active' ? '启用' : '禁用'}
                />
            )
        },
        {
            title: '操作',
            key: 'action',
            width: 200,
            render: (_, record) => (
                <Space size='middle'>
                    <Button type='link' size='small' icon={<EditOutlined />} onClick={() => handleEdit(record)}>
                        编辑
                    </Button>
                    <Button
                        type='link'
                        size='small'
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => handleDelete(record)}
                    >
                        删除
                    </Button>
                </Space>
            )
        }
    ];

    // 表单配置
    const formConfig = [
        {
            type: 'Input',
            label: '白名单词汇',
            value: 'word',
            placeholder: '请输入白名单词汇',
            rules: [{ required: true, message: '请输入白名单词汇' }],
            span: 24
        },
        {
            type: 'Select',
            label: '分类',
            value: 'category',
            placeholder: '请选择分类',
            fields: [
                { label: '专业术语', value: '专业术语' },
                { label: '品牌名称', value: '品牌名称' },
                { label: '地名', value: '地名' },
                { label: '人名', value: '人名' }
            ],
            rules: [{ required: true, message: '请选择分类' }],
            span: 24
        },
        {
            type: 'Input',
            label: '添加原因',
            value: 'reason',
            placeholder: '请输入添加原因',
            rules: [{ required: true, message: '请输入添加原因' }],
            span: 24
        },
        {
            type: 'Select',
            label: '状态',
            value: 'status',
            placeholder: '请选择状态',
            fields: [
                { label: '启用', value: 'active' },
                { label: '禁用', value: 'inactive' }
            ],
            rules: [{ required: true, message: '请选择状态' }],
            span: 24
        }
    ];

    return (
        <div className={styles.container}>
            <BaseCard className={styles.cardContainer} title={<PageTitle title='白名单管理' />}>
                <BaseTable
                    rowKey='id'
                    loading={loading}
                    dataSource={dataSource}
                    columns={columns}
                    btnDisplay={() => (
                        <TableHead
                            LeftDom={
                                <div className={styles.searchContainer}>
                                    <Form
                                        layout='inline'
                                        labelAlign='left'
                                        onFinish={handleSearch}
                                        form={searchForm}
                                        className='label-title'
                                    >
                                        <FilterForm itemConfig={filterConfig} size={100} labelCol={6} />
                                        <BaseButton
                                            type='primary'
                                            style={{ width: 100 }}
                                            className={`${styles.searchBtn} ${styles.baseBtn}`}
                                            icon={<SearchOutlined />}
                                            htmlType='submit'
                                        >
                                            查询
                                        </BaseButton>
                                        <BaseButton
                                            style={{ width: 100, marginLeft: 8 }}
                                            className={styles.baseBtn}
                                            onClick={handleReset}
                                        >
                                            重置
                                        </BaseButton>
                                    </Form>
                                </div>
                            }
                            RightDom={
                                <div style={{ display: 'flex' }}>
                                    <SingleSearch {...searchConfig} />
                                    <BaseButton
                                        type='dashed'
                                        icon={<PlusOutlined />}
                                        className='greenBtn'
                                        onClick={handleAdd}
                                        style={{ marginLeft: 8 }}
                                    >
                                        新建白名单
                                    </BaseButton>
                                </div>
                            }
                        />
                    )}
                />

                <BasePagination
                    current={current}
                    pageSize={pageSize}
                    total={total}
                    onChange={(page, size) => {
                        setCurrent(page);
                        setPageSize(size || 10);
                    }}
                />
            </BaseCard>

            <BaseModal
                title={modalType === 'add' ? '新建白名单' : '编辑白名单'}
                visible={modalVisible}
                onOk={handleModalOk}
                onCancel={handleModalCancel}
                width={600}
            >
                <Form form={form} layout='vertical' initialValues={{ status: 'active' }}>
                    <FilterForm itemConfig={formConfig} labelCol={false} wrapperCol={24} />
                </Form>
            </BaseModal>
        </div>
    );
};

export default WhitelistManagement;

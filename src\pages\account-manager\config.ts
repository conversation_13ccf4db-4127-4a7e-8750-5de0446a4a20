export const addAccountConfig = [
    {
        label: '用户名',
        type: 'Input',
        value: 'name',
        span:18,
        placeholder: '请输入用户名',
        rules: [{ required: true, message: '' },
            () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,30}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入用户名!');
                        }else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            if (value.length > 30) {
                                callback('请保持字符在30字符以内!');
                            } else {
                                callback('请输入用户姓名，支持中文、字母或数字!');
                            }
                        } else {
                            callback();
                        }
                    }
                })
        ]
    },
    {
        label: '联系方式',
        type: 'Input',
        value: 'phone',
        span:18,
        placeholder: '请输入联系方式',
        rules: [{ required: true, message: '' },
        () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^(?:(?:\+|00)86)?1[3-9]\d{9}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入联系方式!');
                        }else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            callback('请输入有效的联系方式');
                        } else {
                            callback();
                        }
                    }
        })
        ]
    },
    // {
    //     value: 'userId',
    // }
];

export const editAccountConfig = [
    {
        label: '用户名',
        type: 'Input',
        value: 'name',
        placeholder: '请输入用户名',
        rules: [{ required: true, message: '' },
        () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,30}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入用户名!');
                        }else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            if (value.length > 30) {
                                callback('请保持字符在30字符以内!');
                            } else {
                                callback('请输入用户名称，支持中文、字母或数字!');
                            }
                        } else {
                            callback();
                        }
                    }
        })
        ]
    },
    {
        label: '联系方式',
        type: 'Input',
        value: 'phone',
        placeholder: '请输入联系方式',
        rules: [{ required: true, message: '' },
        () => ({
                    validator: (_: any, value: any, callback: any) => {
                        // const regExp = new RegExp(/^(13[0-9]|14[5-9]|15[0-35-9]|16[25-7]|17[0-8]|18[0-9]|19[0135689])[0-9]{8}$/);
                        const regExp = new RegExp(/^(?:(?:\+|00)86)?1[3-9]\d{9}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入联系方式!');
                        } else if (verify === false) {
                            callback('请输入有效的联系方式');
                        } else {
                            callback();
                        }
                    }
        })
        ]
    },
    // {
    //     value: 'userId'
    // }
];
export const searchConfig = [
    {
        label: '搜索',
        type: 'Input',
        value: 'name',
        placeholder: '输入用户名/联系方式',
        span: 12,
        className:'find'
    },
    {
        label: '状态',
        type: 'Select',
        value: 'data',
        placeholder: '请选择',
        span: 12,
        className:'find',
        fields: [
            {
                value: '1',
                label: '禁用'
            },
            {
                value: '0',
                label: '可用'
            }
        ]
    }
];

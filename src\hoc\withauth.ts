/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-13 16:26:36
 * @LastEditTime: 2022-10-14 09:24:47
 * @LastEditors: PhilRandWu
 */
import { Component, ReactElement } from 'react';
import PropTypes from 'prop-types';
import { method, PermissionEnum } from '@utils';
/**
 * @description: 通过传入权限，判断组件是否可以正常显示，
 * @param {number} permission  所拥有权限的掩码
 * @param {*} judgepermission  需要判断是否拥有权限的掩码
 * @param {ReactElement} ComposedComponent  显示的组件
 * @return {*}
 */
export let wrapAuth = (permission: number, judgepermission: 'DOWNLOAD' | 'UPLOAD', ComposedComponent: ReactElement) =>
    class WrapComponent extends Component {
        // 构造
        constructor(props: { auth: number }) {
            super(props);
        }

        static propTypes = {
            auth: PropTypes.string.isRequired
        };

        render() {
            // 此处根据权限判断   tool.getAuth(this.props.auth）
            if (method.hasPermission(permission, PermissionEnum[judgepermission])) {
                return ComposedComponent;
            } else {
                return null;
            }
        }
    };

import { useEffect, useState } from 'react';
import { Col, Form, message, Row, Space, Descriptions, Table } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

import BaseCard from '@components/base-card';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';

import styles from './index.module.less';
import FilterForm from '@components/filter-form';
import { useForm } from 'antd/lib/form/Form';
import SourceLogisticsService, { SourceLogisticsTypeEnum } from '@services/traceability_data/source_logistics';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';
import ChainDetailModal from '@components/chain_detail_modal';
import BaseDescriptions from '@components/base-descriptions';

const IconStyle: React.CSSProperties = {
    display: 'inline-block',
    width: 16,
    height: 16,
    fontSize: 12,
    background: '#cecece',
    color: '#fff',
    borderRadius: 100,
    textAlign: 'center',
    margin: '0 6px 0 6px'
};

export const getTransportationType = (type: number) => {
    if (type === 1) return '陆路运输';
    if (type === 2) return '水路运输';
    if (type === 3) return '航空运输';
    if (type === 4) return '冷链运输';
};

const FleeWarning = () => {
    const { id } = useParams();
    const location = useLocation();
    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);

    const [clickTransactionId, setClickTransactionId] = useState<any>();

    const queryLogisticsDetail = useQuery(['queryLogisticsDetail', id], () =>
        SourceLogisticsService.detail(Number(id))
    );
    const queryLogisticsDetailData = queryLogisticsDetail?.data?.data;

    const columns: ColumnsType<any> = [
        {
            title: '箱码',
            dataIndex: 'boxCode',
            key: 'boxCode',
            ellipsis: true
        },

        {
            title: '产品名称',
            dataIndex: 'productName',
            key: 'productName',
            ellipsis: true
        }
    ];
    const [UnloadForm] = useForm();
    const [LoadForm] = useForm();

    const onchainConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Custom',
            children: (
                <a
                    onClick={() => {
                        setClickTransactionId(queryLogisticsDetailData?.transactionId);
                        setChainDetailModalVisible(true);
                    }}
                >
                    {queryLogisticsDetailData?.transactionId}
                </a>
            )
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];

    const inchainConfig = [
        {
            label: '链上哈希',
            name: 'unloadTransactionId',
            value: 'unloadTransactionId',
            title: '信息的链上的哈希值',
            type: 'Custom',
            children: (
                <a
                    onClick={() => {
                        setClickTransactionId(queryLogisticsDetailData?.unloadTransactionId);
                        setChainDetailModalVisible(true);
                    }}
                >
                    {queryLogisticsDetailData?.unloadTransactionId}
                </a>
            )
        },
        {
            label: '上链时间',
            name: 'unloadTransactionTime',
            value: 'unloadTransactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];

    useEffect(() => {
        LoadForm.setFieldsValue({
            transactionId: queryLogisticsDetailData?.transactionId || '-',
            transactionTime: dayjs(queryLogisticsDetailData?.transactionTime).format('YYYY-MM-DD HH:mm:ss') || '-'
        });
        UnloadForm.setFieldsValue({
            unloadTransactionId: queryLogisticsDetailData?.unloadTransactionId || '-',
            unloadTransactionTime:
                dayjs(queryLogisticsDetailData?.unloadTransactionTime).format('YYYY-MM-DD HH:mm:ss') || '-'
        });
    }, [queryLogisticsDetailData]);

    const ChainDetailModalConfig = {
        transactionId: clickTransactionId,
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };
    console.log(
        'state',
        location.state,
        location.state === SourceLogisticsTypeEnum.自行运输,
        SourceLogisticsTypeEnum.委托运输
    );
    return (
        <>
            <BaseCard className={styles.coreFIrmContainer} title={<PageTitle title='运输单详情' />}>
                {location.state?.type === SourceLogisticsTypeEnum.自行运输 ? (
                    <>
                        <PageTitle title='装货信息' type='primaryIcon' bmagin={16} />
                        <BaseDescriptions>
                            <Descriptions.Item label='运输单号'>
                                {queryLogisticsDetailData?.transNumber}
                            </Descriptions.Item>
                            <Descriptions.Item label='装货地点'>
                                {queryLogisticsDetailData?.loadingLocation}
                            </Descriptions.Item>
                            <Descriptions.Item label='运输方式'>
                                {getTransportationType(Number(queryLogisticsDetailData?.transportationType))}
                            </Descriptions.Item>
                        </BaseDescriptions>
                    </>
                ) : (
                    <>
                        <PageTitle title='运输单信息' type='primaryIcon' bmagin={16} />
                        <BaseDescriptions>
                            <Descriptions.Item label='运输单号'>
                                {queryLogisticsDetailData?.transNumber}
                            </Descriptions.Item>
                            <Descriptions.Item label='物流企业'>
                                {queryLogisticsDetailData?.loEnterprises}
                            </Descriptions.Item>
                            <Descriptions.Item label='物流单号'>{queryLogisticsDetailData?.loNumber}</Descriptions.Item>
                        </BaseDescriptions>
                    </>
                )}
                <PageTitle title='箱码信息' type='primaryIcon' bmagin={16} />
                <Table
                    columns={columns}
                    dataSource={queryLogisticsDetailData?.boxTo}
                    style={{ width: '50%', marginBottom: 24 }}
                    pagination={false}
                />

                <div>
                    <Form form={LoadForm}>
                        <PageTitle title='装货区块链信息' type='primaryIcon' bmagin={16} />
                        <FilterForm showMode itemConfig={onchainConfig} labelCol={false} />
                    </Form>
                </div>

                {location?.state?.state !== 0 && location?.state?.type !== 2 && (
                    <Descriptions title='卸货信息' style={{ marginTop: 36 }}>
                        <Descriptions.Item label='卸货地点'>
                            {queryLogisticsDetailData?.unloadingLocation}
                        </Descriptions.Item>
                    </Descriptions>
                )}
                {location?.state?.state !== 0 && location?.state?.type !== 2 && (
                    <Form form={UnloadForm}>
                        <PageTitle title='卸货区块链信息' type='primaryIcon' bmagin={16} />
                        <FilterForm showMode itemConfig={inchainConfig} labelCol={false} />
                    </Form>
                )}
            </BaseCard>

            <ChainDetailModal {...ChainDetailModalConfig} />
        </>
    );
};

export default FleeWarning;

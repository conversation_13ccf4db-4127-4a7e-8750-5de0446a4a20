import React, { useState, useEffect } from 'react';
import { Form, Input, message, Spin } from 'antd';
import copyToClipboard from 'copy-to-clipboard';
import { useSelector, useDispatch } from 'react-redux';
import { useQuery, useMutation } from 'react-query';
import md5 from 'js-md5';
import CryptoJS from 'crypto-js';

// import { encryptionAndDecryption, decrypt } from './help';

//修改密码
import { modifyPassword } from '@services/user';

import BaseCard from '@components/base-card';
import BaseButton from '@components/base-button';
// import { IStore, AppDispatch } from '@/store';

// import { queryInterface } from '@/services';
// import { setPublicKeyAction, loginAction } from '@/store/moudle/user';

import './styles.less';
import BaseInput from '@components/base-input';
import BaseModal from '@components/base-modal';
import PageTitle from '@components/page-title';
import { ReformChainError } from '@utils/errorCodeReform';
// import { IStore } from '@store';
// import { requestValid } from '@services';
// import { resetPassword, updateKeyWord } from '@store/moudle/user';
import { useNavigate } from 'react-router-dom';
import rsaEncrypt from '@utils/rsa';
// import PageTitle from '@/components/page-title';

// const { sign } = require('eosjs-ecc');

const PassWordReset = () => {
    const navigate = useNavigate();
    const [decryptPrivatekey, setDecryptPrivatekey] = useState(false);
    const [resetModal, setResetModal] = useState(false);
    const [createForm] = Form.useForm();
    const [resetFormModal] = Form.useForm();
    const [decryptForm] = Form.useForm();
    const [modifyModal, setModifyModal] = useState(false);
    const modipassword = useMutation(modifyPassword, {
        onSuccess(res) {
            message.success('修改密码成功');
            createForm.resetFields();
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    const user = JSON.parse(localStorage.getItem('userdata') || '');
    // console.log("123123444444",user)
    // const dispatch = useDispatch();

    // const initPassWord = useMutation(requestValid.resetPassword, {
    //     onSuccess: (res: any) => {
    //         navigate("/storage/now")
    //         message.success("修改密码成功");
    //     },
    //     onError: (err: any) => {
    //         message.error('旧密码错误')
    //     }
    // })

    return (
        <div className='password-reset'>
            <BaseCard mt24>
                <PageTitle title='修改密码' />
                <Spin
                    spinning={false}
                // spinning={queryUserBlockchain.isLoading}
                >
                    <Form
                        form={createForm}
                        labelCol={{ span: 2 }}
                        wrapperCol={{ span: 8 }}
                        layout='horizontal'
                        colon={true}
                        labelAlign='right'
                        onFinish={async () => {
                            const { oldPassWord, operationKey } = await createForm.validateFields();
                            modipassword.mutate({
                                newPassword: await rsaEncrypt(operationKey),
                                oldPassword: await rsaEncrypt(oldPassWord)
                            });
                        }}
                    >
                        <Form.Item
                            label='旧密码'
                            name='oldPassWord'
                            rules={[{ required: true, message: '请输入旧密码' }]}
                        >
                            <Input.Password placeholder='请输入旧密码' />
                        </Form.Item>
                        <Form.Item
                            label='新密码'
                            name='operationKey'
                            rules={[
                                { required: true, message: '' },
                                () => ({
                                    validator: (_, value, callback) => {
                                        const regExp = new RegExp(
                                            /^(?=.*[A-Z].*)(?=.*[a-z].*)(?=.*\d)(?=.*[!#$%])[A-Za-z\d!#$%]{8,20}$/
                                        );
                                        const verify = regExp.test(value);
                                        if (!value) {
                                            callback('请输入新密码');
                                        } else if (verify === false) {
                                            callback('新密码不符合密码规则！');
                                        } else {
                                            callback();
                                        }
                                    }
                                })
                            ]}
                        >
                            <Input.Password
                                placeholder='请输入新密码'
                                onChange={(data: any) => {
                                    createForm.validateFields(['operationKeys']);
                                }}
                            />
                        </Form.Item>
                        <Form.Item
                            label='确认密码'
                            name='operationKeys'
                            rules={[
                                { required: true, message: '请确认新密码!' },
                                ({ getFieldValue }) => ({
                                    validator(_, value, callback) {
                                        if (!value || getFieldValue('operationKey') === value) {
                                            callback();
                                        } else {
                                            callback('两次密码输入不一致!');
                                        }
                                    }
                                })
                            ]}
                        >
                            <Input.Password placeholder='请确认新密码' />
                        </Form.Item>
                        <Form.Item className='enterReset'>
                            <BaseButton
                                type='primary'
                                className='mychange'
                                htmlType='submit'
                            // loading={createPrivate.isLoading}
                            >
                                确认
                            </BaseButton>
                        </Form.Item>
                        <div className='loginpswTips'>
                            <div>密码规则：</div>
                            <div>1.长度为8-20位</div>
                            <div>2.必须包含数字0-9</div>
                            <div>3.必须包含一位大写字母和一位小写字母</div>
                            <div>4.必须至少包含特殊字符!#$%中的一个</div>
                        </div>
                    </Form>
                </Spin>
            </BaseCard>
        </div>
    );
};

export default React.memo(PassWordReset);

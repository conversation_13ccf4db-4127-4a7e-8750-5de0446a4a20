import BaseTooltip from '@components/base-tooltip';
import { Descriptions as AntdDescriptions } from 'antd';
import React, { useState } from 'react';

const Descriptions = (props: any) => {
    const { children, ...restProps } = props;
    const newChildren = children?.map((descriptionsItem: any) => {
        if (!descriptionsItem) {
            return descriptionsItem;
        }
        if (typeof descriptionsItem.props.children === 'string') {
            return React.cloneElement(descriptionsItem, {
                ...descriptionsItem.props,
                children: (
                    <BaseTooltip
                        preStyle={{ lineHeight: 'inherit', whiteSpace: 'nowrap' }}
                        data={descriptionsItem.props.children}
                    ></BaseTooltip>
                )
            });
        }
        return descriptionsItem;
    });
    return <AntdDescriptions {...restProps}>{newChildren}</AntdDescriptions>;
};

export default Descriptions;

import BaseCard from '@components/base-card';
import BaseCollapse from '@components/base-collapse';
import BaseFormItem from '@components/base-form-item';
import BaseModal from '@components/base-modal';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import { Form, message, Image } from 'antd';
import React, { useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { chainInfoConfig } from './config';
import { useLocation } from 'react-router-dom';
import { purchaseDetail } from '@services/purchase-controller';
import dayjs from 'dayjs';
import { ReformChainError } from '@utils/errorCodeReform';
import FilterForm from '@components/filter-form/filter-form';
import ChainDetailModal from '@components/chain_detail_modal';
// import './index.less'

function RawmaterialDatail() {
    const { state } = useLocation();
    console.log('state', state);

    const [SourceForm] = Form.useForm();
    const [ChainForm] = Form.useForm();

    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);

    const purchasedetail = useQuery(
        ['purchaseDetail'],
        () => {
            return purchaseDetail({
                id: state.data.purchaseId
            });
        },
        {
            onSuccess(res: any) {
                SourceForm.setFieldsValue({
                    materialName: res?.data?.materialName,
                    batchNumber: res?.data?.purchaseBatch,
                    productionDate: dayjs(res?.data?.productionDate).format('YYYY-MM-DD HH:mm:ss'),
                    expirationDate: res?.data?.expiration,
                    count: res?.data?.count,
                    specification: res?.data?.specification,
                    certificate: res?.data?.certificate,
                    materialImg: res?.data?.materialImg,
                    purchaseAccessory: res?.data?.purchaseAccessory
                });
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );

    const chainInfoConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            type: 'Link',
            onClick: () => {
                setChainDetailModalVisible(true);
            },
            // span: 24,
            title: '信息的链上的哈希值'
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];

    const ChainDetailModalConfig = {
        transactionId: ChainForm.getFieldValue('transactionId'),
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };

    const SourceInfoConfig = [
        {
            label: '原料名称',
            name: 'materialName',
            value: 'materialName',
            type: 'ShowText',
            span: 8
        },
        {
            label: '原料采购批次',
            name: 'batchNumber',
            value: 'batchNumber',
            type: 'ShowText',
            span: 8
        },
        {
            label: '生产日期',
            name: 'productionDate',
            value: 'productionDate',
            type: 'ShowText',
            span: 8
        },
        {
            label: '保质期',
            name: 'expirationDate',
            value: 'expirationDate',
            type: 'ShowText',
            span: 8
        },
        {
            label: '数量',
            name: 'count',
            value: 'count',
            type: 'ShowText',
            span: 8
        },
        {
            label: '规格',
            name: 'specification',
            value: 'specification',
            type: 'ShowText',
            span: 8
        },
        {
            label: '合格证明',
            name: 'certificate',
            value: 'certificate',
            type: 'Custom',
            span: 8,
            children: SourceForm.getFieldValue('certificate') ? (
                <Image width={70} src={SourceForm.getFieldValue('certificate')}></Image>
            ) : (
                '-'
            )
        },
        {
            type: 'Custom',
            label: '原料图片',
            name: 'materialImg',
            value: 'materialImg',
            span: 8,
            children: SourceForm.getFieldValue('materialImg') ? (
                <Image width={70} src={SourceForm.getFieldValue('materialImg')}></Image>
            ) : (
                '-'
            )
        },
        {
            label: '附件',
            name: 'accessory',
            value: '123',
            type: 'Url',
            tips: '下载',
            span: 8,
            show: purchasedetail?.data?.data?.purchaseAccessory ? null : '1',
            display: purchasedetail?.data?.data?.purchaseAccessory ? (
                <a href={purchasedetail?.data?.data?.purchaseAccessory}>{'下载'}</a>
            ) : (
                '-'
            )
        }
    ];

    const valuesForm = SourceForm.getFieldsValue();
    console.log('SourceCodeForm', SourceForm, valuesForm);
    ChainForm.setFieldsValue({
        transactionId: purchasedetail?.data?.data?.transactionId || '-',
        transactionTime: purchasedetail?.data?.data?.transactionTime
            ? dayjs(purchasedetail?.data?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
            : null
    });

    return (
        <>
            <BaseCard title={<PageTitle title='采购详情' />}>
                <Form form={SourceForm}>
                    <PageTitle title='原料信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={SourceInfoConfig} labelCol={false} />
                </Form>
                <Form form={ChainForm}>
                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={chainInfoConfig} labelCol={false} />
                </Form>
            </BaseCard>
            <ChainDetailModal {...ChainDetailModalConfig} />
        </>
    );
}

export default RawmaterialDatail;

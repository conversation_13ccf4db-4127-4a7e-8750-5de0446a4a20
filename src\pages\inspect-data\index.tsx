import { useState } from 'react';
import { Card, Col, Form, message, Row, Space } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';

import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';
import BaseInput from '@components/base-input/base-input';
import BaseSelect from '@components/base-select/base-select';
import BaseDatePicker from '@components/base-date-picker';
import { DownOutlined, UpOutlined, SearchOutlined, SyncOutlined } from '@ant-design/icons';

import styles from './index.module.less';
import { RoleEnum } from '@config';
import { Enum2Object } from '@utils/enum';
import SourceInspectService, { SourceInspectResultsEnum } from '@services/traceability_data/source_inspect';
import { useAppSelector } from '@store';
import { QueryTime } from '@utils';
import PublicSourceService from '@services/traceability_data/public_source';

interface IUrlState {
    pageIndex: number;
    pageSize: number;
    // ...
}

const Inspect = () => {
    const userInfo = useAppSelector((store) => store.user);
    const LocalLoginIdentity = Number(userInfo?.userInfo?.identity);
    const [queryParams, setQueryParams] = useState<any>({ pageSize: 10, pageIndex: 1 });

    const [form] = Form.useForm();
    const navigate = useNavigate();

    const [isSimpleSearch, setIsSimpleSearch] = useState(true);

    const queryOrgList = useQuery(['queryOrgList'], () =>
        PublicSourceService.getOrgList({
            identity: RoleEnum['质检机构']
        })
    );
    const queryOrgListData: any[] = queryOrgList?.data?.data;

    const queryCoreOrgList = useQuery(['queryCoreOrgList'], () => PublicSourceService.getCoreList(), {
        enabled: [RoleEnum.平台方]?.includes(LocalLoginIdentity)
    });
    const queryCoreOrgListData: any[] = queryCoreOrgList?.data?.data;

    const queryInspect = useQuery(['queryInspect', queryParams], () => SourceInspectService.Query(queryParams), {
        onSuccess() {},
        onError() {}
    });
    const queryInspectListData = queryInspect?.data?.data;

    console.log(queryInspect, 'queryInspect queryInspect');

    const columns: ColumnsType<any> = [
        {
            title: '生产批次',
            dataIndex: 'productionBatch',
            key: 'productionBatch',
            ellipsis: true
        },
        {
            title: '产品名',
            dataIndex: 'productName',
            key: 'productName',
            ellipsis: true
        },
        {
            title: '质检机构',
            dataIndex: 'inspectionOrgName',
            key: 'inspectionOrgName',
            ellipsis: true,
            render: (_, row) => (row.inspectionOrgName ? row.inspectionOrgName : '-')
        },
        {
            title: '操作人',
            dataIndex: 'optName',
            key: 'optName',
            ellipsis: true
        },
        ...([RoleEnum.平台方, RoleEnum.运营方]?.includes(LocalLoginIdentity)
            ? [
                  {
                      title: '生产加工企业',
                      dataIndex: 'coreName',
                      key: 'coreName',
                      ellipsis: true
                  }
              ]
            : [
                  {
                      title: '生产加工企业',
                      dataIndex: 'coreName',
                      key: 'coreName',
                      ellipsis: true
                  }
              ]),
        {
            title: '质检结果',
            dataIndex: 'inspectionResults',
            key: 'inspectionResults',
            ellipsis: true,
            render: (inspectionResults) => {
                if (SourceInspectResultsEnum.合格 === inspectionResults) return '合格';
                if (SourceInspectResultsEnum.不合格 === inspectionResults) return '不合格';
            }
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true,
            render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
        },
        {
            title: '哈希',
            dataIndex: 'transactionId',
            key: 'transactionId',
            ellipsis: true
        },

        {
            title: '操作',
            render: (_, row) => (
                <BaseButton type='dashed' className='primaryBtn' onClick={() => navigate(`detail/${row?.id}`)}>
                    查看详情
                </BaseButton>
            )
        }
    ];

    const searchFormItems = [
        <Form.Item label='生产批次' name='productionBatch'>
            <BaseInput placeholder='请输入批次号'></BaseInput>
        </Form.Item>,
        <Form.Item label='创建时间' name='createTime'>
            <BaseDatePicker style={{ width: '100%' }}></BaseDatePicker>
        </Form.Item>,
        <Form.Item label='质检结果' name='inspectionResults'>
            <BaseSelect placeholder='请选择' options={Enum2Object(SourceInspectResultsEnum)}></BaseSelect>
        </Form.Item>,
        <Form.Item label='质检机构' name='inspectionOrgName'>
            <BaseInput placeholder='请输入质检机构'></BaseInput>
            {/* <BaseSelect
                placeholder='请选择'
                options={queryOrgListData?.map((item) => ({
                    label: item?.shortName,
                    value: item?.id
                }))}
            ></BaseSelect> */}
        </Form.Item>,
        ...([RoleEnum.平台方]?.includes(LocalLoginIdentity)
            ? [
                  <Form.Item label='生产加工企业' name='coreId'>
                      <BaseSelect
                          placeholder='请选择'
                          options={queryCoreOrgListData?.map((item) => ({
                              label: item?.shortName,
                              value: item?.id
                          }))}
                      ></BaseSelect>
                  </Form.Item>
              ]
            : [])
    ];

    return (
        <>
            <Card style={{ marginBottom: 10 }} title={<PageTitle title='质检溯源列表' bg='container zhijian' />}>
                <Form
                    form={form}
                    labelCol={{ span: 5 }}
                    labelAlign='left'
                    className='label-title label-title-more'
                    onFinish={(values) => {
                        console.log(values, 'values');
                        const TimeArr = QueryTime(values?.createTime);
                        setQueryParams({
                            ...values,
                            productionBatch: values?.productionBatch?.trim(),
                            startTime: TimeArr?.[0],
                            endTime: TimeArr?.[1],
                            pageIndex: 1,
                            pageSize: 10
                        });
                    }}
                >
                    <Row gutter={[36, 12]}>
                        {searchFormItems.slice(0, isSimpleSearch ? 2 : searchFormItems.length).map((searchFormItem) => (
                            <Col key={searchFormItem.key} span={8}>
                                {searchFormItem}
                            </Col>
                        ))}
                        <Col span={[RoleEnum.平台方]?.includes(LocalLoginIdentity) ? 8 : isSimpleSearch ? 8 : 16}>
                            <div style={{ display: 'flex', justifyContent: 'end' }}>
                                <Space>
                                    <BaseButton
                                        type='primary'
                                        htmlType='submit'
                                        icon={<SearchOutlined rev={undefined} />}
                                    >
                                        查询
                                    </BaseButton>
                                    <BaseButton
                                        type='dashed'
                                        className='primaryBtn'
                                        icon={<SyncOutlined rev={undefined} />}
                                        onClick={() => {
                                            form.resetFields();
                                            setQueryParams({
                                                pageIndex: 1,
                                                pageSize: 10
                                            });
                                        }}
                                    >
                                        重置
                                    </BaseButton>
                                    <BaseButton
                                        style={{ color: '#80a932' }}
                                        type='link'
                                        onClick={() => {
                                            setIsSimpleSearch(!isSimpleSearch);
                                        }}
                                    >
                                        {isSimpleSearch ? '展开' : '收起'}
                                        {isSimpleSearch ? (
                                            <DownOutlined rev={undefined} />
                                        ) : (
                                            <UpOutlined rev={undefined} />
                                        )}
                                    </BaseButton>
                                </Space>
                            </div>
                        </Col>
                    </Row>
                </Form>
            </Card>
            <BaseCard className={styles.coreFIrmContainer}>
                <BaseTable
                    loading={queryInspect.isFetching}
                    columns={columns}
                    dataSource={queryInspectListData?.records}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={queryParams?.pageIndex}
                    pageSize={queryParams?.pageSize}
                    total={queryInspectListData?.total}
                    onShowSizeChange={(page, pageSize) => {
                        setQueryParams({
                            ...queryParams,
                            pageIndex: page,
                            pageSize
                        });
                    }}
                    onChange={(page, pageSize) => {
                        setQueryParams({
                            ...queryParams,
                            pageIndex: page,
                            pageSize
                        });
                    }}
                />
            </BaseCard>
        </>
    );
};

export default Inspect;

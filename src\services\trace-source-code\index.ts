import request from '../request';
//溯源码分页接口
export const traceSourceCodePage = (obj: any) => {
    return request({
        url: '/trace-source-code/traceSourceCodePage',
        method: 'post',
        data: obj
    });
};
//溯源列表详情
export const codeDetail = (obj: any) => {
    return request({
        url: `/trace-source-code/codeDetail?traceCodeId=${obj.traceCodeId}`,
        method: 'post',
        data: obj
    });
};

//溯源码分页
export const traceCodePage = (obj: any) => {
    return request({
        url: '/trace-code/page',
        method: 'post',
        data: obj
    });
};

//溯源码详情
export const traceCodeDetail = (obj: any) => {
    return request({
        url: '/trace-code/detail',
        method: 'get',
        params: obj
    });
};

//溯源码查询记录
export const traceCodeCheckTimes = (obj: any) => {
    return request({
        url: '/trace-code/queryRecord',
        method: 'post',
        data: obj
    });
};
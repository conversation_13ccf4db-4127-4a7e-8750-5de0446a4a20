/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-11-02 15:06:03
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import SearchForm from '@components/search-form';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space, Input, Modal, Select } from 'antd';
import useUrlState from '@ahooksjs/use-url-state';
import { randomPassword } from '@utils';
import styles from './index.module.less';
import { searchConfig, staff } from './config';
import { useMutation, useQuery } from 'react-query';
import { useRef, useState } from 'react';
import { menuList } from '@services/menu';
import {
    staffPage,
    addStaff,
    userModiy,
    updateStaff,
    resetPassWord,
    staffModiy,
    resetStaffPassWord
} from '@services/user';
import BaseModal from '@components/base-modal';
import FilterForm from '@components/filter-form';
import PageTitle from '@components/page-title';
import { SearchOutlined, PlusOutlined, SyncOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import sha256 from 'crypto-js/sha256';
import { useAccountList } from '../../myhooks/useaccountlist';
import copyToClipboard from 'copy-to-clipboard';
import BaseInput from '@components/base-input';
import WithPaginate from '../../hoc/withpaginate';
import { ColumnsType } from 'antd/lib/table';
import { ReformChainError } from '@utils/errorCodeReform';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { CheckCircleFilled } from '@ant-design/icons';
import rsaEncrypt from '@utils/rsa';

const { confirm } = Modal;
const EmployeesManager = (props: any) => {
    const [successModalVisible, setsuccessModalVisible] = useState(false);
    const [search]: any = Form.useForm();
    const queryuser: any = useRef(null);
    const [resetPass, setresetPass] = useState('');
    const [editId, setEditId] = useState('');
    const [createPassword, setCreatePassword] = useState('');
    const { pageInfo, handlePaginationChange } = props;
    const [addModalVisible, setAddModelVisible] = useState(false);
    const [editModalVisible, setEditModelVisible] = useState(false);
    const [loadings, setLoadings] = useState(false);
    const [addEmployeesForm] = Form.useForm();
    const [creatEmployeesForm] = Form.useForm();
    const [editEmployeesForm] = Form.useForm();
    const [resetFrom] = Form.useForm();
    const pswRef: any = useRef();
    // const pswValue = Form.useWatch('password', resetFrom);
    const [resetPswVisible, setresetPswVisible] = useState(false);
    //重置密码接口
    const resetpassword = useMutation(resetStaffPassWord, {
        onSuccess(res) {
            message.success('重置密码成功');
            staffquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            staffquery.refetch();
        }
    });
    //修改状态
    const staffmodiy = useMutation(staffModiy, {
        onSuccess(res) {
            message.success('修改状态成功');
            staffquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            staffquery.refetch();
        }
    });
    //编辑用户
    const updatestaff = useMutation(updateStaff, {
        onSuccess(res) {
            setEditModelVisible(false);
            message.success('编辑员工成功');
            staffquery.refetch();
        },
        onError(err: any) {
            setEditModelVisible(true);
            ReformChainError(err);
        }
    });
    const staffAdd = useMutation(addStaff, {
        onSuccess(res: any) {
            console.log('res999', res);
            message.success('添加用户成功');
            setAddModelVisible(false);
            setsuccessModalVisible(true);
            setLoadings(false);
            staffquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            setLoadings(false);
            staffquery.refetch();
        }
    });
    //权限查询表
    const menulist = useQuery(
        ['candidatae', pageInfo],
        () => {
            return menuList();
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    console.log('menulist', menulist);
    const staffquery = useQuery(
        ['userquery', pageInfo],
        () => {
            return staffPage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                param: queryuser?.current?.name?.trim() || undefined,
                state: queryuser?.current?.data,
                isQueryMenu: 1
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    // console.log("staffquery",staffquery)
    //员工列表数据
    const tableData = staffquery?.data?.data?.records?.map((item: any) => ({
        account: item.userId,
        phone: item.phoneNumber,
        menu: item.menuName?.join(','),
        time: item.createTime,
        status: item.state,
        operation: item.userName
    }));
    //增加
    const addEmployeesConfigs = [
        {
            label: '用户名',
            type: 'Input',
            value: 'userName',
            placeholder: '请输入用户名',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,30}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入用户名!');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            if (value.length > 30) {
                                callback('请保持字符在30字符以内!');
                            } else {
                                callback('请输入用户名，支持中文、字母或数字!');
                            }
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            label: '联系方式',
            type: 'Input',
            value: 'phoneNumber',
            placeholder: '请输入',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^(?:(?:\+|00)86)?1[3-9]\d{9}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入联系方式');
                        } else if (verify === false) {
                            callback('请输入正确的手机号');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            label: '权限',
            type: 'Custom',
            value: 'limits',
            showSearch: false,
            rules: [{ required: true, message: '' }],
            children: (
                <Form.Item name='limits' rules={[{ required: true, message: '请选择权限!' }]}>
                    <Select
                        mode='multiple'
                        placeholder='请选择'
                        showArrow
                        showSearch={false}
                        options={menulist?.data?.data
                            ?.filter((val: any) => val?.menuName.indexOf('参与方') < 0)
                            .map((item: any) => {
                                return {
                                    label: item?.menuName,
                                    options: item?.childrenMenu
                                        ?.filter((val: any) => val?.menuName.indexOf('原料') < 0)
                                        .map((item: any) => {
                                            return {
                                                label: item?.menuName,
                                                value: item?.id
                                            };
                                        })
                                };
                            })}
                    ></Select>
                </Form.Item>
            )
        }
    ];
    //编辑
    const editEmployeesConfigs = [
        {
            label: '用户名',
            type: 'Input',
            value: 'userName',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,30}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入用户名!');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            if (value.length > 30) {
                                callback('请保持字符在30字符以内!');
                            } else {
                                callback('请输入用户名，支持中文、字母或数字!');
                            }
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            label: '联系方式',
            type: 'Input',
            value: 'phoneNumber',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^(?:(?:\+|00)86)?1[3-9]\d{9}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入联系方式');
                        } else if (verify === false) {
                            callback('请输入正确的手机号');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            label: '权限',
            type: 'Custom',
            value: 'limits',
            showSearch: false,
            rules: [{ required: true, message: '' }],
            children: (
                <Form.Item name='limits' rules={[{ required: true, message: '请选择权限!' }]}>
                    <Select
                        mode='multiple'
                        placeholder='请选择'
                        showArrow
                        showSearch={false}
                        options={menulist?.data?.data
                            ?.filter((val: any) => {
                                console.log('val?.menuName', val?.menuName, val?.menuName.indexOf('参与方') < 0);
                                return val?.menuName.indexOf('参与方') < 0;
                            })
                            .map((item: any) => {
                                return {
                                    label: item?.menuName,
                                    options: item?.childrenMenu
                                        ?.filter((val: any) => val?.menuName.indexOf('原料') < 0)
                                        .map((item: any) => {
                                            return {
                                                label: item?.menuName,
                                                value: item?.id
                                            };
                                        })
                                };
                            })}
                    ></Select>
                </Form.Item>
            )
        }
    ];
    const listColumn: ColumnsType<any> = [
        {
            title: '账号',
            dataIndex: 'account',
            key: 'account',
            ellipsis: true
        },
        {
            title: '用户名',
            dataIndex: 'operation',
            key: 'operation',
            ellipsis: true
        },

        {
            title: '联系方式',
            dataIndex: 'phone',
            key: 'phone',
            ellipsis: true
        },
        {
            title: '权限',
            dataIndex: 'menu',
            key: 'menu',
            ellipsis: true
        },
        {
            title: '状态',
            dataIndex: 'status',
            ellipsis: true,
            key: 'status',
            render: (data: any) => (
                <span style={{ color: data ? '#F64041' : '#666666' }}>
                    <Badge
                        status={data ? 'error' : 'success'}
                        color={data ? '#F64041' : 'rgb(36, 171, 59)'}
                        text={data ? '禁用' : '可用'}
                    />
                </span>
            )
        },
        {
            width: 300,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle'>
                    <BaseButton
                        type='dashed'
                        className={record.status ? 'primaryBtn' : 'warnBtn'}
                        // onClick={() => {
                        //     const opp = staffmodiy.mutate({
                        //         state: !record?.status ? 1 : 0,
                        //         userId: record?.account
                        //     });
                        // }}
                        onClick={
                            record.status
                                ? () => {
                                      staffmodiy.mutate({
                                          userId: record?.account
                                      });
                                  }
                                : () => {
                                      showConfirm(record?.account);
                                  }
                        }
                    >
                        {record.status ? '启用' : '禁用'}
                    </BaseButton>
                    <BaseButton
                        type='text'
                        className='editBtn'
                        onClick={() => {
                            console.log('record', record);
                            const paths = record?.menu;
                            const staffmenu = paths?.split(',')?.map((item: any) => {
                                return staff[item];
                            });
                            // console.log("staffmenu",staffmenu)
                            editEmployeesForm.setFieldsValue({
                                userName: record?.operation,
                                phoneNumber: record?.phone,
                                userId: record?.account,
                                limits: staffmenu
                            });
                            setEditId(record?.account);
                            setEditModelVisible(true);
                        }}
                    >
                        编辑
                    </BaseButton>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            pswRef.current = record?.account;
                            setresetPswVisible(true);
                        }}
                    >
                        重置密码
                    </BaseButton>
                </Space>
            )
        }
    ];
    //禁用
    const showConfirm = (id: any) => {
        confirm({
            title: '确定要禁用该用户吗？',
            okText: '停用',
            cancelText: '取消',
            icon: <ExclamationCircleFilled rev={undefined} />,
            content: `禁用后该用户无法使用${sessionStorage.systemTitle}`,
            onOk() {
                staffmodiy.mutate({
                    userId: id
                });
            },
            onCancel() {}
        });
    };

    const addEmployeesConfig = {
        okText: '确定',
        title: '新增员工账号',
        loadings,
        visible: addModalVisible,
        setVisible: setAddModelVisible,
        okHandle: async () => {
            try {
                const values = await addEmployeesForm.validateFields();
                console.log('values', values);
                const randomPd = randomPassword(8);
                setCreatePassword(randomPd);
                // 后续打开，目前为实现 ui
                setLoadings(true);
                await staffAdd.mutate({
                    userName: values?.userName,
                    phoneNumber: await rsaEncrypt(values?.phoneNumber),
                    limits: values?.limits,
                    password: await rsaEncrypt(randomPd)
                });
                setLoadings(false);
            } catch {}
        },
        onCancelHandle: () => {
            setAddModelVisible(false);
            addEmployeesForm.resetFields();
        }
    };

    const editEmployeesConfig = {
        okText: '确定',
        title: '编辑员工',
        visible: editModalVisible,
        loadings,
        setVisible: setEditModelVisible,
        okHandle: async () => {
            try {
                const data = await editEmployeesForm.validateFields();
                console.log('editEmployeesForm', data);
                setLoadings(true);
                await updatestaff.mutate({
                    userId: editId,
                    userName: data?.userName,
                    phoneNumber: await rsaEncrypt(data?.phoneNumber),
                    limits: data?.limits
                });
                setLoadings(false);

                // editEmployeesForm.resetFields();
            } catch {}
        },
        onCancelHandle: () => {
            setEditModelVisible(false);
            editEmployeesForm.resetFields();
        }
    };

    const resetPasswordConfig = {
        title: '重置密码',
        visible: resetPswVisible,
        setVisible: setresetPswVisible,
        okHandle: async () => {
            if (!resetPass) {
                message.error('请生成登录密码!');
                return;
            }
            resetpassword.mutate({
                userId: pswRef.current,
                password: await rsaEncrypt(resetPass)
            });
            setresetPass('');
            setresetPswVisible(false);
            // adminresetLoginPsw.mutate({
            //     user_id: resetPswVisible.id,
            //     new_password: pswValue,
            // })
            resetFrom.resetFields();
        },
        onCancelHandle: () => {
            setresetPass('');
            setresetPswVisible(false);
            resetFrom.resetFields();
        }
    };

    //查询
    const onFinish = (values: any) => {
        handlePaginationChange(1);
        console.log('values', values);
        queryuser.current = values;
        console.log('queryuser', queryuser);
        staffquery.refetch();
    };
    //创建数据
    const successConfigs = [
        {
            label: '用户名',
            value: 'managername',
            placeholder: '请输入用户名',
            className: 'manaName',
            display: addEmployeesForm.getFieldValue('userName')
        },
        {
            label: '联系方式',
            value: 'phone',
            placeholder: '请输入',
            className: 'phone',
            display: addEmployeesForm.getFieldValue('phoneNumber')?.slice(0, 3) + 'xxxxxxxx'
        },
        {
            label: '员工账号',
            value: 'manageraccount',
            placeholder: '请输入',
            className: 'phone',
            display: staffAdd?.data?.data
        },
        {
            label: '密码',
            value: 'password',
            placeholder: '请输入',
            className: 'phone',
            display: '. . . . . . . . . .'
        }
    ];
    //创建成功
    const successConfig = {
        okText: '复制密码',
        title: '创建成功',
        hidden: true,
        dispalys: true,
        visible: successModalVisible,
        setVisible: setsuccessModalVisible,
        okHandle: async () => {
            const values = await addEmployeesForm.getFieldsValue();
            console.log('values', values);
            setsuccessModalVisible(false);
            const username: any = {
                // account: staffAdd?.data?.data?.account,
                password: createPassword //断点加密
            };
            console.log('username', username);
            const copyRet = copyToClipboard(`${username.password}`);
            copyRet ? message.success('复制成功') : message.error('复制失败');
            addEmployeesForm.resetFields();
        },
        onCancelHandle: () => {
            setsuccessModalVisible(false);
            addEmployeesForm.resetFields();
        }
    };
    return (
        <>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
                title={<PageTitle title='员工列表' bg='container yuan' />}
            >
                <BaseTable
                    rowKey='account'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return (
                            <TableHead
                                LeftDom={
                                    <div
                                        // className="searchContainer"
                                        className={styles.searchContainer}
                                    >
                                        <Form
                                            onFinish={onFinish}
                                            layout='inline'
                                            labelAlign='left'
                                            form={search}
                                            className='label-title'
                                            style={{ marginBottom: '20px' }}
                                        >
                                            {/* <SearchForm /> */}
                                            <FilterForm itemConfig={searchConfig} size={230} labelCol={4} />
                                            <BaseButton
                                                htmlType='submit'
                                                type='primary'
                                                // className='searchBtn'
                                                style={{ width: 100 }}
                                                className={`${styles.searchBtn} ${styles.baseBtn}`}
                                                icon={<SearchOutlined rev={undefined} />}
                                            >
                                                查询
                                            </BaseButton>
                                        </Form>
                                        <BaseButton
                                            type='dashed'
                                            className='primaryBtn'
                                            style={{ width: 100 }}
                                            icon={<SyncOutlined rev={undefined} />}
                                            onClick={() => {
                                                console.log('queryuser.current', queryuser.current);
                                                queryuser.current = null;
                                                staffquery.refetch();
                                                search.resetFields();
                                            }}
                                        >
                                            重置
                                        </BaseButton>
                                    </div>
                                }
                                RightDom={
                                    <BaseButton
                                        type='dashed'
                                        icon={<PlusOutlined rev={undefined} />}
                                        className='bgBtn'
                                        onClick={() => {
                                            setAddModelVisible(true);
                                        }}
                                        style={{ position: 'relative', top: -10 }}
                                    >
                                        新增员工账号
                                    </BaseButton>
                                }
                            />
                        );
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={staffquery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={staffquery?.data?.data?.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>

            <BaseModal {...addEmployeesConfig}>
                <Form name='addEmployeesForm' form={addEmployeesForm} className='edit-label-title'>
                    {<FilterForm itemConfig={addEmployeesConfigs} />}
                </Form>
            </BaseModal>

            <BaseModal {...editEmployeesConfig}>
                <Form name='editEmployeesForm' form={editEmployeesForm} className='edit-label-title'>
                    {<FilterForm itemConfig={editEmployeesConfigs} />}
                </Form>
            </BaseModal>

            <BaseModal
                centered
                {...resetPasswordConfig}
                width={515}
                style={{ height: 411 }}
                cancelText='取消'
                okText='确认'
            >
                <Form
                    name='resetpassword'
                    labelCol={{
                        span: 5
                    }}
                    wrapperCol={{
                        span: 14
                    }}
                    autoComplete='off'
                    form={resetFrom}
                >
                    <Form.Item
                        label='重置密码'
                        name='password'
                        wrapperCol={{ span: 20 }}
                        initialValue={''}
                        className='edit-label-title'
                        rules={[
                            {
                                required: true,
                                message: '请生成登录密码!'
                            }
                        ]}
                    >
                        <div className={styles.resetContainer}>
                            <Input
                                id='pswRef'
                                value={resetPass}
                                readOnly
                                className={styles.pswInput}
                                name='resetPass'
                            />
                            <span
                                className={`${styles.btn} ${styles.newPswBtn} ${styles.active}`}
                                onClick={() => {
                                    // createNewPsw.mutate();
                                    setresetPass(randomPassword(8));
                                    console.log('123123123', pswRef);
                                }}
                            >
                                生成
                            </span>
                            <span
                                id='copyBtn'
                                className={
                                    resetPass ? `${styles.btn} ${styles.active}` : `${styles.btn} ${styles.disable}`
                                }
                                data-clipboard-target='#pswRef'
                                onClick={() => {
                                    if (resetPass) {
                                        // console.log("password", pswRef.current.input.value, pswRef);
                                        const copyRet = copyToClipboard(resetPass);
                                        // console.log('copyRet', copyRet)
                                        copyRet ? message.success('复制成功') : message.error('复制失败');
                                    }
                                }}
                            >
                                复制
                            </span>
                        </div>
                    </Form.Item>
                </Form>
            </BaseModal>
            <BaseModal
                {...successConfig}
                cancelButtonProps={{ style: { display: 'none' } }}
                className='employees_header'
                // closeIcon={<CheckCircleFilled className={styles.employess_icon} rev={undefined} />}
            >
                <Form
                    name='creatEmployeesForm'
                    form={creatEmployeesForm}
                    labelAlign='left'
                    className='employess-label-title'
                >
                    {<FilterForm itemConfig={successConfigs} />}
                </Form>
            </BaseModal>
        </>
    );
};

export default WithPaginate(EmployeesManager);

/*
 * @Description: 掩码做权限验证
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-23 09:41:31
 * @LastEditTime: 2022-11-01 17:29:53
 * @LastEditors: PhilRandWu
 */
/**
 * @description:  权限枚举
 * @return {*}
 */
export enum PermissionEnum {
    NONE = 0,
    UPLOAD = 1,
    DOWNLOAD = 2
}

export const method = {
    addPermission: (userPermission: any, flag: any) => {
        return userPermission | flag;
    },
    hasPermission: (permission: any, flag: any) => {
        return (permission & flag) === flag;
    },
    removePermission: (userPermission: any, flag: any) => {
        return userPermission & ~flag;
    },
    listPermission: (userPermission: any) => {
        let result: string[] = [];
        if (userPermission & PermissionEnum.UPLOAD) {
            result.push('UPLOAD');
        }
        if (userPermission & PermissionEnum.DOWNLOAD) {
            result.push('DOWNLOAD');
        }
        return result;
    }
};

/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-11 10:48:34
 * @LastEditTime: 2022-11-01 18:32:13
 * @LastEditors: PhilRandWu
 */
import BaseFormItem from '@components/base-form-item';
import { Collapse } from 'antd';
import React, { ReactElement } from 'react';
import './index.less';

const { Panel } = Collapse;

interface itemConfigInterface {
    label: string;
    value: string | number | undefined;
    name: string;
}

interface collapseInterface {
    Itemkey: string | number;
    headTitle: ReactElement;
    itemConfig: itemConfigInterface[];
}

const BaseCollapse = ({ Itemkey, headTitle, itemConfig }: collapseInterface) => {
    console.log('key', Itemkey);
    const onChange = (key: string | string[]) => {
        console.log('key', key);
    };
    return (
        <Collapse
            // defaultActiveKey={['1']}
            onChange={onChange}
            expandIconPosition='end'
        >
            <Panel header={headTitle} key={Itemkey}>
                <BaseFormItem configs={itemConfig} />
            </Panel>
        </Collapse>
    );
};
export default BaseCollapse;

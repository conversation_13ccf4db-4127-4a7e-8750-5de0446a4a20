{"name": "cm-multi-party-reconciliation", "version": "0.1.0", "private": true, "dependencies": {"@ahooksjs/use-url-state": "^3.5.1", "@ant-design/charts": "^1.4.2", "@ant-design/icons": "^4.8.1", "@craco/craco": "^7.1.0", "@reduxjs/toolkit": "^1.8.2", "@tailwindcss/postcss7-compat": "^2.2.17", "@types/crypto-js": "^4.1.1", "@types/file-saver": "^2.0.5", "@types/jest": "^27.5.2", "@types/js-md5": "^0.4.3", "@types/lodash-es": "^4.17.9", "@types/node": "^16.11.41", "@types/react": "^18.0.14", "@types/react-dom": "^18.0.5", "@types/react-router-dom": "^5.3.3", "ahooks": "^3.5.0", "antd": "^4.21.3", "antd-dayjs-webpack-plugin": "^1.0.6", "axios": "^1.6.2", "buffer": "^6.0.3", "clipboard": "^2.0.11", "copy-to-clipboard": "^3.3.1", "craco-less": "^2.0.0", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "css-loader": "^6.7.1", "dayjs": "^1.11.3", "echarts": "^5.5.0", "echarts-for-react": "^3.0.2", "eosjs": "^22.1.0", "eosjs-ecc": "^4.0.7", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "file-saver": "^2.0.5", "hashlib": "^1.0.1", "js-md5": "^0.7.3", "jsencrypt": "^3.3.2", "kind-of": "^6.0.3", "less-loader": "^11.0.0", "lidashi-tools": "^1.4.0", "lodash-es": "^4.17.21", "merkletreejs": "^0.3.11", "node-polyfill-webpack-plugin": "^2.0.0", "nth-check": "^2.1.1", "nzh": "^1.0.14", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-json-view": "^1.21.3", "react-query": "^3.39.1", "react-redux": "^8.0.2", "react-router-dom": "6", "semver": "^7.5.4", "stream-browserify": "^3.0.0", "style-loader": "^3.3.1", "typescript": "^4.7.3", "web-vitals": "^2.1.4", "webpackbar": "^5.0.2", "worker-plugin": "^5.0.1", "xlsx": "^0.18.5"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject", "prettier": "npx prettier --write ."}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/mockjs": "^1.0.6", "mockjs": "^1.1.0", "prettier": "2.7.1", "react-scripts": "5.0.1", "terser-webpack-plugin": "^5.3.10"}, "overrides": {"kind-of": "$kind-of", "postcss": "$postcss", "semver": "$semver", "nth-check": "$nth-check"}}
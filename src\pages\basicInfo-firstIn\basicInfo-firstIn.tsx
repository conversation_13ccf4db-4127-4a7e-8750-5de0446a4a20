/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-11-01 18:06:12
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import styles from './index.module.less';
import PageTitle from '@components/page-title';
import { Form, Input } from 'antd';
import { businessConfig, enterpriseConfig, headConfig } from './config';
import BaseButton from '@components/base-button';
import BaseFormItem from '@components/base-form-item';
import BaseInput from '@components/base-input';
const { TextArea } = Input;

const BasicInfoFirstIn = () => {
    const onFinish = (values: any) => {
        console.log('Success:', values);
    };

    const onFinishFailed = (errorInfo: any) => {
        console.log('Failed:', errorInfo);
    };

    return (
        <>
            <BaseCard title={<PageTitle title='编辑基础信息' />}>
                <Form
                    name='basic'
                    labelCol={{ span: 3 }}
                    wrapperCol={{ span: 6 }}
                    onFinish={onFinish}
                    onFinishFailed={onFinishFailed}
                    autoComplete='off'
                >
                    <PageTitle title='企业信息' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={enterpriseConfig} children={<BaseInput />} />

                    <PageTitle title='负责人信息' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={headConfig} children={<BaseInput />} />

                    <PageTitle title='企业介绍' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={businessConfig} children={<TextArea showCount maxLength={200} />} />

                    <div className={styles.btnContainer}>
                        <BaseButton type='primary' className={styles.saveBtn}>
                            保存
                        </BaseButton>
                        <BaseButton className={styles.cancelBtn}>取消</BaseButton>
                    </div>
                </Form>
            </BaseCard>
        </>
    );
};

export default BasicInfoFirstIn;

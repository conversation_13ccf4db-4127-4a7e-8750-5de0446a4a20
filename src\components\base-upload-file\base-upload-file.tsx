import { UploadOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import { Button, message, Upload } from 'antd';
import React from 'react';
import './index.less';

const props: UploadProps = {
    name: 'file',

    headers: {
        authorization: 'authorization-text'
    },
    onChange(info) {
        if (info.file.status !== 'uploading') {
            console.log(info.file, info.fileList);
        }
        if (info.file.status === 'done') {
            message.success(`${info.file.name} 文件上传成功`);
        } else if (info.file.status === 'error') {
            message.error(`${info.file.name} 文件上传失败`);
        }
    }
};

const BaseUploadFile: React.FC = (props: UploadProps) => (
    <div className='uploadFileContainer'>
        <Upload {...props}>
            <Button icon={<UploadOutlined rev={undefined} />} className='butcolor'>
                上传文件
            </Button>
        </Upload>
    </div>
);

export default BaseUploadFile;

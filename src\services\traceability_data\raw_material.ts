import request from '../request';

export interface IRawMaterialQuery {
    coreId: number,
    endTime: string,
    materialId: number,
    orgId: number,
    pageIndex: number,
    pageSize: number,
    purchaseBatch: string,
    startTime: string,
    state: number
}

export enum SourceRawMaterialStateEnum {
    "正常",
    "作废"
}

export default class RawMaterialService {
    public static async Query(data: IRawMaterialQuery) {
        return request({
            url: '/traceData/materialPage',
            method: 'post',
            data
        });
    };

    public static async detail(id: number) {
        return request({
            url: '/traceData/materialDetail',
            method: 'get',
            params: {
                id
            }
        });
    }

    public static async getListByPurchaseId(id: number) {
        return request({
            url: '/production/listByPurchaseId',
            method: 'post',
            data: {id}
        });
    }
}
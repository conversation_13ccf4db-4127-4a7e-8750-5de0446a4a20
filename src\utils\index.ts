import { getUploadUrl } from '@services';
import { message } from 'antd';
import axios from 'axios';
import dayjs from "dayjs";
import rsaDecrypte from './rsa-dec';

export { decrypt } from './blockChainUtils';
export { PermissionEnum, method } from './permisson';

export function randomPassword(length: number) {
    length = Number(length);
    // Limit length
    if (length < 6) {
        length = 6;
    } else if (length > 16) {
        length = 16;
    }
    let passwordArray = ['ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz', '1234567890', '!#$%'];
    var password = [];
    let n = 0;
    for (let i = 0; i < length; i++) {
        // If password length less than 9, all value random
        if (password.length < length - 4) {
            // Get random passwordArray index
            let arrayRandom = Math.floor(Math.random() * 4);
            // Get password array value
            let passwordItem = passwordArray[arrayRandom];
            // Get password array value random index
            // Get random real value
            let item = passwordItem[Math.floor(Math.random() * passwordItem.length)];
            password.push(item);
        } else {
            // If password large then 9, lastest 4 password will push in according to the random password index
            // Get the array values sequentially
            let newItem = passwordArray[n];
            let lastItem = newItem[Math.floor(Math.random() * newItem.length)];
            // Get array splice index
            let spliceIndex = Math.floor(Math.random() * password.length);
            password.splice(spliceIndex, 0, lastItem);
            n++;
        }
    }
    return password.join('');
}
export function renderRole(roleid: any) {
    switch (roleid) {
        case 1:
            return '生产加工企业'

        case 2:
            return '供应商'

        case 3:
            return '质检机构'

        case 4:
            return '监管机构'

        case 5:
            return '物流企业'

        case 6:
            return '运营方'

        case 7:
              return '农户'
        case 8:
              return '销售企业'
        default:
            break;
    }
}

export const fileUpload: any = async ({ file, onError, onProgress, onSuccess, setIsUpLoading }: any) => {
    console.log('fileUpload', file)
    try {
        console.log('setIsUpLoading',setIsUpLoading)
        setIsUpLoading(true);
        let uploadUrlRet = await getUploadUrl(file?.name)
        console.log('uploadUrlRet',uploadUrlRet)
        const faceUrl = await decryptedUrl(uploadUrlRet);
        console.log('faceUrl',faceUrl)
        try {
            const uploadRet = await axios({
                method: 'put',
                url: faceUrl,
                onUploadProgress: onProgress,
                data: file
            });
            console.log(uploadRet)
            onSuccess?.({
                ...uploadRet,
                fileUrl: faceUrl.split('?')[0]
            });
            setIsUpLoading(false);
        } catch (err: any) {
            message.error('文件上传失败');
            onError?.(err);
        }
    } catch (err: any) {
        message.error('获取文件上传路径失败');
        onError?.(err);
    }
};
export const getFileUrlFormUploadedFile = (files?: any[]): (string[] | undefined) => {
    if (!files) {
        return undefined
    }
    if (files.length > 0) {
        return files.map((item: any) => item?.response?.fileUrl)
    } else {
        return undefined
    }
}
export const fileUrlsToUploadFileList = (urls: string[]) => {
    console.log("aaaaaaaaaa", urls)
    const result =  Promise.all(isArrayArr(urls).map(async(url, index) => {
        if(url&&url.indexOf('://')==-1){ //看是否解密过解密过会变成http://或者https://
                url = await decryptedUrl(url)
        }
        console.log('url=qqq=', url)
        console.log('qqqqqqqqqq',
            ({
                name: url?.split("?")?.[0]?.split("/")?.[5] ? decodeURIComponent(url?.split("?")?.[0]?.split("/")?.[5]) : `item_` + index, //避免相同内容出现相同的hash导致删除时的异常
                uid: url + index,
                response: {
                    fileUrl: url
                },
                url:url
            })
        )
        const file = ({
            name: url?.split("?")?.[0]?.split("/")?.[5] ? decodeURIComponent(url?.split("?")?.[0]?.split("/")?.[5]) : `item_` + index, //避免相同内容出现相同的hash导致删除时的异常
            uid: url + index,
            response: {
                fileUrl: url
            },
            url:url
        })
        return file
    }))
    return result
};
export const dealJson = (detailJson:any)=>{
    let dealArr = []
    dealArr.push(detailJson)
    const proArr = Promise.all(isArrayArr(dealArr).map(async(items) => {
        const keyArr = ['qualification','video','picture','placeImg','placeVideo','placeAptitude','productImg','productVideo']
        const morePic = ['picture','placeImg','productImg',]
        keyArr.forEach(async(val:any) => {
            // detailJson['qualification']= await decryptedUrl(detailJson.qualification)
            // detailJson['video'] = await decryptedUrl(detailJson.video)
            if(items[val]&&items[val].length>0){
                if(Array.isArray(items[val])){
                    items[val] = Promise.all(items[val]?.map(async(item:string)=>await decryptedUrl(item)))
                }else{
                    items[val] = await decryptedUrl(items[val])
                }
            }
        });
        console.log('decrypted--------',items)
        return items
    }))
    console.log('proArr',proArr)
    return proArr

}
function isPromise(obj:any) {
    return !!obj && typeof obj.then === 'function';
  }

export const  isArrayArr = (arr:any[])=>{
    if(Array.isArray(arr)&&arr.length>0){
        return arr
    }
    return []
}

//decryptStr 处理加密串
export  const decryptStr = async (str:string) => {
    if(str){
        const det = await rsaDecrypte(str)
        console.log('det--str',det)
        return det
    }
    return ''
}

// 处理加密URL
export const decryptedUrl = async (str:any)=>{
  console.log('async-----')
  
  if(str&&str.length>0&&!isPromise(str)){
      if(str.indexOf('://')==-1){
          console.log('处理',str)
          const index = str.indexOf('?')
          if(index==-1){
            const det = await rsaDecrypte(str)
            return det
          }else{
            const url= str.slice(0, index)
            console.log('decryptedUrl',url)
            const det = await rsaDecrypte(str)
            console.log('det',det)
            return det+str.slice(index, str.length)||''
          }

      }
      return str
  }
  return ''
}

// 处理加密URL
export const decryptedUrlfn = async (str:string)=>{
    if(str){
        const index = str.indexOf('?')
        const url= str.slice(0, index)
        console.log('decryptedUrl',url)
        const det = await rsaDecrypte(url)
        console.log('detfn',det)
        return det+str.slice(index, str.length)||''
    }
    return ''
}

// 处理json里有加密后的项
export const decryJsonRes = async(json:any) =>{
    if(json===undefined)    return {}
    const  urlArr = ['picture','qualification','video']
    let initJson = JSON.parse(JSON.stringify(json))
    console.log('initJson',initJson)
    await urlArr.forEach(async (item:string) =>{
        if(item.indexOf('://')==-1 ){ //看是否解密过解密过会变成http://或者https://
            if(initJson[item]){
                if(Array.isArray(initJson[item])){
                    initJson[item]=initJson[item].map(
                        async(val:string) => await decryptedUrl(val))
                }else{
                    console.log('item',item)
                    initJson[item] = await decryptedUrl(initJson[item])
                }
            }
        }
    })
    return initJson

}

export const urlsToList = (urls: string[]) => {
    console.log('urlsToList-urls', urls)
    return urls.map((url, index) => {
        const index1 = url.indexOf('.png')
        const index2 = url.indexOf('.jpg')
        const index3 = url.indexOf('?%')
        const index4 = url.indexOf('?')
        const index5 = url.indexOf('g%')
        const index6 = url.indexOf('G%')
        // if(index1>0){
        //     url= url.slice(0, index1+4)
        // }
        // else if(index2>0){
        //     url= url.slice(0, index2+4)
        // }
        // else{
        //     if(index3>0){
        //         url= url.slice(0, index3)
        //     }
        //     else if(index5>0){
        //         url= url.slice(0, index5+1)
        //     }else if(index6>0){
        //         url= url.slice(0, index6+1)
        //     }
        //     else if(index4>0){
        //         url= url.slice(0, index4)
        //     }
        // }
        return url
    });
};


export function QueryTime(Time: any) {
    return Array.isArray(Time)
        ? [dayjs(Time?.[0]).startOf('date').toISOString(), dayjs(Time?.[1]).endOf('date').toISOString()]
        : ['', ''];
}
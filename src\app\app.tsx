import Routes from '@router';
import { getUserInfo } from '@services/user';
import { useAppDispatch } from '@store';
import { loginAction } from '@store/slice-reducer/user';
import { decryptStr } from '@utils';
import { ReformChainError } from '@utils/errorCodeReform';
import { Spin } from 'antd';
import * as React from 'react';
import { useQuery } from 'react-query';
import { useSelector } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import './index.less';

function App() {
    const [isLoginCheckEnd, setIsLoginCheckEnd] = React.useState(false);
    const [spinning, setSpinning] = React.useState<boolean>(false);
    const dispatch = useAppDispatch();
    const state = useSelector((state: any) => state.spinContext);
    console.log('showLoadingFlag', state);

    const getUserInfoQuery = useQuery(['getUserInfoQuery'], getUserInfo, {
        enabled: false, //登录管理员判断
        async onSuccess(loginRes) {
            const telephone = await decryptStr(loginRes?.data?.phoneNumber);
            const userName = await decryptStr(loginRes?.data?.userName);
            const userinfo = {
                name: userName,
                user_id: loginRes?.data?.userId,
                role_id: loginRes?.data?.roleId,
                circle_id: loginRes?.data?.circleId,
                company_id: loginRes?.data?.companyId,
                isFirst: loginRes?.data?.isFirst,
                telephone: telephone,
                privateKey: loginRes?.data?.privateKey,
                publicKey: loginRes?.data?.publicKey
            };
            localStorage.setItem('userdata', JSON.stringify(userinfo));
            sessionStorage.setItem('encryptKey', loginRes?.data?.privateKey);
            // 暂时注释，以待后续对接,对接使用以下配置，暂时使用 getMenusByRole(RoleEnum.生产加工企业) 模拟 loginRes?.data?.menuList
            // const userMenuPermissionList = (loginRes?.data?.menuList || []).map((menuId: any) => menuIdMap[menuId]);

            dispatch(
                loginAction({
                    userInfo: loginRes?.data
                })
            );
            setIsLoginCheckEnd(true);
        },
        onError(err: any) {
            ReformChainError(err);
            setIsLoginCheckEnd(true);
        },
        retry: false
    });

    React.useEffect(() => {
        const jwt = sessionStorage.getItem('jwt');
        if (jwt) {
            getUserInfoQuery.refetch();
        } else {
            setIsLoginCheckEnd(true);
        }
    }, []);
    React.useEffect(() => {
        console.log('state.showLoading', state);
        setSpinning(state.showLoading);
    }, [state.showLoading]);

    return (
        <div className='App'>
            {/* <BrowserRouter basename='/web/'> */}
            <BrowserRouter>
                {/* <Router /> */}
                {isLoginCheckEnd ? (
                    <Routes />
                ) : (
                    <div
                        style={{
                            height: '100vh',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}
                    >
                        <Spin size='large'></Spin>
                    </div>
                )}
                {spinning && (
                    <div
                        style={{
                            height: '100vh',
                            width: '100vw',
                            left: 0,
                            top: 0,
                            position: 'fixed',
                            zIndex: 10000,
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}
                    >
                        <Spin spinning={spinning} size='large'></Spin>
                    </div>
                )}
            </BrowserRouter>
        </div>
    );
}

export default App;

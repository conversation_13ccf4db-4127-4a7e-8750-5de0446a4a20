import request from "@services/request";


export default class PublicSourceService {

    public static async RawMaterialList(valid?: boolean) {
        return request({
            url: '/material/list',
            method: 'get',
            params: {
                valid
            }
        });
    }

    public static async orgNameList(data: any) {
        return request({
            url: '/traceData/productionPage',
            method: 'post',
            data
        });
    }

    public static async coreNameList(data: any) {
        return request({
            url: '/traceData/productionPage',
            method: 'post',
            data
        });
    }

    public static async productNameList(data: any) {
        return request({
            url: '/traceData/productionPage',
            method: 'post',
            data
        });
    }


    public static async getChainHis(transactionId: string) {
        return request({
            method: 'get',
            url: '/trace-code/getChainHis',
            params: {transactionId}
        })
    }


    public static async getOrgList(obj?: any) {
        return request({
            method: 'get',
            url: '/org/list',
            params: obj
        })
    }


    public static async getCoreList() {
        return request({
            method: 'get',
            url: '/org/coreList',
        })
    }


    public static async getProductList(valid?: boolean) {
        return request({
            method: 'get',
            url: '/product/list',
            params: {
                valid
            }
        })
    }
}
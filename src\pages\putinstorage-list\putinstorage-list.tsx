/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-09 16:17:21
 * @LastEditTime: 2022-11-01 18:17:02
 * @LastEditors: PhilRandWu
 */
/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-10-09 16:16:29
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space, DatePicker } from 'antd';
import useUrlState from '@ahooksjs/use-url-state';

import { storageInPage, cancelStorageIn } from '@services/storage-in';
import { useMutation, useQuery } from 'react-query';
import dayjs from 'dayjs';
import { ReformChainError } from '@utils/errorCodeReform';
import { ColumnsType } from 'antd/lib/table';

import styles from './index.module.less';
import { useRef, useState } from 'react';
import PageTitle from '@components/page-title';
import { PlusOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { useAccountList } from '../../myhooks/useaccountlist';
import SingleSearch from '@components/single-search';
import { useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import FilterForm from '@components/filter-form/filter-form';
import { SearchOutlined,SyncOutlined } from '@ant-design/icons';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

const { RangePicker } = DatePicker;

const PutInStorageList = (props: any) => {
    const navigate = useNavigate();
    const { pageInfo, handlePaginationChange } = props;
    const querylist: any = useRef('');
    const [search]: any = Form.useForm();

    //分页
    const storageInquery = useQuery(
        ['storageInquery', pageInfo],
        () => {
            return storageInPage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                warehouseNumber: querylist?.current?.warehouseNumber?.trim() || undefined,
                beginTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[0]).startOf('day')).format()
                    : undefined,
                endTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[1]).endOf('day')).format()
                    : undefined
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    console.log('userquery', storageInquery);

    const tableData = storageInquery?.data?.data?.records?.map((item: any) => ({
        id: item?.id,
        warehouseNumber: item?.warehouseNumber,
        createTime: dayjs(item?.createTime).format('YYYY-MM-DD HH:mm:ss'),
        optName: item?.optName,
        storehouse: item?.storehouse
    }));

    const listColumn: ColumnsType<any> = [
        {
            title: '入库单号',
            dataIndex: 'warehouseNumber',
            key: 'warehouseNumber',
            ellipsis: true
        },
        {
            title: '仓库名称',
            dataIndex: 'storehouse',
            key: 'storehouse',
            ellipsis: true
        },

        {
            title: '操作人',
            dataIndex: 'optName',
            key: 'optName',
            ellipsis: true
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true,
            render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
        },
        {
            width: 180,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle'>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            navigate('detail', {
                                state: {
                                    data: record
                                }
                            });
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    const searchConfig = [
        {
            label: '入库单号',
            type: 'Input',
            value: 'warehouseNumber',
            placeholder: '请输入入库单号',
            span: 12,
            className: 'find'
        },
        {
            label: '创建时间',
            type: 'Custom',
            value: 'createTime',
            placeholder: '请选择',
            span: 12,
            className: 'find',
            children: <RangePicker style={{ width: 230 }} getPopupContainer={(trigger: any) => trigger.parentNode} />
        }
    ];

    return (
        <>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
                title={<PageTitle title='入库列表' />}
            >
                <BaseTable
                    rowKey='account'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return (
                            <TableHead
                                LeftDom={
                                    <div
                                        // className="searchContainer"
                                        className={styles.searchContainer}
                                    >
                                        <Form
                                            onFinish={(values) => {
                                                handlePaginationChange(1);
                                                console.log('values', values);
                                                querylist.current = values;
                                                storageInquery.refetch();
                                            }}
                                            layout='inline'
                                            labelAlign='left'
                                            form={search}
                                            className='label-title'
                                        >
                                            <FilterForm itemConfig={searchConfig} size={230} labelCol={6} />
                                            <Space>
                                                <BaseButton
                                                    htmlType='submit'
                                                    type='primary'
                                                    // className='searchBtn'
                                                    style={{ width: 100 }}
                                                    className={`${styles.searchBtn} ${styles.baseBtn}`}
                                                    icon={<SearchOutlined rev={undefined} />}
                                                >
                                                    查询
                                                </BaseButton>
                                                <BaseButton
                                                    type='dashed'
                                                    className='primaryBtn'
                                                    style={{ width: 100 }}
                                                    icon={<SyncOutlined rev={undefined} />}
                                                    onClick={() => {
                                                        querylist.current = null;
                                                        storageInquery.refetch();
                                                        search.resetFields();
                                                    }}
                                                >
                                                    重置
                                                </BaseButton>
                                            </Space>
                                        </Form>
                                    </div>
                                }
                            />
                        );
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={storageInquery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={storageInquery?.data?.data.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>
        </>
    );
};

export default WithPaginate(PutInStorageList);

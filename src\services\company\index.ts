/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-11-01 18:29:31
 * @LastEditTime: 2022-11-01 18:29:31
 * @LastEditors: PhilRandWu
 */
import request from '../request';
//生产加工企业数据
export const corePage = (obj: any) => {
    return request({
        url: '/org/getOrgList',
        method: 'post',
        data: obj
    });
};
//生产加工企业状态改变
export const modifyUserState = (obj: any) => {
    return request({
        url: '/org/updateState',
        method: 'post',
        data: obj
    });
};
//核心候选人列表
export const candidateList = () => {
    return request({
        url: '/user/getUsableAdmin',
        method: 'get',
    });
};
//编辑企业简称
export const setBasicInfo = (obj: any) => {
    return request({
        url: '/org/modifyShortName',
        method: 'post',
        data: obj
    });
};
//编辑企业信息
export const editBasicInfo = (obj: any) => {
    return request({
        url: '/org/modifyOrg',
        method: 'post',
        data: obj
    });
};
//企业信息
export const selectParticipantDetail = (obj: any) => {
    return request({
        url: '/org/getOrgDetail',
        method: 'get',
        params: obj
    });
};

//添加企业
export const addCore = (obj: any) => {
    return request({
        url: '/org/insertOrg',
        method: 'post',
        data: obj
    });
};
export const getOrgMenuIdList = (obj: any) => {
   return request({
     url: '/menu/getOrgMenuIdList',
     method: 'get',
     params: obj
   });
  };


  export const getUserMenuList = (obj: any) => {
   return request({
     url: '/menu/getUserMenuList',
     method: 'get',
     params: obj
   });
  };
import BaseCard from '@components/base-card';
import { addMaterial, materialPage, modiMater, supplierList, updateMaterial } from '@services/material';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, Input, Select, message, Space, Modal } from 'antd';
import useUrlState from '@ahooksjs/use-url-state';

import styles from './index.module.less';
import { useRef, useState } from 'react';
import PageTitle from '@components/page-title';
import { SearchOutlined, PlusOutlined, ExclamationCircleFilled, DownOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import { useMutation, useQuery } from 'react-query';
import BaseButton from '@components/base-button';
import { useAccountList } from '../../myhooks/useaccountlist';
import copyToClipboard from 'copy-to-clipboard';
import BaseInput from '@components/base-input';
import SingleSearch from '@components/single-search';
import { Navigate, useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import { ReformChainError } from '@utils/errorCodeReform';
import { ColumnsType } from 'antd/lib/table';
import { getProductSelectList } from '@services/food';

interface IUrlState {
    pageIndex: number;
    pageSize: number;
}

const SourceList = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;
    const navigate = useNavigate();
    const [addEmployeesForm] = Form.useForm();
    const [editForm] = Form.useForm();
    const querylist = useRef('');
    const [editModalVisible, setEditModalVisible] = useState<'add' | 'edit' | false>(false);
    const [editRawId, setEditRawId] = useState<any>();

    const queryList: any = useAccountList({
        pageIndex: pageInfo.pageIndex,
        pageSize: pageInfo.pageSize
    });
    const matermodiy = useMutation(modiMater, {
        onSuccess(res) {
            message.success('修改状态成功');
            materialquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            materialquery.refetch();
        }
    });
    const materialquery = useQuery(
        ['materialquery', pageInfo],
        () => {
            return materialPage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                param: querylist?.current?.trim() || undefined
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    // 产品选择
    const productSelectListQuery = useQuery(
        ['productSelectListQuery'],
        () => {
            return getProductSelectList({
                valid: true
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            },
            enabled: !!editModalVisible
        }
    );
    // 供应商选择
    const supplierSelectListQuery = useQuery(
        ['supplierSelectListQuery'],
        () => {
            return supplierList();
        },
        {
            onError(err: any) {
                ReformChainError(err);
            },
            enabled: !!editModalVisible
        }
    );
    // 新建原料
    const addRawMaterial = useMutation(addMaterial, {
        onSuccess(res) {
            message.success('新建原料成功');
            materialquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            materialquery.refetch();
        }
    });
    // 新建原料
    const editRawMaterial = useMutation(updateMaterial, {
        onSuccess(res) {
            message.success('编辑原料成功');
            materialquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            materialquery.refetch();
        }
    });
    // console.log("userquery",userquery)
    //列表数据
    const tableData = materialquery?.data?.data?.records?.map((item: any) => ({
        account: item.materialNumber,
        materialName: item.materialName,
        fromProduct: item?.materialProductTos,
        foodIds: item?.foodIds,
        suppliers: item?.materialSupplierTos,
        status: item.state,
        id: item?.id,
        supplierIds: item?.supplierIds
    }));

    const listColumn: ColumnsType<any> = [
        {
            title: '原料编号',
            dataIndex: 'id',
            key: 'id',
            ellipsis: true
        },
        {
            title: '原料名称',
            dataIndex: 'materialName',
            key: 'materialName',
            ellipsis: true
        },

        {
            title: '所属产品',
            dataIndex: 'fromProduct',
            key: 'fromProduct',
            ellipsis: true,
            render(productNames) {
                return productNames?.map((item: any) => item?.productName).join('；');
            }
        },
        {
            title: '供应商',
            dataIndex: 'suppliers',
            key: 'suppliers',
            ellipsis: true,
            render(suppliers) {
                return !!suppliers.length ? suppliers?.map((item: any) => item?.shortName).join('；') : '-';
            }
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            ellipsis: true,
            render: (data: any) => (
                <span style={{ color: data ? '#F64041' : '#666666' }}>
                    <Badge
                        status={data ? 'error' : 'success'}
                        color={data ? '#F64041' : 'rgb(36, 171, 59)'}
                        text={data ? '禁用' : '可用'}
                    />
                </span>
            )
        },
        {
            width: 180,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle'>
                    <BaseButton
                        type='dashed'
                        className={record.status ? 'primaryBtn' : 'warnBtn'}
                        onClick={() => {
                            if (!record.status) {
                                Modal.confirm({
                                    title: '确定要禁用该原料吗?',
                                    content: '禁用后将无法为该原料创建新的原料采购批次，已创建的批次不受影响',
                                    onOk() {
                                        matermodiy.mutate({
                                            opt: record?.status ? 'ENABLE' : 'DISABLE',
                                            id: record?.id
                                        });
                                    },
                                    onCancel() {}
                                });
                            } else
                                matermodiy.mutate({
                                    opt: record?.status ? 'ENABLE' : 'DISABLE',
                                    id: record?.id
                                });
                        }}
                    >
                        {record.status ? '启用' : '禁用'}
                    </BaseButton>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            editForm.setFieldsValue({
                                ...record,
                                suppliers: record?.suppliers?.map((item: any) => item?.orgId),
                                fromProduct: record?.fromProduct?.map((item: any) => item?.productId)
                            });
                            setEditRawId(record?.id);
                            setEditModalVisible('edit');
                        }}
                    >
                        编辑
                    </BaseButton>
                </Space>
            )
        }
    ];

    const searchConfig = {
        label: '',
        handleSearch: () => {
            handlePaginationChange(1);
            materialquery.refetch();
        },
        placeholder: '输入原料编号/原料名称',
        setSearchValue: (values: any) => {
            querylist.current = values;
        }
    };

    return (
        <>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
                title={<PageTitle title='原料列表' />}
            >
                <BaseTable
                    rowKey='account'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return (
                            <TableHead
                                LeftDom={<div></div>}
                                RightDom={
                                    <div
                                        style={{
                                            display: 'flex'
                                        }}
                                    >
                                        <SingleSearch {...searchConfig} />
                                        <BaseButton
                                            type='dashed'
                                            icon={<PlusOutlined rev={undefined} />}
                                            className='greenBtn'
                                            onClick={() => {
                                                // navigate('source-add');
                                                setEditModalVisible('add');
                                            }}
                                        >
                                            新建原料
                                        </BaseButton>
                                    </div>
                                }
                            />
                        );
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={materialquery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={materialquery?.data?.data.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>

            <Modal
                title={`${editModalVisible === 'add' ? '新建原料' : '编辑原料'}`}
                open={!!editModalVisible}
                onCancel={() => {
                    editForm.resetFields();
                    setEditModalVisible(false);
                }}
                onOk={async () => {
                    await editForm.validateFields();
                    const results = await editForm.validateFields();
                    console.log(results);
                    editModalVisible === 'add'
                        ? addRawMaterial.mutate({
                              materialName: results?.materialName,
                              productIds: results?.fromProduct,
                              supplierIds: results?.suppliers
                          })
                        : editRawMaterial.mutate({
                              materialName: results?.materialName,
                              productIds: results?.fromProduct,
                              supplierIds: results?.suppliers,
                              id: editRawId
                          });
                    editForm.resetFields();
                    setEditModalVisible(false);
                }}
            >
                <Form form={editForm} labelCol={{ span: 5 }} className='edit-label-title'>
                    <Form.Item
                        label='原料名称'
                        name='materialName'
                        rules={[
                            {
                                required: true,
                                message: '请输入原料名称'
                            },
                            {
                                max: 50,
                                message: '请保持字符在50字符以内!'
                            }
                        ]}
                    >
                        <Input placeholder='请输入' disabled={editModalVisible !== 'add'} />
                    </Form.Item>
                    <Form.Item
                        label='所属产品'
                        name='fromProduct'
                        rules={[
                            {
                                required: true,
                                message: '请输入所属产品'
                            }
                        ]}
                    >
                        <Select
                            mode='multiple'
                            style={{ width: '100%' }}
                            showArrow
                            showSearch={false}
                            placeholder='请选择'
                            options={productSelectListQuery?.data?.data?.map((item: any) => {
                                return {
                                    label: item?.productName,
                                    value: item?.id
                                };
                            })}
                        />
                    </Form.Item>
                    <Form.Item label='供应商' name='suppliers'>
                        <Select
                            mode='multiple'
                            placeholder='请选择'
                            style={{ width: '100%' }}
                            showArrow
                            showSearch={false}
                            options={supplierSelectListQuery?.data?.data?.map((item: any) => {
                                return {
                                    label: item?.shortName,
                                    value: item?.supplierId
                                };
                            })}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};

export default WithPaginate(SourceList);

import { useRef, useState } from 'react';
import { Col, Form, message, Row, Space, Badge, InputNumber, Card } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQuery } from 'react-query';
import dayjs from 'dayjs';

import WithPaginate from '../../hoc/withpaginate';
import BaseCard from '@components/base-card';
import BaseModal from '@components/base-modal';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';
import BaseInput from '@components/base-input/base-input';
import BaseSelect from '@components/base-select/base-select';
import BaseDatePicker from '@components/base-date-picker';

import { DownOutlined, UpOutlined } from '@ant-design/icons';

import styles from './index.module.less';
import { cancelLogistic, logisticsPage } from '@services/logistics';
import { ReformChainError } from '@utils/errorCodeReform';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

interface IUrlState {
    pageIndex: number;
    pageSize: number;
    // ...
}

const LogisticsManage = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;

    const querylist: any = useRef('');

    const [visible, setVisible] = useState(false);
    const [queryParams, setQueryParams] = useState<Partial<IUrlState>>({ pageSize: 10, pageIndex: 1 });

    const [isSimpleSearch, setIsSimpleSearch] = useState(true);

    const [form] = Form.useForm();
    const navigate = useNavigate();

    const logicticsListQuery = useQuery(
        ['logicticsListQuery', queryParams],
        () =>
            logisticsPage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                transNumber: querylist?.current?.transNumber?.trim() || undefined,
                type: querylist?.current?.type,
                state: querylist?.current?.state,
                beginTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[0]).startOf('day')).format()
                    : undefined,
                endTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[1]).endOf('day')).format()
                    : undefined
            }),
        {
            onSuccess() {},
            onError(err) {
                ReformChainError(err);
            }
        }
    );

    //作废
    const cancel = useMutation(cancelLogistic, {
        onSuccess(res) {
            message.success('作废成功');
            logicticsListQuery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            logicticsListQuery.refetch();
        }
    });

    console.log(logicticsListQuery, 'queryInspect queryInspect');

    const columns: ColumnsType<any> = [
        {
            title: '运输单号',
            dataIndex: 'transNumber',
            key: 'transNumber',
            ellipsis: true
        },
        {
            title: '运输类型',
            dataIndex: 'type',
            key: 'type',
            ellipsis: true,
            render: (type) => {
                return type === 1 ? '自行运输' : '委托运输';
            }
        },
        {
            title: '操作人',
            dataIndex: 'optName',
            key: 'optName',
            ellipsis: true
        },

        {
            title: '创建时间',
            dataIndex: 'createTime	',
            key: 'createTime	',
            ellipsis: true,
            render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
        },
        {
            title: '状态',
            dataIndex: 'state',
            key: 'state',
            ellipsis: true,
            render: (_, row, index) => {
                switch (row?.state) {
                    case 0:
                        return <Badge color='#1890FF' text='运输中' />;

                    case 1:
                        return <Badge status='success' text='已完成' />;

                    case 2:
                        return <Badge color='#BFBFBF' text='已作废' />;

                    default:
                        break;
                }
            }
        },

        {
            title: '操作',
            width: 200,
            render: (_, row, index) => (
                <Space>
                    <BaseButton
                        danger
                        disabled={row?.state === 2}
                        onClick={() => {
                            cancel.mutate({
                                id: row?.id
                            });
                        }}
                    >
                        作废
                    </BaseButton>
                    <BaseButton
                        style={{ borderColor: 'rgba(61, 115, 239, 1)', color: 'rgba(61, 115, 239, 1)' }}
                        onClick={() => {
                            navigate('detail', { state: row });
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    const searchFormItems = [
        <Form.Item label='运输单' name='transNumber'>
            <BaseInput placeholder='请输入运输单号'></BaseInput>
        </Form.Item>,
        <Form.Item label='运输类型' name='type'>
            <BaseSelect
                placeholder='请选择'
                options={[
                    {
                        label: '自行运输',
                        value: 1
                    },
                    {
                        label: '委托运输',
                        value: 2
                    }
                ]}
            ></BaseSelect>
        </Form.Item>,
        <Form.Item label='创建时间' name='createTime'>
            <BaseDatePicker style={{ width: '100%' }}></BaseDatePicker>
        </Form.Item>,
        <Form.Item label='状态' name='state'>
            <BaseSelect
                placeholder='请选择'
                options={[
                    {
                        label: '运输中',
                        value: 0
                    },
                    {
                        label: '已完成',
                        value: 1
                    },
                    {
                        label: '已作废',
                        value: 2
                    }
                ]}
            ></BaseSelect>
        </Form.Item>
    ];

    return (
        <>
            <Card style={{ marginBottom: 10 }} title={<PageTitle title='运输单列表' />}>
                <Form
                    form={form}
                    labelAlign='left'
                    labelCol={{ span: 5 }}
                    onFinish={(values) => {
                        handlePaginationChange(1);
                        console.log('values', values);
                        querylist.current = values;
                        logicticsListQuery.refetch();
                    }}
                    className='label-title'
                >
                    <Row gutter={[36, 12]}>
                        {searchFormItems.slice(0, isSimpleSearch ? 2 : searchFormItems.length).map((searchFormItem) => (
                            <Col key={searchFormItem.key} span={8}>
                                {searchFormItem}
                            </Col>
                        ))}
                        <Col span={isSimpleSearch ? 8 : 16}>
                            <div style={{ display: 'flex', justifyContent: 'end' }}>
                                <Space>
                                    <BaseButton type='primary' htmlType='submit'>
                                        查询
                                    </BaseButton>
                                    <BaseButton
                                        onClick={() => {
                                            querylist.current = null;
                                            logicticsListQuery.refetch();
                                            form.resetFields();
                                        }}
                                    >
                                        重置
                                    </BaseButton>
                                    <BaseButton
                                        style={{ color: '#80a932' }}
                                        type='link'
                                        onClick={() => {
                                            setIsSimpleSearch(!isSimpleSearch);
                                        }}
                                    >
                                        {isSimpleSearch ? '展开' : '收起'}
                                        {isSimpleSearch ? (
                                            <DownOutlined rev={undefined} />
                                        ) : (
                                            <UpOutlined rev={undefined} />
                                        )}
                                    </BaseButton>
                                </Space>
                            </div>
                        </Col>
                    </Row>
                </Form>
            </Card>
            <BaseCard className={styles.coreFIrmContainer}>
                <BaseTable
                    loading={logicticsListQuery.isFetching}
                    columns={columns}
                    dataSource={logicticsListQuery?.data?.data?.records || []}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={logicticsListQuery?.data?.data?.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>
        </>
    );
};

export default WithPaginate(LogisticsManage);

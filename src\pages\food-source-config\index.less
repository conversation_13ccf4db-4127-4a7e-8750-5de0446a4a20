.sourceConfigContainer {
    background: #fff;
    .base-card .ant-card {
        background: #fff;
        .ant-list {
            background: #fbfbfd;
        }
    }
    .ant-collapse-header {
        background: none;
        margin-top: 16;
    }
    .ant-collapse {
        background: none;
        border: none;
        .ant-collapse-item {
            box-sizing: border-box;
            border: 1px solid #d9d9d9;
            border-radius: 5px;
            margin-bottom: 16px;
            .ant-collapse-content {
                border-radius: 5px;
            }
        }
    }
    .switch-card {
        border: 1px solid #f0f0f0 !important;
    }
    .saveBtn {
        margin-right: 24px;
    }
    .addBtnContainer {
        .flexContent(row, left);
        .ant-form-item {
            margin-bottom: 5px;
        }
    }
    .submitBtn {
        margin-top: 10px;
        // margin-left: 30px;
        display: inline-block;
        width: 100px;
    }
    .primaryBtn {
        margin-top: 10px;
        display: inline-block;
        width: 100px;
    }
    .ant-collapse-extra {
        width: 100%;
        // text-align: center; */
        margin-left: 36% !important;
    }
    .label-title{
        .ant-col-7{
            flex: 0 0 36%;
            max-width: 36%;
        }
    }
}

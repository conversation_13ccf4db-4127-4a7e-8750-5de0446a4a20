import BaseCard from '@components/base-card';
import BaseFormItem from '@components/base-form-item';
import PageTitle from '@components/page-title';
import { Form, message, Image } from 'antd';
import { ReformChainError } from '@utils/errorCodeReform';

import { qualityTestDetail } from '@services/quality-test';
import { useMutation, useQuery } from 'react-query';
import { useLocation } from 'react-router-dom';
import dayjs from 'dayjs';

import React, { useState } from 'react';
import { QcqaInfoConfig, chainInfoConfig } from './config';
import FilterForm from '@components/filter-form/filter-form';
import ChainDetailModal from '@components/chain_detail_modal';
import { decryptedUrl } from '@utils';
// import './index.less'

function QcqaDetail() {
    const { state } = useLocation();
    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    const [inspectionAccessory, setInspectionAccessory] = useState<any>();
    const [inspectionReport, setInspectionReport] = useState<any>();

    console.log('state', state);
    //查看详情
    const qualityTestquery = useQuery(
        ['qualityTestquery'],
        () => {
            return qualityTestDetail({
                qualityId: state.data.id
            });
        },
        {
            async onSuccess(res: any) {
                const inspectionAccessory = await decryptedUrl(res?.data?.inspectionAccessory);
                const inspectionReport = await decryptedUrl(res?.data?.inspectionReport);
                setInspectionAccessory(inspectionAccessory);
                setInspectionReport(inspectionReport);
                QcqaForm.setFieldsValue({
                    ...res?.data,
                    inspectionAccessory,
                    inspectionReport
                });
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    const qualityTestdata = qualityTestquery?.data?.data;
    const [QcqaForm] = Form.useForm();
    const [ChainForm] = Form.useForm();

    const valuesForm = QcqaForm.getFieldsValue();
    console.log('SourceCodeForm', QcqaForm, valuesForm);

    const chainInfoConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Custom',
            children: qualityTestdata?.transactionId ? (
                <a onClick={() => setChainDetailModalVisible(true)}>{qualityTestdata?.transactionId}</a>
            ) : (
                '-'
            )
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];
    const QcqaInfoConfig = [
        {
            label: '产品名称',
            name: 'productName',
            value: 'productName',
            type: 'ShowText',
            span: 8
        },
        {
            label: '生产批次',
            name: 'productionBatch',
            value: 'productionBatch',
            type: 'ShowText',
            span: 8,
            displayDom: qualityTestdata?.productionBatch
        },
        {
            label: '质检机构',
            name: 'inspectionOrgName',
            value: 'inspectionOrgName',
            type: 'ShowText',
            span: 8
        },
        {
            label: '质检内容',
            name: 'inspectionContent',
            value: 'inspectionContent',
            type: 'ShowText',
            span: 8
        },
        {
            label: '质检结果',
            name: 'inspectionResults',
            value: 'inspectionResults',
            type: 'Status',
            span: 8,
            display: (
                <h3 className={qualityTestdata?.inspectionResults !== 2 ? 'SuccessStatus' : 'ErrorStatus'}>
                    {qualityTestdata?.inspectionResults !== 2 ? '合格' : '不合格'}
                </h3>
            )
        },
        {
            label: '质检报告',
            name: 'inspectionReport',
            value: 'inspectionReport',
            type: 'Custom',
            span: 8,
            children: <Image width={70} src={inspectionReport}></Image>
        },
        {
            label: '附件',
            name: 'inspectionAccessory',
            value: '123',
            type: 'Custom',
            span: 8,
            children: qualityTestdata?.inspectionAccessory ? <a href={inspectionAccessory}>{'下载'}</a> : '-'
        }
    ];
    ChainForm.setFieldsValue({
        blockNum: qualityTestdata?.blockNum,
        transactionId: qualityTestdata?.transactionId,
        transactionTime: qualityTestdata?.transactionTime
            ? dayjs(qualityTestdata?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
            : null
    });
    return (
        <>
            <BaseCard title={<PageTitle title='质检信息详情' bg='container zhijian' />}>
                <Form form={QcqaForm} className='custom-form-item-height'>
                    <PageTitle title='质检信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={QcqaInfoConfig} labelCol={false} />
                </Form>
                <Form form={ChainForm}>
                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={chainInfoConfig} labelCol={false} />
                </Form>
            </BaseCard>
            <ChainDetailModal
                transactionId={qualityTestdata?.transactionId}
                open={ChainDetailModalVisible}
                onCancel={() => setChainDetailModalVisible(false)}
            />
        </>
    );
}

export default QcqaDetail;

import React, { useState } from 'react';
import {
    Col,
    Form,
    message,
    Row,
    Space,
    Descriptions as AntdDescriptions,
    Collapse,
    Image,
    ConfigProvider,
    Divider,
    Table
} from 'antd';
import { ColumnsType } from 'antd/lib/table';
import zhCN from 'antd/lib/locale/zh_CN';
import { TableLocale } from 'antd/lib/table/interface';
import { useNavigate, useLocation } from 'react-router-dom';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';

import BaseCard from '@components/base-card';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseTooltip from '@components/base-tooltip';
import ChainDetailModal from '@components/chain_detail_modal';

import styles from './index.module.less';
import './index.less';
import { traceCodeCheckTimes, traceCodeDetail } from '@services/trace-source-code';
import { ReformChainError } from '@utils/errorCodeReform';
import BaseModal from '@components/base-modal';
import { getTransportationType } from '@pages/logistics-data-detail';
import { decryptedUrl, isArrayArr } from '@utils';

const Descriptions = (props: any) => {
    const { children, ...restProps } = props;
    const newChildren = children.map((descriptionsItem: any) => {
        if (!descriptionsItem) {
            return descriptionsItem;
        }
        if (typeof descriptionsItem.props.children === 'string') {
            return React.cloneElement(descriptionsItem, {
                ...descriptionsItem.props,
                children: (
                    <BaseTooltip
                        preStyle={{ lineHeight: 'inherit', whiteSpace: 'nowrap' }}
                        data={descriptionsItem.props.children}
                    ></BaseTooltip>
                )
            });
        }
        return descriptionsItem;
    });
    return <AntdDescriptions {...restProps}>{newChildren}</AntdDescriptions>;
};
Descriptions.Item = AntdDescriptions.Item;

const FleeWarning = () => {
    const { state } = useLocation();
    const navigate = useNavigate();
    const { Panel } = Collapse;

    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    const [recordModalVisible, setRecordModalVisible] = useState(false);
    const [transId, setTransId] = useState('');
    const [pageIndex, setPageIndex] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [productImg, setProductImg] = useState<any>();
    const [productVideo, setProductVideo] = useState<any>();
    const [placeAptitude, setPlaceAptitude] = useState<any>();

    const [processDecImg, setProcessDecImg] = useState<any>([]);
    const [processDecVideo, setProcessDecVideo] = useState<any>([]);

    const [placeImg, setPlaceImg] = useState<any>();
    const [placeVideo, setPlaceVideo] = useState<any>();
    const [inspectionReport, setInspectionReport] = useState<any>();

    const [qualification, setQualification] = useState<any>();
    const [picture, setPicture] = useState<any>();
    const [video, setVideo] = useState<any>();

    const errImage =
        'data:image/png;base64,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';

    const traceCodeDetailQuery: any = useQuery(
        ['traceCodeDetailQuery'],
        () =>
            traceCodeDetail({
                id: state.id
            }),
        {
            async onSuccess(res: any) {
                // const materiCertificateQualification= await decryptedUrl(res?.data?.materialInfo?.certificateQualification)

                const productImg = await Promise.all(
                    isArrayArr(res?.data?.productBrief?.productImg)?.map((item: any) => {
                        return decryptedUrl(item);
                    })
                );
                setProductImg(productImg);
                const productVideo = await decryptedUrl(res?.data?.productBrief?.productVideo);
                setProductVideo(productVideo);

                const placeAptitude = await decryptedUrl(res?.data?.placeBasic?.placeAptitude);
                setPlaceAptitude(placeAptitude);
                const placeImg = await Promise.all(
                    isArrayArr(res?.data?.placeBrief?.placeImg)?.map((item: any) => {
                        return decryptedUrl(item);
                    })
                );
                setPlaceImg(placeImg);
                const placeVideo = await decryptedUrl(res?.data?.placeBrief?.placeVideo);
                setPlaceVideo(placeVideo);
                const arrayDataProcessVideo = await Promise.all(
                    isArrayArr(res?.data?.process).map((item: any) => {
                        return decryptedUrl(item.processVideo);
                    })
                );
                const arrayDataProcessImg = await Promise.all(
                    isArrayArr(res?.data?.process)?.map(async (item: any) => {
                        let arr = [];
                        if (Array.isArray(item?.processImg) && item?.processImg?.length > 0) {
                            const itemArr = await Promise.all(
                                isArrayArr(item?.processImg)?.map((val: string) => decryptedUrl(val))
                            );
                            arr.push(...itemArr);
                        }
                        return arr;
                    })
                );
                console.log('arrayDataProcessImg', arrayDataProcessImg);
                setProcessDecImg(arrayDataProcessImg);
                setProcessDecVideo(arrayDataProcessVideo);
                const inspectionReport = await Promise.all(
                    isArrayArr(res?.data?.inspection)?.map((item: any) => {
                        return (item['inspectionReport'] =
                            item['inspectionReport'] === '空'
                                ? item['inspectionReport']
                                : item['inspectionReport']
                                ? decryptedUrl(item.inspectionReport)
                                : item['inspectionReport']);
                    })
                );
                setInspectionReport(inspectionReport);
                const qualification = await decryptedUrl(res?.data?.orgBasic?.qualification);
                setQualification(qualification);

                const picture = await Promise.all(
                    isArrayArr(res?.data?.orgBrief?.picture)?.map((item: any) => {
                        return decryptedUrl(item);
                    })
                );
                setPicture(picture);
                const video = await decryptedUrl(res?.data?.orgBrief?.video);
                setVideo(video);
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    const detailData = traceCodeDetailQuery?.data?.data;
    const traceCodeCheckTimesQuery = useQuery(
        ['traceCodeCheckTimesQuery', pageIndex, pageSize],
        () =>
            traceCodeCheckTimes({
                codeId: state.id,
                pageIndex: pageIndex,
                pageSize: pageSize
            }),
        {
            onError(err: any) {
                ReformChainError(err);
            },
            enabled: recordModalVisible
        }
    );

    const recordModalConfig = {
        className: 'checkRecords',
        title: '查询记录',
        visible: recordModalVisible,
        setVisible: setRecordModalVisible,
        getContainer: false as any,
        footer: null,
        okHandle: async () => {
            setRecordModalVisible(false);
            setPageIndex(1);
            setPageSize(10);
        },
        onCancelHandle: () => {
            setRecordModalVisible(false);
            setPageIndex(1);
            setPageSize(10);
        }
    };

    const columns = [
        {
            title: '',
            dataIndex: '',
            render: () => {
                return <div>查询时间</div>;
            }
        },
        {
            title: '',
            dataIndex: 'time',
            render: (_: any, data: any) => {
                return <div>{dayjs(data.time).format('YYYY-MM-DD HH:mm:ss')}</div>;
            }
        }
    ];
    const unit = detailData?.product?.specificationUnit == '1' ? '克（g）' : '千克（kg）';
    return (
        <BaseCard title={<PageTitle title='溯源码详情' bg='container su' />} className='sourceContainer'>
            <Collapse>
                <Panel header='溯源码详情' key='1'>
                    <Descriptions>
                        <Descriptions.Item label='溯源码'>{detailData?.code || '-'}</Descriptions.Item>
                        <Descriptions.Item label='生产批次'>{detailData?.productionBatch || '-'}</Descriptions.Item>
                        <Descriptions.Item label='所属码包'>{detailData?.packNumber || '-'}</Descriptions.Item>
                        <Descriptions.Item label='生码时间'>
                            {detailData?.createTime != '空'
                                ? dayjs(detailData?.createTime).format('YYYY-MM-DD HH:mm:ss')
                                : '-'}
                        </Descriptions.Item>
                        <Descriptions.Item label='状态'>{detailData?.state ? '禁用' : '启用'}</Descriptions.Item>
                        <Descriptions.Item label='查询次数'>{detailData?.searchCount || 0}</Descriptions.Item>
                        <Descriptions.Item label='查询记录'>
                            <a
                                onClick={() => {
                                    setRecordModalVisible(true);
                                }}
                            >
                                点击查看
                            </a>
                        </Descriptions.Item>
                    </Descriptions>
                </Panel>
                {detailData?.product && (
                    <Panel header='产品详情' key='2m ""'>
                        {Object.keys(detailData?.product).length !== 0 ? (
                            <Descriptions>
                                {detailData?.product?.productName && (
                                    <Descriptions.Item label='产品名称'>
                                        {detailData?.product?.productName || '-'}
                                    </Descriptions.Item>
                                )}
                                {detailData?.product?.productCategory && (
                                    <Descriptions.Item label='产品品类'>
                                        {detailData?.product?.productCategory || '-'}
                                    </Descriptions.Item>
                                )}
                                {detailData?.product?.expirationDate && (
                                    <Descriptions.Item label='保质期'>
                                        {detailData?.product?.expirationDate || '-'}
                                    </Descriptions.Item>
                                )}
                                {/* {detailData?.product?.productCode && (
                                    <Descriptions.Item label='产品编码'>
                                        {detailData?.product?.productCode || '-'}
                                    </Descriptions.Item>
                                )} */}
                                {detailData?.product?.executiveStandard && (
                                    <Descriptions.Item label='产品执行标准'>
                                        {detailData?.product?.executiveStandard || '-'}
                                    </Descriptions.Item>
                                )}
                                {detailData?.product?.productionLicense && (
                                    <Descriptions.Item label='食品生产许可证编号'>
                                        {detailData?.product?.productionLicense || '-'}
                                    </Descriptions.Item>
                                )}
                                {detailData?.product?.specification && (
                                    <Descriptions.Item label='产品规格'>
                                        {detailData?.product?.specification == '空'
                                            ? '-'
                                            : detailData?.product?.specification + unit}
                                    </Descriptions.Item>
                                )}
                                {detailData?.product?.ingredient && (
                                    <Descriptions.Item label='配料'>
                                        {detailData?.product?.ingredient || '-'}
                                    </Descriptions.Item>
                                )}
                                {/* {detailData?.product?.productAptitude && (
                                    <Descriptions.Item label='产品合格证明'>
                                        {detailData?.product?.productAptitude === '空' ? (
                                            '空'
                                        ) : (
                                            <Image
                                                preview={true}
                                                width={70}
                                                height={50}
                                                src={detailData?.product?.productAptitude}
                                                fallback={errImage}
                                            />
                                        )}
                                    </Descriptions.Item>
                                )} */}
                                {detailData?.product?.productTransId && (
                                    <Descriptions.Item label='链上哈希' className='ellipsis-text'>
                                        {detailData?.product?.productTransId ? (
                                            <a
                                                onClick={() => {
                                                    setTransId(detailData?.product?.productTransId);
                                                    setChainDetailModalVisible(true);
                                                }}
                                                title={detailData?.product?.productTransId}
                                            >
                                                {detailData?.product?.productTransId}
                                            </a>
                                        ) : (
                                            '-'
                                        )}
                                    </Descriptions.Item>
                                )}
                                {detailData?.product?.productTransTime && (
                                    <Descriptions.Item label='上链时间'>
                                        {detailData?.product?.productTransTime != '空'
                                            ? dayjs(detailData?.product?.productTransTime).format('YYYY-MM-DD HH:mm:ss')
                                            : '-'}
                                    </Descriptions.Item>
                                )}
                            </Descriptions>
                        ) : (
                            <div style={{ color: 'red' }}>该详情尚未填写</div>
                        )}
                    </Panel>
                )}
                {detailData?.productBrief && (
                    <Panel header='产品简介' key='3'>
                        {Object.keys(detailData?.productBrief).length !== 0 ? (
                            <Descriptions>
                                {detailData?.productBrief?.productIntro && (
                                    <Descriptions.Item label='产品介绍'>
                                        {detailData?.productBrief?.productIntro}
                                        {/* <pre className='showtext-item'>{detailData?.productBrief?.productIntro}</pre> */}
                                    </Descriptions.Item>
                                )}
                                {detailData?.productBrief?.productImg && productImg && (
                                    <Descriptions.Item label='宣传图片'>
                                        {detailData?.productBrief?.productImg?.includes('空') ? (
                                            '空'
                                        ) : (
                                            <Image.PreviewGroup>
                                                <Space>
                                                    {productImg?.map((item: any) => {
                                                        return (
                                                            <Image
                                                                preview={true}
                                                                width={70}
                                                                height={50}
                                                                src={item}
                                                                fallback={errImage}
                                                            />
                                                        );
                                                    })}
                                                </Space>
                                            </Image.PreviewGroup>
                                        )}
                                    </Descriptions.Item>
                                )}
                                {detailData?.productBrief?.productVideo && productVideo && (
                                    <Descriptions.Item label='宣传视频'>
                                        {detailData?.productBrief?.productVideo === '空' ? (
                                            ' 空'
                                        ) : (
                                            <video src={productVideo} width={150} controls></video>
                                        )}
                                    </Descriptions.Item>
                                )}
                            </Descriptions>
                        ) : (
                            <div style={{ color: 'red' }}>该详情尚未填写</div>
                        )}
                    </Panel>
                )}
                {detailData?.placeBasic && (
                    <Panel header='产地基础信息' key='4'>
                        {Object.keys(detailData?.placeBasic).length !== 0 ? (
                            <Descriptions>
                                {detailData?.placeBasic?.placeName && (
                                    <Descriptions.Item label='产地名称'>
                                        {detailData?.placeBasic?.placeName}
                                    </Descriptions.Item>
                                )}
                                {detailData?.placeBasic?.placeAddress && (
                                    <Descriptions.Item label='产地地址'>
                                        {detailData?.placeBasic?.placeAddress}
                                    </Descriptions.Item>
                                )}
                                {detailData?.placeBasic?.placeAptitude && placeAptitude && (
                                    <Descriptions.Item label='产地位置图'>
                                        {detailData?.placeBasic?.placeAptitude === '空' ? (
                                            '空'
                                        ) : (
                                            <Image
                                                preview={true}
                                                width={70}
                                                height={50}
                                                src={placeAptitude}
                                                fallback={errImage}
                                            />
                                        )}
                                    </Descriptions.Item>
                                )}
                                {detailData?.placeBasic?.placeTransId && (
                                    <Descriptions.Item label='链上哈希' className='ellipsis-text'>
                                        {detailData?.placeBasic?.placeTransId ? (
                                            <a
                                                onClick={() => {
                                                    setTransId(detailData?.placeBasic?.placeTransId);
                                                    setChainDetailModalVisible(true);
                                                }}
                                                title={detailData?.placeBasic?.placeTransId}
                                            >
                                                {detailData?.placeBasic?.placeTransId}
                                            </a>
                                        ) : (
                                            '-'
                                        )}
                                    </Descriptions.Item>
                                )}
                                {detailData?.placeBasic?.placeTransTime && (
                                    <Descriptions.Item label='上链时间'>
                                        {detailData?.placeBasic?.placeTransTime != '空'
                                            ? dayjs(detailData?.placeBasic?.placeTransTime).format(
                                                  'YYYY-MM-DD HH:mm:ss'
                                              )
                                            : '-'}
                                    </Descriptions.Item>
                                )}
                            </Descriptions>
                        ) : (
                            <div style={{ color: 'red' }}>该详情尚未填写</div>
                        )}
                    </Panel>
                )}
                {detailData?.placeBrief && (
                    <Panel header='产地简介' key='5'>
                        {Object.keys(detailData?.placeBrief).length !== 0 ? (
                            <Descriptions>
                                {detailData?.placeBrief?.placeIntro && (
                                    <Descriptions.Item label='产地介绍'>
                                        {detailData?.placeBrief?.placeIntro}
                                        {/* <pre className='showtext-item'>{detailData?.placeBrief?.placeIntro}</pre> */}
                                    </Descriptions.Item>
                                )}
                                {detailData?.placeBrief?.placeImg && placeImg && (
                                    <Descriptions.Item label='产地图片'>
                                        {detailData?.placeBrief?.placeImg?.includes('空') ? (
                                            '空'
                                        ) : (
                                            <Image.PreviewGroup>
                                                <Space>
                                                    {placeImg?.map((item: any) => {
                                                        return (
                                                            <Image
                                                                preview={true}
                                                                width={70}
                                                                height={50}
                                                                src={item}
                                                                fallback={errImage}
                                                            />
                                                        );
                                                    })}
                                                </Space>
                                            </Image.PreviewGroup>
                                        )}
                                    </Descriptions.Item>
                                )}
                                {detailData?.placeBrief?.placeVideo && placeVideo && (
                                    <Descriptions.Item label='产地视频'>
                                        {detailData?.placeBrief?.placeVideo === '空' ? (
                                            '空'
                                        ) : (
                                            <video src={placeVideo} width={150} controls></video>
                                        )}
                                    </Descriptions.Item>
                                )}
                            </Descriptions>
                        ) : (
                            <div style={{ color: 'red' }}>该详情尚未填写</div>
                        )}
                    </Panel>
                )}
                {detailData?.material && (
                    <Panel header='原料详情' key={`6`}>
                        {Object.keys(detailData?.material).length !== 0 ? (
                            detailData?.material?.map((item: any, index: number) => {
                                return (
                                    <div>
                                        <Descriptions>
                                            {item?.materialName && (
                                                <Descriptions.Item label='原料名称'>
                                                    {item?.materialName}
                                                </Descriptions.Item>
                                            )}
                                            {item?.purchaseBatch && (
                                                <Descriptions.Item label='原料采购批次'>
                                                    {item?.purchaseBatch}
                                                </Descriptions.Item>
                                            )}
                                            {item?.productionDate && (
                                                <Descriptions.Item label='生产日期'>
                                                    {item?.productionDate === '空'
                                                        ? '空'
                                                        : dayjs(item?.productionDate).format('YYYY-MM-DD HH:mm:ss')}
                                                </Descriptions.Item>
                                            )}
                                            {item?.expiration && (
                                                <Descriptions.Item label='保质期'>{item?.expiration}</Descriptions.Item>
                                            )}
                                            {item?.count && (
                                                <Descriptions.Item label='数量'>{item?.count}</Descriptions.Item>
                                            )}
                                            {item?.specification && (
                                                <Descriptions.Item label='规格'>
                                                    {item?.specification}
                                                </Descriptions.Item>
                                            )}
                                            {item?.certificate && (
                                                <Descriptions.Item label='原料合格证明'>
                                                    {item?.certificate === '空' ? (
                                                        '空'
                                                    ) : (
                                                        <Image
                                                            preview={true}
                                                            width={70}
                                                            height={50}
                                                            src={item?.certificate}
                                                            fallback={errImage}
                                                        />
                                                    )}
                                                </Descriptions.Item>
                                            )}
                                            {item?.materialImg && (
                                                <Descriptions.Item label='原料图片'>
                                                    {item?.materialImg === '空' ? (
                                                        '空'
                                                    ) : (
                                                        <Image
                                                            preview={true}
                                                            width={70}
                                                            height={50}
                                                            src={item?.materialImg}
                                                            fallback={errImage}
                                                        />
                                                    )}
                                                </Descriptions.Item>
                                            )}
                                            {item?.supplier && (
                                                <Descriptions.Item label='供应商'>{item?.supplier}</Descriptions.Item>
                                            )}
                                            {item?.purchaseTransId && (
                                                <Descriptions.Item label='链上哈希' className='ellipsis-text'>
                                                    <a
                                                        onClick={() => {
                                                            setTransId(item?.purchaseTransId);
                                                            setChainDetailModalVisible(true);
                                                        }}
                                                        title={item?.purchaseTransId}
                                                    >
                                                        {item?.purchaseTransId}
                                                    </a>
                                                </Descriptions.Item>
                                            )}
                                            {item?.purchaseTransTime && (
                                                <Descriptions.Item label='上链时间'>
                                                    {dayjs(item?.purchaseTransTime).format('YYYY-MM-DD HH:mm:ss')}
                                                </Descriptions.Item>
                                            )}
                                        </Descriptions>
                                        {detailData?.material?.length === 1 ? null : <Divider />}
                                    </div>
                                );
                            })
                        ) : (
                            <div style={{ color: 'red' }}>该详情尚未填写</div>
                        )}
                    </Panel>
                )}
                {detailData?.process && (
                    <Panel header='生产过程' key={`7`}>
                        {Object.keys(detailData?.process).length !== 0 ? (
                            detailData?.process?.map((item: any, index: number) => {
                                return (
                                    <div>
                                        <Descriptions>
                                            {item?.processName && (
                                                <Descriptions.Item label='生产过程名称'>
                                                    {item?.processName}
                                                </Descriptions.Item>
                                            )}
                                            {item?.processInstructions && (
                                                <Descriptions.Item label='生产过程说明'>
                                                    {item?.processInstructions}
                                                </Descriptions.Item>
                                            )}
                                            {item?.processImg &&
                                                processDecImg &&
                                                processDecImg[index] &&
                                                processDecImg[index].length > 0 && (
                                                    <Descriptions.Item label='生产过程图片'>
                                                        {item?.processImg?.includes('空') ? (
                                                            '空'
                                                        ) : (
                                                            <Image.PreviewGroup>
                                                                <Space>
                                                                    {processDecImg[index]?.map((item: any) => {
                                                                        return (
                                                                            <Image
                                                                                preview={true}
                                                                                width={70}
                                                                                height={50}
                                                                                src={item}
                                                                                fallback={errImage}
                                                                            />
                                                                        );
                                                                    })}
                                                                </Space>
                                                            </Image.PreviewGroup>
                                                        )}
                                                    </Descriptions.Item>
                                                )}
                                            {item?.processVideo &&
                                                processDecVideo &&
                                                processDecVideo[index] &&
                                                processDecVideo[index].length > 0 && (
                                                    <Descriptions.Item label='生产过程视频'>
                                                        {item?.processVideo === '空' ? (
                                                            '空'
                                                        ) : (
                                                            <video
                                                                src={processDecVideo[index]}
                                                                width={150}
                                                                controls
                                                            ></video>
                                                        )}
                                                    </Descriptions.Item>
                                                )}
                                            {item?.processTransId && (
                                                <Descriptions.Item label='链上哈希' className='ellipsis-text'>
                                                    <a
                                                        onClick={() => {
                                                            setTransId(item?.processTransId);
                                                            setChainDetailModalVisible(true);
                                                        }}
                                                        title={item?.processTransId}
                                                    >
                                                        {item?.processTransId}
                                                    </a>
                                                </Descriptions.Item>
                                            )}
                                            {item?.processTransTime && (
                                                <Descriptions.Item label='上链时间'>
                                                    {dayjs(item?.processTransTime).format('YYYY-MM-DD HH:mm:ss')}
                                                </Descriptions.Item>
                                            )}
                                        </Descriptions>
                                        {detailData?.process?.length === 1 ? null : <Divider />}
                                    </div>
                                );
                            })
                        ) : (
                            <div style={{ color: 'red' }}>该详情尚未填写</div>
                        )}
                    </Panel>
                )}
                {detailData?.production && (
                    <Panel header='生产加工详情' key='8'>
                        {Object.keys(detailData?.production).length !== 0 ? (
                            <Descriptions>
                                {detailData?.production?.productionBatch && (
                                    <Descriptions.Item label='批次号'>
                                        {detailData?.production?.productionBatch}
                                    </Descriptions.Item>
                                )}
                                {detailData?.production?.amount && (
                                    <Descriptions.Item label='数量'>{detailData?.production?.amount}</Descriptions.Item>
                                )}
                                {detailData?.production?.line && (
                                    <Descriptions.Item label='生产线'>{detailData?.production?.line}</Descriptions.Item>
                                )}
                                {detailData?.production?.grower && (
                                    <Descriptions.Item label='种植户'>
                                        {detailData?.production?.grower}
                                    </Descriptions.Item>
                                )}
                                {detailData?.production?.productionTransId && (
                                    <Descriptions.Item label='链上哈希' className='ellipsis-text'>
                                        <a
                                            onClick={() => {
                                                setTransId(detailData?.production?.productionTransId);
                                                setChainDetailModalVisible(true);
                                            }}
                                            title={detailData?.production?.productionTransId}
                                        >
                                            {detailData?.production?.productionTransId}
                                        </a>
                                    </Descriptions.Item>
                                )}
                                {detailData?.production?.productionTransTime && (
                                    <Descriptions.Item label='上链时间'>
                                        {dayjs(detailData?.production?.productionTransTime).format(
                                            'YYYY-MM-DD HH:mm:ss'
                                        )}
                                    </Descriptions.Item>
                                )}
                            </Descriptions>
                        ) : (
                            <div style={{ color: 'red' }}>该详情尚未填写</div>
                        )}
                    </Panel>
                )}
                {detailData?.logistics && (
                    <Panel header='物流详情' key='9'>
                        {Object.keys(detailData?.logistics).length !== 0 ? (
                            <Descriptions>
                                {detailData?.logistics?.loadingLocation && (
                                    <Descriptions.Item label='装货地点'>
                                        {detailData?.logistics?.loadingLocation}
                                    </Descriptions.Item>
                                )}
                                {detailData?.logistics?.unloadingLocation && (
                                    <Descriptions.Item label='卸货地点'>
                                        {detailData?.logistics?.unloadingLocation}
                                    </Descriptions.Item>
                                )}
                                {detailData?.logistics?.transportationType && (
                                    <Descriptions.Item label='运输方式'>
                                        {getTransportationType(Number(detailData?.logistics?.transportationType))}
                                    </Descriptions.Item>
                                )}
                                {detailData?.logistics?.loEnterprises && (
                                    <Descriptions.Item label='物流企业'>
                                        {detailData?.logistics?.loEnterprises}
                                    </Descriptions.Item>
                                )}
                                {detailData?.logistics?.loNumber && (
                                    <Descriptions.Item label='物流单号'>
                                        {detailData?.logistics?.loNumber}
                                    </Descriptions.Item>
                                )}
                                {detailData?.logistics?.logisticsTransId && (
                                    <Descriptions.Item label='链上哈希' className='ellipsis-text'>
                                        <a
                                            onClick={() => {
                                                setTransId(detailData?.logistics?.logisticsTransId);
                                                setChainDetailModalVisible(true);
                                            }}
                                            title={detailData?.logistics?.logisticsTransId}
                                        >
                                            {detailData?.logistics?.logisticsTransId}
                                        </a>
                                    </Descriptions.Item>
                                )}
                                {detailData?.logistics?.logisticsTransTime && (
                                    <Descriptions.Item label='上链时间'>
                                        {dayjs(detailData?.logistics?.logisticsTransTime).format('YYYY-MM-DD HH:mm:ss')}
                                    </Descriptions.Item>
                                )}
                            </Descriptions>
                        ) : (
                            <div style={{ color: 'red' }}>该详情尚未填写</div>
                        )}
                    </Panel>
                )}
                {detailData?.inspection && (
                    <Panel header='质检详情' key={`10`}>
                        {Object.keys(detailData?.inspection).length !== 0 ? (
                            detailData?.inspection?.map((item: any, index: number) => {
                                return (
                                    <div>
                                        <Descriptions>
                                            {item?.inspectionContent && (
                                                <Descriptions.Item label='质检内容'>
                                                    {item?.inspectionContent}
                                                </Descriptions.Item>
                                            )}
                                            {item?.inspectionResults && (
                                                <Descriptions.Item label='质检结果'>
                                                    <span
                                                        style={
                                                            item?.inspectionResults === '1'
                                                                ? { color: '#29bd9c' }
                                                                : { color: 'red' }
                                                        }
                                                    >
                                                        {item?.inspectionResults === '1' ? '合格' : '不合格'}
                                                    </span>
                                                </Descriptions.Item>
                                            )}
                                            {item?.inspectionReport && inspectionReport && inspectionReport.length > 0 && (
                                                <>
                                                    {item?.inspectionReport === '空' ? null : item?.inspectionReport ? (
                                                        <Descriptions.Item label='质检报告'>
                                                            <Image
                                                                preview={true}
                                                                width={70}
                                                                height={50}
                                                                src={inspectionReport[index]}
                                                                fallback={errImage}
                                                            />
                                                        </Descriptions.Item>
                                                    ) : null}
                                                </>
                                            )}
                                            {item?.inspectionInstitution && (
                                                <Descriptions.Item label='质检机构'>
                                                    {item?.inspectionInstitution}
                                                </Descriptions.Item>
                                            )}
                                            {item?.inspectionTransId && (
                                                <Descriptions.Item label='链上哈希' className='ellipsis-text'>
                                                    <a
                                                        onClick={() => {
                                                            setTransId(item?.inspectionTransId);
                                                            setChainDetailModalVisible(true);
                                                        }}
                                                        title={item?.inspectionTransId}
                                                    >
                                                        {item?.inspectionTransId}
                                                    </a>
                                                </Descriptions.Item>
                                            )}
                                            {item?.inspectionTransTime && (
                                                <Descriptions.Item label='上链时间'>
                                                    {dayjs(item?.inspectionTransTime).format('YYYY-MM-DD HH:mm:ss')}
                                                </Descriptions.Item>
                                            )}
                                        </Descriptions>
                                        {detailData?.inspection?.length === 1 ? null : <Divider />}
                                    </div>
                                );
                            })
                        ) : (
                            <div style={{ color: 'red' }}>该详情尚未填写</div>
                        )}
                    </Panel>
                )}
                {detailData?.orgBasic && (
                    <Panel header='企业基础信息' key='11'>
                        {Object.keys(detailData?.orgBasic).length !== 0 ? (
                            <Descriptions>
                                {detailData?.orgBasic?.companyName && (
                                    <Descriptions.Item label='企业名称'>
                                        {detailData?.orgBasic?.companyName}
                                    </Descriptions.Item>
                                )}
                                {detailData?.orgBasic?.address && (
                                    <Descriptions.Item label='企业地址'>
                                        {detailData?.orgBasic?.address}
                                    </Descriptions.Item>
                                )}
                                {detailData?.orgBasic?.creditCode && (
                                    <Descriptions.Item label='统一社会信用代码'>
                                        {detailData?.orgBasic?.creditCode}
                                    </Descriptions.Item>
                                )}
                                {detailData?.orgBasic?.qualification && qualification && (
                                    <Descriptions.Item label='企业资质'>
                                        {detailData?.orgBasic?.qualification === '空' ? (
                                            '空'
                                        ) : (
                                            <Image
                                                preview={true}
                                                width={70}
                                                height={50}
                                                src={qualification}
                                                fallback={errImage}
                                            />
                                        )}
                                    </Descriptions.Item>
                                )}
                                {detailData?.orgBasic?.orgTransId && (
                                    <Descriptions.Item label='链上哈希' className='ellipsis-text'>
                                        <a
                                            onClick={() => {
                                                setTransId(detailData?.orgBasic?.orgTransId);
                                                setChainDetailModalVisible(true);
                                            }}
                                            title={detailData?.orgBasic?.orgTransId}
                                        >
                                            {detailData?.orgBasic?.orgTransId}
                                        </a>
                                    </Descriptions.Item>
                                )}
                                {detailData?.orgBasic?.orgTransTime && (
                                    <Descriptions.Item label='上链时间'>
                                        {dayjs(detailData?.orgBasic?.orgTransTime).format('YYYY-MM-DD HH:mm:ss')}
                                    </Descriptions.Item>
                                )}
                            </Descriptions>
                        ) : (
                            <div style={{ color: 'red' }}>该详情尚未填写</div>
                        )}
                    </Panel>
                )}
                {detailData?.orgBrief && (
                    <Panel header='企业简介' key='12'>
                        {Object.keys(detailData?.orgBrief).length !== 0 ? (
                            <Descriptions>
                                {detailData?.orgBrief?.introduce && (
                                    <Descriptions.Item label='企业介绍'>
                                        {detailData?.orgBrief?.introduce}
                                        {/* <pre className='showtext-item'>{detailData?.orgBrief?.introduce}</pre> */}
                                    </Descriptions.Item>
                                )}
                                {detailData?.orgBrief?.picture && picture && picture.length > 0 && (
                                    <Descriptions.Item label='企业图片'>
                                        {detailData?.orgBrief?.picture?.includes('空') ? (
                                            '空'
                                        ) : (
                                            <Image.PreviewGroup>
                                                <Space>
                                                    {picture?.map((item: any) => {
                                                        return (
                                                            <Image
                                                                preview={true}
                                                                width={70}
                                                                height={50}
                                                                src={item}
                                                                fallback={errImage}
                                                            />
                                                        );
                                                    })}
                                                </Space>
                                            </Image.PreviewGroup>
                                        )}
                                    </Descriptions.Item>
                                )}
                                {detailData?.orgBrief?.video && video && (
                                    <Descriptions.Item label='企业视频'>
                                        {detailData?.orgBrief?.video !== '空' ? (
                                            <video src={video} width={150} controls></video>
                                        ) : (
                                            '空'
                                        )}
                                    </Descriptions.Item>
                                )}
                            </Descriptions>
                        ) : (
                            <div style={{ color: 'red' }}>该详情尚未填写</div>
                        )}
                    </Panel>
                )}

                {detailData?.landPlantBasic && (
                    <Panel header='种植信息' key='13'>
                        {Object.keys(detailData?.landPlantBasic).length !== 0 ? (
                            <Descriptions>
                                {detailData?.landPlantBasic?.plantName && (
                                    <Descriptions.Item label='农作物类型'>
                                        {detailData?.landPlantBasic?.plantName || '-'}
                                    </Descriptions.Item>
                                )}
                                {detailData?.landPlantBasic?.sowTime && (
                                    <Descriptions.Item label='播种时间'>
                                        {detailData?.landPlantBasic?.sowTime != '空'
                                            ? dayjs(detailData?.landPlantBasic?.sowTime).format('YYYY-MM-DD HH:mm:ss')
                                            : '-'}
                                    </Descriptions.Item>
                                )}
                                {detailData?.landPlantBasic?.harvestTime && (
                                    <Descriptions.Item label='收割时间'>
                                        {detailData?.landPlantBasic?.harvestTime != '空'
                                            ? dayjs(detailData?.landPlantBasic?.harvestTime).format(
                                                  'YYYY-MM-DD HH:mm:ss'
                                              )
                                            : '-'}
                                    </Descriptions.Item>
                                )}
                                {/* {detailData?.landPlantBasic?.landPlantBasicCode && (
                                    <Descriptions.Item label='产品编码'>
                                        {detailData?.landPlantBasic?.landPlantBasicCode || '-'}
                                    </Descriptions.Item>
                                )} */}
                                {detailData?.landPlantBasic?.landName && (
                                    <Descriptions.Item label='种植地块'>
                                        {detailData?.landPlantBasic?.landName || '-'}
                                    </Descriptions.Item>
                                )}
                                {detailData?.landPlantBasic?.harvestNum && (
                                    <Descriptions.Item label='地块产量'>
                                        {detailData?.landPlantBasic?.harvestNum + '吨' || '-'}
                                    </Descriptions.Item>
                                )}

                                {/* {detailData?.product?.specification && (
                                    <Descriptions.Item label='产品规格'>
                                        {detailData?.product?.specification || '-'}
                                    </Descriptions.Item>
                                )}
                                {detailData?.product?.ingredient && (
                                    <Descriptions.Item label='配料'>
                                        {detailData?.product?.ingredient || '-'}
                                    </Descriptions.Item>
                                )} */}
                                {/* {detailData?.product?.productAptitude && (
                                    <Descriptions.Item label='产品合格证明'>
                                        {detailData?.product?.productAptitude === '空' ? (
                                            '空'
                                        ) : (
                                            <Image
                                                preview={true}
                                                width={70}
                                                height={50}
                                                src={detailData?.product?.productAptitude}
                                                fallback={errImage}
                                            />
                                        )}
                                    </Descriptions.Item>
                                )} */}
                                {/* {detailData?.product?.productTransId && (
                                    <Descriptions.Item label='链上哈希' className='ellipsis-text'>
                                        {detailData?.product?.productTransId ? (
                                            <a
                                                onClick={() => {
                                                    setTransId(detailData?.product?.productTransId);
                                                    setChainDetailModalVisible(true);
                                                }}
                                                title={detailData?.product?.productTransId}
                                            >
                                                {detailData?.product?.productTransId}
                                            </a>
                                        ) : (
                                            '-'
                                        )}
                                    </Descriptions.Item>
                                )}
                                {detailData?.product?.productTransTime && (
                                    <Descriptions.Item label='上链时间'>
                                        {detailData?.product?.productTransTime
                                            ? dayjs(detailData?.product?.productTransTime).format('YYYY-MM-DD HH:mm:ss')
                                            : '-'}
                                    </Descriptions.Item>
                                )} */}
                            </Descriptions>
                        ) : (
                            <div style={{ color: 'red' }}>该详情尚未填写</div>
                        )}
                    </Panel>
                )}
                {/* 收购信息 */}
                {detailData?.purchase && (
                    <Panel header='收购信息' key='14'>
                        {Object.keys(detailData?.purchase).length !== 0 ? (
                            <Descriptions>
                                {detailData?.purchase?.farmerName && (
                                    <Descriptions.Item label='农户姓名'>
                                        {detailData?.purchase?.farmerName || '-'}
                                    </Descriptions.Item>
                                )}
                                {detailData?.purchase?.acquiringFirm && (
                                    <Descriptions.Item label='收购方'>
                                        {detailData?.purchase?.acquiringFirm || '-'}
                                    </Descriptions.Item>
                                )}
                                {detailData?.purchase?.plantName && (
                                    <Descriptions.Item label='收购商品'>
                                        {detailData?.purchase?.plantName || '-'}
                                    </Descriptions.Item>
                                )}
                                {detailData?.purchase?.purchaseWeight && (
                                    <Descriptions.Item label='收购重量'>
                                        {detailData?.purchase?.purchaseWeight != '空'
                                            ? detailData?.purchase?.purchaseWeight + '吨'
                                            : '-'}
                                    </Descriptions.Item>
                                )}
                                {detailData?.purchase?.purchaseTime && (
                                    <Descriptions.Item label='收购时间'>
                                        {detailData?.purchase?.purchaseTime != '空'
                                            ? dayjs(detailData?.purchase?.purchaseTime).format('YYYY-MM-DD HH:mm:ss')
                                            : '-'}
                                    </Descriptions.Item>
                                )}
                                {/* {detailData?.landPlantBasic?.landPlantBasicCode && (
                                    <Descriptions.Item label='产品编码'>
                                        {detailData?.landPlantBasic?.landPlantBasicCode || '-'}
                                    </Descriptions.Item>
                                )} */}

                                {/* {detailData?.product?.specification && (
                                    <Descriptions.Item label='产品规格'>
                                        {detailData?.product?.specification || '-'}
                                    </Descriptions.Item>
                                )}
                                {detailData?.product?.ingredient && (
                                    <Descriptions.Item label='配料'>
                                        {detailData?.product?.ingredient || '-'}
                                    </Descriptions.Item>
                                )} */}
                                {/* {detailData?.product?.productAptitude && (
                                    <Descriptions.Item label='产品合格证明'>
                                        {detailData?.product?.productAptitude === '空' ? (
                                            '空'
                                        ) : (
                                            <Image
                                                preview={true}
                                                width={70}
                                                height={50}
                                                src={detailData?.product?.productAptitude}
                                                fallback={errImage}
                                            />
                                        )}
                                    </Descriptions.Item>
                                )} */}
                                {/* {detailData?.product?.productTransId && (
                                    <Descriptions.Item label='链上哈希' className='ellipsis-text'>
                                        {detailData?.product?.productTransId ? (
                                            <a
                                                onClick={() => {
                                                    setTransId(detailData?.product?.productTransId);
                                                    setChainDetailModalVisible(true);
                                                }}
                                                title={detailData?.product?.productTransId}
                                            >
                                                {detailData?.product?.productTransId}
                                            </a>
                                        ) : (
                                            '-'
                                        )}
                                    </Descriptions.Item>
                                )}
                                {detailData?.product?.productTransTime && (
                                    <Descriptions.Item label='上链时间'>
                                        {detailData?.product?.productTransTime
                                            ? dayjs(detailData?.product?.productTransTime).format('YYYY-MM-DD HH:mm:ss')
                                            : '-'}
                                    </Descriptions.Item>
                                )} */}
                            </Descriptions>
                        ) : (
                            <div style={{ color: 'red' }}>该详情尚未填写</div>
                        )}
                    </Panel>
                )}
                {/* 仓储信息 */}
                {detailData?.warehouseInfoDataTo && (
                    <Panel header='仓储信息' key='15'>
                        {Object.keys(detailData?.warehouseInfoDataTo).length !== 0 ? (
                            <Descriptions>
                                {detailData?.warehouseInfoDataTo?.inboundTime && (
                                    <Descriptions.Item label='入库时间'>
                                        {detailData?.warehouseInfoDataTo?.inboundTime != '空'
                                            ? dayjs(detailData?.warehouseInfoDataTo?.inboundTime).format(
                                                  'YYYY-MM-DD HH:mm:ss'
                                              )
                                            : '-'}
                                    </Descriptions.Item>
                                )}
                                {detailData?.warehouseInfoDataTo?.warehouseName && (
                                    <Descriptions.Item label='仓库名称'>
                                        {detailData?.warehouseInfoDataTo?.warehouseName || '-'}
                                    </Descriptions.Item>
                                )}
                                {detailData?.warehouseInfoDataTo?.warehouseAddress && (
                                    <Descriptions.Item label='仓库地址'>
                                        {detailData?.warehouseInfoDataTo?.warehouseAddress || '-'}
                                    </Descriptions.Item>
                                )}
                            </Descriptions>
                        ) : (
                            <div style={{ color: 'red' }}>该详情尚未填写</div>
                        )}
                    </Panel>
                )}
            </Collapse>

            <ChainDetailModal
                transactionId={transId}
                open={ChainDetailModalVisible}
                onCancel={() => setChainDetailModalVisible(false)}
            />

            <BaseModal {...recordModalConfig}>
                <ConfigProvider locale={zhCN}>
                    <BaseTable
                        columns={columns}
                        showHeader={false}
                        dataSource={traceCodeCheckTimesQuery?.data?.data?.records?.map((item: any) => {
                            return {
                                time: item
                            };
                        })}
                        // locale={{
                        //     emptyText: '暂无数据'
                        // }}
                        pagination={{
                            current: pageIndex,
                            pageSize: pageSize,
                            total: traceCodeCheckTimesQuery?.data?.data?.total,
                            // showTotal: (total: number, range: [number, number]) => {
                            //     return `共 ${total} 条记录 ${
                            //         pageIndex && pageSize ? `第 ${pageIndex} / ${Math.ceil(total / pageSize)} 页` : ''
                            //     }`;
                            // },
                            onChange: (page, pageSize) => {
                                setPageIndex(page);
                                setPageSize(pageSize);
                            }
                        }}
                        style={{ width: '100%' }}
                        bordered
                        summary={() => (
                            <Table.Summary fixed>
                                <Table.Summary.Row>
                                    <Table.Summary.Cell index={0}>总计</Table.Summary.Cell>
                                    <Table.Summary.Cell index={1}>
                                        {traceCodeCheckTimesQuery?.data?.data?.total}
                                    </Table.Summary.Cell>
                                </Table.Summary.Row>
                            </Table.Summary>
                        )}
                    />
                </ConfigProvider>
            </BaseModal>
        </BaseCard>
    );
};

export default FleeWarning;

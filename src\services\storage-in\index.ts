/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-11-01 18:28:42
 * @LastEditTime: 2022-11-01 18:28:42
 * @LastEditors: PhilRandWu
 */
import request from '../request';
//入库分页
export const storageInPage = (obj: any) => {
    return request({
        url: '/inWarehouse/getList',
        method: 'post',
        data: obj
    });
};
//作废入库
export const cancelStorageIn = (obj: any) => {
    return request({
        url: `/storage-in/cancelStorageIn?storageInId=${obj.storageInId}`,
        method: 'post',
        data: obj
    });
};
//可入库食品列表
export const canInFoodList = (obj: any) => {
    return request({
        url: '/storage-in/canInFoodList',
        method: 'post',
        data: obj
    });
};
//食品的生产批次
export const productionBatchList = (obj: any) => {
    return request({
        url: `/production/productionBatchList?foodId=${obj.foodId}`,
        method: 'post',
        data: obj
    });
};
//查看详情
export const storageInDetail = (obj: any) => {
    return request({
        url: `/inWarehouse/getDetail`,
        method: 'get',
        params: obj
    });
};
//新增入库信息
export const addStorageIn = (obj: any) => {
    return request({
        url: '/storage-in/addStorageIn',
        method: 'post',
        data: obj
    });
};

.loginModal {
    .ant-modal-content {
        .loginTips {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .noCountImg {
                width: 50px;
                height: 50px;
                background: url('../../assets/icon/nocount.png');
                background-size: contain;
                margin-bottom: 9px;
            }
            .returnLoginBtn {
                cursor: pointer;
                padding: 4px 7px;
                display: block;
                width: 56px;
                height: 20px;
                font-size: 14px;
                font-weight: 400;
                color: #ffffff;
                line-height: 20px;
                background: #3d73ef;
                border-radius: 4px;
                margin-top: 77px;
                box-sizing: content-box;
            }
        }
        .ant-modal-footer {
            display: none;
        }
    }
}
.loginpswTips {
    color: #f64041;
    font-size: 12px;
    margin-left: 15px;
}
.ant-modal-root .changePswModal {
    .ant-modal-header .ant-modal-title {
        font-size: 16px;
    }
    .ant-modal-body {
        padding-left: 61px;
        margin-top: 7px;
        .changePsw {
            .ant-form.ant-form-horizontal {
                margin: 0;
            }
            .pswTips {
                color: #f64041;
                font-size: 12px;
            }
        }
    }
}

.bg {
    width: 100%;
    height: 100vh;
    // background: url('../../assets/icon/bg.jpg');
    background: url('../../assets/landicon/bg.png') no-repeat;
    background-size: cover;
    display: flex;
    justify-content: center;
    align-items: center;
    // padding-top: px;
    .loginCard {
        width: 884px;
        height: 603px;
        background: #ffffff;
        border-radius: 7px;
        margin: 0 auto;
        display: flex;
        flex-direction: row;
        align-items: center;
        border-radius: 20px;
        .leftImg {
            position: relative;
            // background-image: url('../../assets/icon/leftLoginImg.png');
            background-image: url('../../assets/landicon/leftLoginImg.png');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            width: 389px;
            height: 100%;
            border-radius: 20px 0 0 20px;
            .leftImgFont{
                position: absolute;
                top:40px;
                left:50px;
                p{
                    color:#f1f1f1;
                    font-size: 36px;
                    line-height: 50px;
                    height:50px;
                    font-weight: 700;
                }
                .p-line{
                    margin-top:20px;
                    width:45%;
                    height:6px;
                    background:#fff;
                    border-radius:6px;

                }
            }
        }
        .rightForm {
            width: 495px;
            padding: 0 60px;
        }
        .loginAccount,
        .loginPsw,
        .loginCode {
            display: flex;
            align-items: center;
            .ant-input-prefix {
                width: 16px;
                height: 16px;
            }
        }
        .loginAccount .ant-input-prefix {
            background: url('../../assets/icon/accountIcon.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .loginPsw .ant-input-prefix {
            background: url('../../assets/icon/passwordImg.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        .loginTitle {
            margin-bottom: 53px;
            opacity: 1;
            color: #333333;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 30px;
            line-height: 36px;
            letter-spacing: 0px;
        }
        .formImg {
            margin-top: 3px;
            margin-left: -10px;
        }
        .loginAccount,
        .loginPsw,
        .loginCode {
            width: 363px;
            height: 44px;
            opacity: 1;
            border-radius: 16px;
            background: #f5f5fa !important;
            input {
                background: #f5f5fa;
            }
        }
        .loginContainer {
            .loginBtn {
                width: 363px;
                height: 46px;
                line-height: 46px;
                margin-top: 16px;
                padding: 0;
                margin-bottom: 10px;
                opacity: 1;
                border-radius: 16px;
                background: #f1b137 !important;
                span {
                    opacity: 1;
                    color: #ffffff;
                    font-family: PingFang SC;
                    font-weight: regular;
                    font-size: 16px;
                    line-height: 19px;
                    letter-spacing: 0px;
                    text-align: left;
                }
            }
        }
        .otherLogin {
            cursor: pointer;
            opacity: 1;
            color: #1e5aeb;
            font-family: PingFang SC;
            font-weight: regular;
            font-size: 14px;
            line-height: 17px;
            letter-spacing: 0px;
        }
        .formItemContainer {
            display: flex;
            justify-content: space-between;
            margin-bottom: 24px;
            .requiredIcon {
                display: inline-block;
                margin-right: 4px;
                color: #ff3838;
                font-size: 14px;
                font-family: SimSun, sans-serif;
                line-height: 1;
                margin-right: 14px;
                margin-top: 10px;
            }
            .ant-row.ant-form-item {
                margin: 0;
                .ant-form-item-label label {
                    width: 56px;
                    height: 20px;
                    font-size: 14px;
                    color: #333333;
                    line-height: 20px;
                }
            }
        }
        .ant-form.ant-form-horizontal {
            display: inline-block;
            width: 430px;
        }
        .ant-form-item-explain.ant-form-item-explain-connected {
            margin-left: 16px;
        }
    }
    .versionInfo {
        box-sizing: border-box;
        display: inline-block;
        width: 50px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        position: absolute;
        bottom: 0;
        right: 0;
        background-color: #1890ff;
        color: #fff;
        display: inline-block;
    }
    .versionInfoDis {
        box-sizing: border-box;
        display: inline-block;
        width: 50px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        position: absolute;
        bottom: 0;
        right: 0;
        background-color: #1890ff;
        color: #fff;
        display: none;
    }
}

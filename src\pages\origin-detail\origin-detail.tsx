/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 17:15:57
 * @LastEditTime: 2022-11-01 18:12:36
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BaseFormItem from '@components/base-form-item';
import PageTitle from '@components/page-title';
import React, { useState } from 'react';
import { Form, message } from 'antd';
import { useMutation, useQuery } from 'react-query';
import { getOriginDetail } from '@services/origin';
import { enumeration } from './config';
import SwitchList from '@components/switch-list';
import styles from './index.module.less';
import { useLocation } from 'react-router-dom';
import { ReformChainError } from '@utils/errorCodeReform';
import dayjs from 'dayjs';
import { Image } from 'antd';
import FilterForm from '@components/filter-form/filter-form';

import { FormItemImages, FormItemVideo } from '@components';
import ChainDetailModal from '@components/chain_detail_modal';
import { decryptedUrl, isArrayArr } from '@utils';

const FoodDetail = (props: any) => {
    const { state } = useLocation();
    const [detailForm] = Form.useForm();

    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    // console.log("state",state)

    const detailquery = useQuery(
        ['getOriginDetail'],
        () => {
            return getOriginDetail({
                placeId: state?.id
            });
        },
        {
            async onSuccess(res) {
                console.log('res0990099', res);
                const arrayData = await Promise.all(
                    isArrayArr(res?.data?.placeImg)?.map((item: any) => {
                        return decryptedUrl(item);
                    })
                );
                const placeAptitude = await decryptedUrl(res?.data?.placeAptitude);
                // setPlaceAptitude(placeAptitude)
                // setPromotionPicData(arrayData)
                const videos = await decryptedUrl(res?.data?.placeVideo);
                detailForm.setFieldsValue({
                    ...res?.data,
                    placeImg: arrayData && arrayData.length > 0 ? arrayData : null,
                    placeVideo: videos ? videos : null,
                    placeAptitude: placeAptitude ? placeAptitude : null,
                    transactionTime: res?.data?.transactionTime
                        ? dayjs(res?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
                        : '-'
                });
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );

    const originInfoConfig = [
        {
            label: '产地名称',
            name: 'placeName',
            value: 'placeName',
            type: 'ShowText'
        },
        {
            label: '产地地址',
            name: 'placeAddress',
            value: 'placeAddress',
            type: 'ShowText'
        },
        {
            label: '产地位置图',
            name: 'placeAptitude',
            value: 'placeAptitude',
            type: 'Custom',
            children: (
                <Form.Item key='placeAptitude' name='placeAptitude'>
                    <FormItemImages width={150}></FormItemImages>
                </Form.Item>
            )
        }
    ];

    const originIntroductionConfig = [
        {
            label: '产地介绍',
            name: 'placeIntro',
            value: 'placeIntro',
            type: 'ShowScrollText'
        },
        {
            label: '产地图片',
            name: 'placeImg',
            value: 'placeImg',
            type: 'Custom',
            children: (
                <Form.Item key='placeImg' name='placeImg'>
                    <FormItemImages width={200} height={100}></FormItemImages>
                </Form.Item>
            )
        },
        {
            label: '产地视频',
            name: 'placeVideo',
            value: 'placeVideo',
            type: 'Custom',
            children: (
                <Form.Item key='placeVideo' name='placeVideo'>
                    <FormItemVideo width={200} controls></FormItemVideo>
                </Form.Item>
            )
        }
    ];

    const ChainDetailModalConfig = {
        transactionId: detailForm.getFieldValue('transactionId'),
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };

    const chainConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Link',
            onClick: () => {
                setChainDetailModalVisible(true);
            }
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];

    const onFinish = (values: any) => {
        console.log('Success:', values);
    };

    const onFinishFailed = (errorInfo: any) => {
        console.log('Failed:', errorInfo);
    };
    console.log('detailquery?.data?.data?.material', detailquery?.data?.data?.material);
    return (
        <div>
            <BaseCard title={<PageTitle title='产地详情' bg='container chandi' />}>
                <Form
                    name='basic'
                    form={detailForm}
                    onFinish={onFinish}
                    onFinishFailed={onFinishFailed}
                    autoComplete='off'
                >
                    <PageTitle title='产地基础信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={originInfoConfig} labelCol={false} />

                    <PageTitle title='产地简介' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={originIntroductionConfig} labelCol={false} />

                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={chainConfig} labelCol={false} />
                </Form>
            </BaseCard>
            <ChainDetailModal {...ChainDetailModalConfig} />
        </div>
    );
};

export default FoodDetail;

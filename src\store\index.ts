/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-14 11:14:36
 * @LastEditTime: 2022-11-01 18:28:00
 * @LastEditors: PhilRandWu
 */
import { configureStore } from '@reduxjs/toolkit';
import userSliceReducer from './slice-reducer/user';
import appSliceReducer from './slice-reducer/app';
import spinContextReducer from './slice-reducer/spinLoading';

import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

const store = configureStore({
    reducer: {
        user: userSliceReducer,
        appContext: appSliceReducer,
        spinContext: spinContextReducer,
    },
    middleware: (getDefaultMiddleware) => getDefaultMiddleware({})
});

export type IStore = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export default store;

/**
 * 代替react-redux的useDispatch
 */
export const useAppDispatch = () => useDispatch<AppDispatch>();
/**
 * 代替react-redux的useSelector
 */
export const useAppSelector: TypedUseSelectorHook<IStore> = useSelector;

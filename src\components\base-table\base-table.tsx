/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-06-29 13:24:26
 * @LastEditTime: 2022-11-01 18:35:48
 * @LastEditors: PhilRandWu
 */

import { Table } from 'antd';
import React, { Ref, useState } from 'react';
import type { TableProps } from 'antd';

import './override.less';

interface IProps<RecordType> extends TableProps<RecordType> {
    ref?: Ref<HTMLDivElement> | undefined;
    titleNode?: React.ReactNode[];
    btnDisplay?: any;
    align?: string;
}

function BaseTable<RecordType extends object = any>(props: IProps<RecordType>) {
    return (
        <Table
            className='base-table'
            pagination={false}
            sticky
            title={() => <>{props.btnDisplay && props.btnDisplay()}</>}
            {...props}
        />
    );
}

export default BaseTable;

const SelectTableElement = (props: any) => {
    const rowSelection = {
        onChange: (selectedRowKeys: any, selectedRows: any) => {
            console.log(selectedRowKeys);
            props.setCheckId(selectedRowKeys);
            props.setCheckData(selectedRows);
        },
        getCheckboxProps: (record: any) => ({
            disabled: record.name && record.name === 'Disabled User',
            name: record.name || ''
        })
    };

    const resetSelect = () => {
        props.setCheckData([]);
        props.setCheckId([]);
    };

    return (
        <div>
            <Table
                pagination={false}
                rowSelection={{
                    type: props?.clickType ? props?.clickType : 'checkbox',
                    selectedRowKeys: props.checkId,
                    ...rowSelection
                }}
                title={() => <>{props.btnDisplay && props.btnDisplay(props.checkData, resetSelect)}</>}
                {...props}
            />
        </div>
    );
};

export const SelectTable = React.memo(SelectTableElement);

/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-28 11:08:40
 * @LastEditTime: 2022-09-28 16:23:26
 * @LastEditors: PhilRandWu
 */
import { Tooltip } from 'antd';
import './index.less';

interface Tooptip {
    data: any;
    maxWidth?: number;
    maxHeight?: number;
    slice?: boolean;
    sliceData?: string;
    changeplay?: any;
    getPopupContainer?: any;
    preStyle?: React.CSSProperties;
}

const BaseTooptip = (props: Tooptip) => {
    const data = props?.data ? props?.data + '' : '';
    const sliceData = props?.sliceData ? props?.sliceData : '';
    return (
        <Tooltip
            // getPopupContainer={(trigger: any) => trigger.parentNode}
            getPopupContainer={
                props?.getPopupContainer
                    ? props?.getPopupContainer
                    : props?.changeplay
                    ? () => document.body
                    : (trigger: any) => trigger.parentNode
            }
            className='baseTooltip'
            // visible={true}
            title={
                <pre
                    style={{
                        whiteSpace: 'pre-wrap',
                        maxWidth: props?.maxWidth ? props?.maxWidth : 100,
                        maxHeight: props?.maxHeight ? props?.maxHeight : 134,
                        // background: 'none',
                        overflowY: 'auto'
                    }}
                >
                    {data}
                </pre>
            }
            overlayClassName='toolTips'
            color='#F7F8FA'
            overlayStyle={{
                boxShadow: '5px 5px 10px rgba(0,0,0,.25) -5px -5px 5px rgba(0,0,0,.25)'
            }}
            overlayInnerStyle={{ color: '#5B5B69' }}
        >
            <span>
                <pre style={props?.preStyle} className='preData'>
                    {props?.slice ? sliceData : data}
                </pre>
            </span>
        </Tooltip>
    );
};

export default BaseTooptip;

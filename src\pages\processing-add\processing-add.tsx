/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 18:07:55
 * @LastEditTime: 2024-10-10 09:24:40
 * @LastEditors: 吴山仁
 */

import BaseButton from '@components/base-button';
import BaseCard from '@components/base-card';
import FilterForm from '@components/filter-form';
import PageTitle from '@components/page-title';
import {
    Divider,
    Form,
    FormInstance,
    message,
    DatePicker,
    Row,
    Col,
    Upload,
    Button,
    Table,
    Popconfirm,
    Radio,
    Select
} from 'antd';
import { ReformChainError } from '@utils/errorCodeReform';
import BaseTooptip from '@components/base-tooltip';
import { getLocalPrivatekey } from '@utils/blockChainUtils';
import { purchaseBatchList } from '@services/purchase-controller';
import { materialList } from '@services/food';
import {
    materialAndPurchaseList,
    addProduction,
    selectProcessPage,
    selectProcessPageLand,
    cancelPurchase
} from '@services/production';
import { useNavigate } from 'react-router-dom';
import { signData } from '../../utils/blockChainUtils';
import { useMutation, useQuery } from 'react-query';
import React, { Component, useEffect, useRef, useState } from 'react';
import { productInfoConfigs } from './config';
import styles from './index.module.less';
import { useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import BaseTable, { SelectTable } from '@components/base-table/base-table';
import TableHead from '@components/table-head/table-head';
import BaseModal from '@components/base-modal';
import BasePagination from '@components/base-pagination';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import { ColumnsType } from 'antd/lib/table';
import BaseInput from '@components/base-input';
import BaseSelect from '@components/base-select';
import BaseFormItem from '@components/base-form-item';
import { LandSourceService, PlantNamesSourceService, FarmerNameById } from '@services/land-test';
import { fileUpload, getFileUrlFormUploadedFile } from '@utils';
import { UploadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

const RawMaterialAdd = (props: any) => {
    const navigate = useNavigate();
    const { state } = useLocation();
    console.log(state);
    const [upfile, setupfile]: any = useState('');

    const querylist = useRef<any>('');
    const [processSelect, setProcessSelect] = useState<any>();
    const [selectedProcess, setSelectedProcess] = useState<any>([]);
    const [addModalVisible, setAddModelVisible] = useState(false);
    const [addModalVisibleland, setAddModelVisibleland] = useState(false);
    const [isUpLoading, setIsUpLoading]: any = useState();
    const [pageIndex, setPageIndex] = useState(1);
    const [pageSize, setPageSize] = useState(5);
    const [landSelect, setLandSelect] = useState<any>();
    const [selectedLand, setSelectedLand] = useState<any>(state.purchase_data); //

    // const [landModalVisible, setLandModelVisible] = useState(false);
    const [availableNums, setAvailableNums] = useState<any>(''); // 种植过程

    const [text, setText]: any = useState('选择生产过程');

    console.log('state', state);
    const [SourceCodeForm] = Form.useForm();
    const [processForm] = Form.useForm();
    const dispatch = useDispatch();
    // 获取地块名
    const landProductSele = useQuery(['landProductSele'], () => LandSourceService());

    const landProductListData: any[] = landProductSele?.data?.data;

    const typeProductSele = useQuery(['typeProductSele'], () => PlantNamesSourceService());

    const typeProductListData: any[] = typeProductSele?.data?.data;
    console.log(typeProductListData);
    let newdata = SourceCodeForm?.getFieldValue('productionDate');
    useEffect(() => {
        const date = new Date().getTime();
        console.log(date.toString());
        const datetext = date.toString().slice(1, 13);

        SourceCodeForm.setFieldsValue({
            productionBatch: datetext,
            grower: selectedLand[0].farmerName
        });
    }, []);
    //采购信息列表
    // const foodconfig = useQuery(
    //     ['foodconfig'],
    //     () => {
    //         return materialAndPurchaseList({
    //             productId: state?.id
    //         });
    //     },
    //     {
    //         onSuccess(res) {
    //             getLocalPrivatekey(dispatch);
    //             const date = new Date().getTime();
    //             console.log(date.toString());
    //             const datetext = date.toString().slice(1, 13);

    //             SourceCodeForm.setFieldsValue({
    //                 productionBatch: datetext
    //             });
    //             console.log('res: ', res);
    //         },
    //         onError(err: any) {
    //             ReformChainError(err);
    //         }
    //     }
    // );
    // const [farmerName, setFarmerName] = useState('');
    // // 种植户查询
    // const FarmerNameByIdQuery = useQuery(
    //     ['FarmerNameByIdQuery', farmerName],
    //     () => {
    //         return FarmerNameById(farmerName);
    //     },
    //     {
    //         enabled: Boolean(farmerName),
    //         onSuccess(res) {
    //             SourceCodeForm.setFieldsValue({
    //                 grower: res.data.data
    //             });
    //         },
    //         onError(err: any) {
    //             ReformChainError(err);
    //         }
    //     }
    // );
    // SourceCodeForm.setFieldsValue({
    //     grower: FarmerNameByIdQuery?.data?.data
    // });

    //选择生产过程列表
    const processListQuery = useQuery(
        ['processListQuery', pageSize, pageIndex],
        () => {
            return selectProcessPage({
                pageIndex: pageIndex,
                pageSize: pageSize,
                processName: querylist?.current?.processName?.trim() || undefined,
                startTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[0]).startOf('day')).format()
                    : undefined,
                endTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[1]).endOf('day')).format()
                    : undefined
            });
        },
        {
            onSuccess(res) {
                console.log('res: ', res);
            },
            onError(err: any) {
                ReformChainError(err);
            },
            enabled: !!addModalVisible
        }
    );
    //选择种植过程列表
    // const ladnListQuery = useQuery(
    //     ['ladnListQuery', pageSize, pageIndex],
    //     () => {
    //         return selectProcessPageLand({
    //             pageIndex: pageIndex,
    //             pageSize: pageSize,
    //             landId: querylist?.current?.landName,
    //             plantName: querylist?.current?.plantName
    //             // startTime: querylist?.current?.createTime
    //             //     ? dayjs.utc(dayjs(querylist?.current?.createTime[0]).startOf('day')).format()
    //             //     : undefined,
    //             // endTime: querylist?.current?.createTime
    //             //     ? dayjs.utc(dayjs(querylist?.current?.createTime[1]).endOf('day')).format()
    //             //     : undefined
    //         });
    //     },
    //     {
    //         onSuccess(res) {
    //             console.log('res: ', res);
    //         },
    //         onError(err: any) {
    //             ReformChainError(err);
    //         },
    //         enabled: !!addModalVisibleland
    //     }
    // );

    //选择收购过程列表
    const purchaseQuery = useQuery(
        ['purchaseQuery', pageSize, pageIndex],
        () => {
            return cancelPurchase({
                pageIndex: pageIndex,
                pageSize: pageSize,
                purchaseBatch: querylist?.current?.purchaseBatch,
                startTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[0]).startOf('day')).format()
                    : undefined,
                endTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[1]).endOf('day')).format()
                    : undefined
            });
        },
        {
            onSuccess(res) {
                console.log('res: ', res);
            },
            onError(err: any) {
                ReformChainError(err);
            },
            enabled: !!addModalVisibleland
        }
    );
    console.log(purchaseQuery);
    const addproduction = useMutation(addProduction, {
        onSuccess(res) {
            message.success('添加成功');
            navigate('/product/process');
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });

    const productVideoAcceptTypes = ['.rar', '.zip', '.doc', '.docx', '.pdf', '.jpg'];
    const handleBeforeProductVideoUpload = (file: any) => {
        const fileType = file?.name?.split('.').at(-1).toLowerCase();
        console.log(fileType, file, 'fffffff');
        if (!productVideoAcceptTypes.includes('.' + fileType)) {
            message.error('文件格式不正确');
            return Upload.LIST_IGNORE;
        }
        if (file.size / 1024 / 1024 > 20) {
            message.error('附件最大上传20MB');
            return Upload.LIST_IGNORE;
        }
        return true;
    };

    const mapToEnum_1: any = {};
    const mapToEnum_2: any = {};

    const listColumn: ColumnsType<any> = [
        {
            title: '生产过程编号',
            dataIndex: 'id',
            key: 'id',
            ellipsis: true
        },
        {
            title: '过程名称',
            dataIndex: 'processName',
            key: 'processName',
            ellipsis: true
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true,
            render: (time) => {
                return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
            }
        }
    ];

    // 种植过程
    const listColumnLand: ColumnsType<any> = [
        {
            title: '地块名称',
            dataIndex: 'landName',
            key: 'landName',
            ellipsis: true
        },
        {
            title: '种植批次',
            dataIndex: 'plantBatch',
            key: 'plantBatch',
            ellipsis: true
        },
        {
            title: '农作物类型',
            dataIndex: 'plantName',
            key: 'plantName',
            ellipsis: true
        },

        {
            title: '播种时间',
            dataIndex: 'sowTime',
            key: 'sowTime',
            ellipsis: true,
            render: (time) => {
                return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
            }
        },
        {
            title: '收割时间',
            dataIndex: 'harvestTime',
            key: 'harvestTime',
            ellipsis: true,
            render: (time) => {
                return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '';
            }
        },
        {
            title: '可加工数量(吨)',
            dataIndex: 'availableNum',
            key: 'availableNum',
            ellipsis: true
        }
    ];
    // 收购过程
    const listColumnPurchase: ColumnsType<any> = [
        {
            title: '收购批次',
            dataIndex: 'purchaseBatch',
            key: 'purchaseBatch',
            ellipsis: true
        },

        {
            title: '收购时间',
            dataIndex: 'purchaseTime',
            key: 'purchaseTime',
            ellipsis: true,
            render: (time: any) => {
                return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
            }
        },
        {
            title: '农作物类型',
            dataIndex: 'plantName',
            key: 'plantName',
            ellipsis: true
        },
        {
            title: '收割时间',
            dataIndex: 'harvestTime',
            key: 'harvestTime',
            ellipsis: true,
            render: (time: any) => {
                return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
            }
        },

        {
            title: '可加工数量(吨)',
            dataIndex: 'availablePurchaseWeight',
            key: 'availablePurchaseWeight',
            ellipsis: true
        }
    ];
    // (foodconfig?.data?.data || [])?.forEach((item: any, index: any) => {
    //     mapToEnum_1[item.id] = item.material_name; //原料
    //     (item.purchaseBatch || [])?.forEach((item: any, index: any) => {
    //         mapToEnum_2[item.id] = item.batch_number;
    //     });
    //     //   mapToEnum_2[item.food_id] = item.food_number //采购
    // });
    const [unit, setUnit]: any = useState('1');
    const suffixSelector = (
        <Form.Item name='suffix' noStyle>
            <Select
                defaultValue={unit}
                style={{ width: 70 }}
                onChange={(e) => {
                    console.log(e);
                    setUnit(e);
                }}
            >
                <Option value='1'>袋</Option>
                <Option value='2'>盒</Option>
                <Option value='2'>个</Option>
            </Select>
        </Form.Item>
    );
    const processColumns = [
        {
            title: '过程编号',
            dataIndex: 'id',
            key: 'id'
        },
        {
            title: '过程名称',
            dataIndex: 'processName',
            key: 'processName'
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true,
            render: (time: any) => {
                return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
            }
        },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            render: (_: any, data: any) => {
                return (
                    <Popconfirm
                        title='确定删除吗?'
                        onConfirm={() => {
                            setSelectedProcess(selectedProcess.filter((item: any) => item.id !== data.id));
                        }}
                    >
                        <a>删除</a>
                    </Popconfirm>
                );
            }
        }
    ];
    // 种植过程
    const landColumns = [
        {
            title: '地块名称',
            dataIndex: 'landName',
            key: 'landName',
            ellipsis: true
        },
        {
            title: '种植批次',
            dataIndex: 'plantBatch',
            key: 'plantBatch',
            ellipsis: true
        },
        {
            title: '农作物类型',
            dataIndex: 'plantName',
            key: 'plantName',
            ellipsis: true
        },
        {
            title: '播种时间',
            dataIndex: 'sowTime',
            key: 'sowTime',
            ellipsis: true,
            render: (time: any) => {
                return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
            }
        },
        {
            title: '收割时间',
            dataIndex: 'harvestTime',
            key: 'harvestTime',
            ellipsis: true,
            render: (time: any) => {
                return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
            }
        },
        {
            title: '可加工数量(吨)',
            dataIndex: 'availableNum',
            key: 'availableNum',
            ellipsis: true
        },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            render: (_: any, data: any) => {
                return (
                    <Popconfirm
                        title='确定删除吗?'
                        onConfirm={() => {
                            setSelectedLand(selectedLand.filter((item: any) => item.id !== data.id));
                        }}
                    >
                        <a>删除</a>
                    </Popconfirm>
                );
            }
        }
    ];
    // 收购过程展示列表
    const purchaseBatchColumns = [
        {
            title: '收购批次',
            dataIndex: 'purchaseBatch',
            key: 'purchaseBatch',
            ellipsis: true
        },

        {
            title: '收购时间',
            dataIndex: 'purchaseTime',
            key: 'purchaseTime',
            ellipsis: true,
            render: (time: any) => {
                return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
            }
        },
        // {
        //     title: '大米品种',
        //     dataIndex: 'plantName',
        //     key: 'plantName',
        //     ellipsis: true
        // },
        {
            title: '收割时间',
            dataIndex: 'harvestTime',
            key: 'harvestTime',
            ellipsis: true,
            render: (time: any) => {
                return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
            }
        },
        {
            title: '农作物类型',
            dataIndex: 'plantName',
            key: 'plantName',
            ellipsis: true
        },
        // {
        //     title: '收获时间',
        //     dataIndex: 'purchaseTime',
        //     key: 'purchaseTime',
        //     ellipsis: true,
        //     render: (time: any) => {
        //         return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
        //     }
        // },
        {
            title: '可加工数量(吨)',
            dataIndex: 'availablePurchaseWeight',
            key: 'availablePurchaseWeight',
            ellipsis: true
        }
        // {
        //     title: '操作',
        //     dataIndex: 'action',
        //     key: 'action',
        //     render: (_: any, data: any) => {
        //         return (
        //             <Popconfirm
        //                 title='确定删除吗?'
        //                 onConfirm={() => {
        //                     setSelectedLand(selectedLand.filter((item: any) => item.id !== data.id));
        //                 }}
        //             >
        //                 <a>删除</a>
        //             </Popconfirm>
        //         );
        //     }
        // }
    ];
    const productConfigs = [
        {
            type: 'Input',
            label: '生产批次',
            value: 'productionBatch',
            placeholder: '请输入',
            required: 'required',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback('请输入批次！');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            span: 12,
            className: 'count'
        },
        {
            type: 'Input',
            label: '数量',
            value: 'amount',
            required: 'required',
            // addonAfter: suffixSelector,
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^\+?[1-9]\d{0,9}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入数量！');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            callback('请输入正整数,并在10字符以内');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入数量',
            span: 12,
            className: 'count',
            wide: 280
        },
        {
            type: 'Input',
            label: '生产线',
            value: 'line',
            placeholder: '请输入',
            rules: [
                {
                    max: 50,
                    message: '请保持字符在50字符以内!'
                },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (value && (value[0] == ' ' || value[value.length - 1] == ' ')) {
                            callback('字段前后不能输入空格！');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            wide: 280,
            span: 12
        },
        {
            type: 'Input',
            label: '种植户',
            value: 'grower',
            placeholder: '请输入',
            wide: 280,
            disable: true,

            rules: [
                {
                    max: 50,
                    message: '请保持字符在50字符以内!'
                },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (value && (value[0] == ' ' || value[value.length - 1] == ' ')) {
                            callback('字段前后不能输入空格！');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            span: 12
        },
        {
            type: 'Custom',
            label: '附件',
            value: 'accessory',
            placeholder: '支持扩展名：.rar .zip .doc .docx .pdf .jpg...',
            span: 12,
            children: (
                <Form.Item noStyle>
                    <div className={styles.prodPic}>
                        <Form.Item
                            wrapperCol={{ span: 15 }}
                            name='accessory'
                            rules={[
                                {
                                    required: false,
                                    message: '请上传'
                                }
                            ]}
                            extra={
                                <div style={{ color: '#333', width: 280 }}>
                                    支持扩展名：.rar .zip .doc .docx .pdf .jpg...
                                </div>
                            }
                            valuePropName='fileList'
                            getValueFromEvent={(e: any) => {
                                if (Array.isArray(e)) {
                                    return e;
                                }
                                return e && e.fileList;
                            }}
                        >
                            <Upload
                                accept={productVideoAcceptTypes.join(',')}
                                customRequest={({ file, onError, onProgress, onSuccess }) => {
                                    // @ts-ignore
                                    fileUpload({
                                        ...{ file, onError, onProgress, onSuccess },
                                        isUploading: isUpLoading,
                                        setIsUpLoading: setIsUpLoading
                                    });
                                }}
                                beforeUpload={handleBeforeProductVideoUpload}
                                maxCount={1}
                            >
                                <Button icon={<UploadOutlined rev={undefined} />}>上传文件</Button>
                            </Upload>
                        </Form.Item>
                    </div>
                </Form.Item>
            )
        },
        {
            type: 'Button',
            label: '选择生产过程',
            name: 'changefood',
            value: 'foodlist',
            rules: [{ required: false, message: '请选择食品' }],
            onClick: () => {
                console.log('123321');
                setText('选择生产过程');
                setAddModelVisible(true);
            },
            placeholder: '点击选择',
            color: 'primary',
            span: 12
        },
        {
            // type: 'Button',
            // label: '选择生产过程',
            // name: 'changefood',
            // value: 'foodlist',
            // required: 'required',
            // rules: [{ required: false, message: '请选择食品' }],
            // onClick: () => {
            //     console.log('123321');
            //     setAddModelVisible(true);
            // },
            // placeholder: '点击选择',
            // color: 'primary',
            span: 12
        }
        // {
        //     type: 'Button',
        //     label: `选择种植过程`,
        //     name: 'changefood',
        //     value: 'land',
        //     required: 'requireds',
        //     rules: [{ required: false, message: '请选择种植过程' }],
        //     onClick: () => {
        //         setText('选择种植过程');
        //         setAddModelVisibleland(true);
        //     },
        //     placeholder: '点击选择',
        //     color: 'primary',
        //     span: 12
        // },
        // {
        //     type: 'Button',
        //     label: `选择收购过程`,
        //     name: 'changefood',
        //     value: 'land',
        //     required: 'requireds',
        //     rules: [{ required: false, message: '请选择收购过程' }],
        //     onClick: () => {
        //         setText('选择收购过程');
        //         setAddModelVisibleland(true);
        //     },
        //     placeholder: '点击选择',
        //     color: 'primary',
        //     span: 12
        // }
    ];

    // const materialConfigs =
    //     foodconfig?.data?.data?.map((item: any, index: number) => {
    //         return {
    //             type: 'Select',
    //             label: (
    //                 <BaseTooptip
    //                     data={item?.materialName}
    //                     getPopupContainer={() => document.getElementById('scrollArea')}
    //                 ></BaseTooptip>
    //             ),
    //             value: `${item?.materialName}${index}`,
    //             fields: item?.purchaseList?.map((item: any) => {
    //                 return {
    //                     value: item?.id,
    //                     label: item?.purchaseBatch
    //                 };
    //             }),
    //             span: 12,
    //             placeholder: '请选择',
    //             rules: [{ required: true, message: `请选择原料${index + 1}` }]
    //         };
    //     }) || [];

    // 监听表单变化
    const onFieldsChange = (values: any, errorFields: any) => {
        //限制供货日期必须在生产日期之后
        newdata = SourceCodeForm?.getFieldValue('productionDate');
        // productionDate:生产时间    checkTime:抽样时间   sampleTime:留样时间
        if (
            values[0].name[0] === 'productionDate' ||
            values[0].name[0] === 'checkTime' ||
            values[0].name[0] === 'sampleTime'
        ) {
            //抽样时间
            if (SourceCodeForm?.getFieldValue('productionDate')?.$d >= SourceCodeForm?.getFieldValue('checkTime')?.$d) {
                SourceCodeForm.setFieldsValue({ checkTime: null });
            }
            //留样时间清空
            if (
                SourceCodeForm?.getFieldValue('productionDate')?.$d >= SourceCodeForm?.getFieldValue('sampleTime')?.$d
            ) {
                SourceCodeForm.setFieldsValue({ sampleTime: null });
            }
        }
    };

    const information = (values: any) => {
        if (isUpLoading) {
            message.warning('正在上传文件请稍等～');
            return;
        }
        // if (selectedLand.length == 0) {
        //     message.warning('请选择收购过程');
        //     return;
        // }
        const allKeys = Object.keys(values);
        console.log('values', values);
        const params: any = {
            productId: state?.id,
            // materialInfo: materialConfigs.map((item: any, index: number) => {
            //     return {
            //         materialId: foodconfig?.data?.data?.filter(
            //             (raw: any) => item?.value === `${raw?.materialName}${index}`
            //         )[0]?.materialId,
            //         purchaseId: values[`${item?.value}`]
            //     };
            // }),
            processInfo: selectedProcess.map((item: any) => {
                return item?.id;
            }),
            productionBatch: values?.productionBatch,
            amount: values?.amount,
            line: values?.line,
            grower: values?.grower,
            productionAccessory: getFileUrlFormUploadedFile(values?.accessory)?.[0],
            purchaseBatch: state.purchase_data[0]?.purchaseBatch
            // plantBatch: selectedLand[0].plantBatch
        };
        const paramStr = JSON.stringify(params);
        signData(dispatch, JSON.stringify(params), (error, result: any) => {
            if (!error && result) {
                addproduction.mutate({
                    addProductionVo: params,
                    paramStr: paramStr,
                    signature: result
                });
            } else if (error !== 'misprivatekey') {
                message.info('签名异常，请重试或联系管理员');
            }
        });
    };

    const chooseProcessConfig = {
        okText: '确定',
        title: text,
        visible: text === '选择生产过程' ? addModalVisible : addModalVisibleland,
        setVisible: text === '选择生产过程' ? setAddModelVisible : setAddModelVisibleland,
        width: 1000,
        okHandle: async () => {
            try {
                processForm.resetFields();
                if (text === '选择生产过程') {
                    setAddModelVisible(false);

                    const isSlected = selectedProcess?.some((process: any) => process?.id === processSelect[0]?.id);
                    if (isSlected) {
                        message.warning('生产过程已选择');
                        return;
                    }

                    setSelectedProcess([...selectedProcess, ...processSelect]);
                } else {
                    // console.log(SourceCodeForm?.getFieldValue('amount'));
                    // if (availableNums < Number(SourceCodeForm?.getFieldValue('amount'))) {
                    //     message.warning('您输入的加工数量大于可加工数量，请重新输入');
                    //     return;
                    // }

                    setAddModelVisibleland(false);
                    const isSlectedLand = selectedLand?.some((process: any) => process?.id === landSelect[0]?.id);
                    if (isSlectedLand) {
                        // message.warning('种植过程已选择');
                        message.warning('该收购过程已选择');
                        return;
                    }
                    SourceCodeForm.setFieldsValue({
                        grower: landSelect[0]?.farmerName
                    });
                    // setFarmerName(landSelect[0]?.farmerName);
                    setSelectedLand([...landSelect]);
                }

                querylist.current = null;
                setPageIndex(1);
                setPageSize(5);
            } catch {}
        },
        onCancelHandle: () => {
            setAddModelVisible(false);
            setAddModelVisibleland(false);
            processForm.resetFields();
            setProcessSelect([]);
            querylist.current = null;
            setPageIndex(1);
            setPageSize(5);
        }
    };
    // 种植过程

    // const chooseLandConfig = {
    //     okText: '确定',
    //     title: '选择种植过程',
    //     visible: landModalVisible,
    //     setVisible: setLandModelVisible,
    //     width: 1000,
    //     okHandle: async () => {
    //         try {
    //             setLandModelVisible(false);
    //             processForm.resetFields();
    //             const isSlected = selectedLand?.some((process: any) => process?.id === landSelect[0]?.id);
    //             querylist.current = null;
    //             setPageIndex(1);
    //             setPageSize(5);
    //             setProcessSelect([]);
    //             if (isSlected) {
    //                 message.warning('生产过程已选择');
    //                 return;
    //             }
    //             setSelectedProcess([...selectedLand, ...landSelect]);
    //         } catch {}
    //     },
    //     onCancelHandle: () => {
    //         setLandModelVisible(false);
    //         processForm.resetFields();
    //         setProcessSelect([]);
    //         querylist.current = null;
    //         setPageIndex(1);
    //         setPageSize(5);
    //     }
    // };
    return (
        <BaseCard title={<PageTitle title='新建生产批次' bg='container sheng' />}>
            <Form
                form={SourceCodeForm}
                onFieldsChange={onFieldsChange}
                onFinish={information}
                className='edit-label-title label-title'
            >
                {/* {foodconfig?.data?.data && foodconfig?.data?.data.length > 0 && (
                    <PageTitle title='原料信息' type='primaryIcon' bmagin={16} />
                )} */}
                {/* <FilterForm itemConfig={materialConfigs} size={280} labelCol={6} wrapperCol={9} /> */}

                <PageTitle title='生产信息' type='primaryIcon' bmagin={16} />
                <FilterForm itemConfig={productConfigs} labelCol={6} wrapperCol={9} />

                {/* <Form form={}> */}
                <PageTitle title='生产过程' type='primaryIcon' />
                <BaseTable
                    rowKey='account'
                    style={{ width: 1000 }}
                    className='baseTable-title-nopadding'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return (
                            <TableHead
                                LeftDom={<div></div>}
                                RightDom={
                                    <div
                                        style={{
                                            display: 'flex'
                                        }}
                                    ></div>
                                }
                            />
                        );
                    }}
                    columns={processColumns}
                    dataSource={selectedProcess}
                />
                {/* 种植过程 */}
                {/* <PageTitle title='种植过程' type='primaryIcon' /> */}
                {/* <BaseTable
                    rowKey='account'
                    style={{ width: 1200 }}
                    className='baseTable-title-nopadding'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return (
                            <TableHead
                                LeftDom={<div></div>}
                                RightDom={
                                    <div
                                        style={{
                                            display: 'flex'
                                        }}
                                    ></div>
                                }
                            />
                        );
                    }}
                    columns={landColumns}
                    dataSource={selectedLand}
                /> */}
                <PageTitle title='收购过程' type='primaryIcon' />
                <BaseTable
                    rowKey='account'
                    style={{ width: 1200 }}
                    className='baseTable-title-nopadding'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return (
                            <TableHead
                                LeftDom={<div></div>}
                                RightDom={
                                    <div
                                        style={{
                                            display: 'flex'
                                        }}
                                    ></div>
                                }
                            />
                        );
                    }}
                    columns={purchaseBatchColumns}
                    dataSource={selectedLand}
                />
                {/* </Form> */}

                <div className={styles.saveBtnDiv}>
                    <Form.Item className={styles.saveBtn}>
                        <BaseButton type='primary' htmlType='submit' className={styles.submitBtn}>
                            提交
                        </BaseButton>
                    </Form.Item>
                    <Form.Item>
                        <BaseButton
                            htmlType='button'
                            type='dashed'
                            className={styles.primaryBtn}
                            onClick={() => {
                                navigate('/product/process');
                            }}
                        >
                            取消
                        </BaseButton>
                    </Form.Item>
                </div>
            </Form>

            <BaseModal {...chooseProcessConfig}>
                <Form
                    form={processForm}
                    labelCol={{ span: 8 }}
                    onFinish={(values) => {
                        console.log(values, 'values');
                        setPageIndex(1);
                        console.log('values', values);
                        querylist.current = values;
                        text === '选择生产过程' ? processListQuery.refetch() : purchaseQuery.refetch();
                    }}
                    className='label-title'
                >
                    <Row gutter={[36, 12]}>
                        {text == '选择生产过程' ? (
                            <>
                                <Col span={8}>
                                    <Form.Item label='过程名称' name='processName'>
                                        <BaseInput placeholder='请输入过程名称'></BaseInput>
                                    </Form.Item>
                                </Col>

                                <Col span={8}>
                                    <Form.Item label='创建时间' name='createTime'>
                                        <RangePicker getPopupContainer={(trigger: any) => trigger.parentNode} />
                                    </Form.Item>
                                </Col>
                            </>
                        ) : (
                            <>
                                <Col span={8}>
                                    {/* <Form.Item label='地块名称' name='landName'>
                                        <BaseInput placeholder='请输入地块名称'></BaseInput>
                                    </Form.Item> */}
                                    <Form.Item label='收购批次' name='purchaseBatch'>
                                        <BaseInput placeholder='请输入收购批次'></BaseInput>
                                        {/* <BaseSelect
                                            placeholder='请选择收购批次'
                                            options={landProductListData?.map((item) => ({
                                                label: item?.landName,
                                                value: item?.landId
                                            }))}
                                        ></BaseSelect> */}
                                    </Form.Item>
                                </Col>

                                <Col span={8}>
                                    <Form.Item label='收购时间' name='createTime'>
                                        {/* <BaseSelect
                                            placeholder='请选择农作物类型'
                                            options={typeProductListData?.map((item) => ({
                                                label: item?.plantName,
                                                value: item?.plantName
                                            }))}
                                        ></BaseSelect> */}
                                        <RangePicker
                                            style={{ width: '100%' }}
                                            getPopupContainer={(trigger: any) => trigger.parentNode}
                                        />
                                    </Form.Item>
                                </Col>
                            </>
                        )}
                        <Col span={8}>
                            <BaseButton
                                type='primary'
                                htmlType='submit'
                                style={{ width: 100 }}
                                // className='searchBtn'
                                className={`${styles.searchBtn} ${styles.baseBtn}`}
                                icon={<SearchOutlined rev={undefined} />}
                            >
                                查询
                            </BaseButton>
                        </Col>
                    </Row>
                </Form>
                <Table
                    rowKey='id'
                    rowSelection={{
                        type: 'radio',
                        onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
                            const selectedRow = selectedRows.find((row) => row.id === selectedRowKeys[0]);
                            console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
                            console.log(selectedRow);
                            text === '选择生产过程' ? setProcessSelect(selectedRows) : setLandSelect(selectedRows);
                            // text === '选择生产过程' ? setAvailableNums('') : setAvailableNums(selectedRow.availableNum);
                        },
                        getCheckboxProps: (record) => ({
                            // disabled: record.availableNum == '0' || record.state == '1', // Column configuration not to be checked
                            // availableNum: record.availableNum

                            disabled: record.availablePurchaseWeight == '0' || record.state == '1', // Column configuration not to be checked
                            availablePurchaseWeight: record.availablePurchaseWeight
                        })
                    }}
                    columns={text == '选择生产过程' ? listColumn : listColumnPurchase}
                    dataSource={
                        text == '选择生产过程'
                            ? processListQuery?.data?.data?.records
                            : purchaseQuery?.data?.data?.records
                    }
                    loading={text == '选择生产过程' ? processListQuery?.isLoading : purchaseQuery?.isLoading}
                    pagination={false}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    // showSizeChanger
                    current={pageIndex}
                    pageSize={pageSize}
                    total={
                        text === '选择生产过程' ? processListQuery?.data?.data?.total : purchaseQuery?.data?.data?.total
                    }
                    // onShowSizeChange={handlePaginationChange}
                    onChange={(index, pagesize) => {
                        console.log(index, pagesize);
                        setPageIndex(index);
                        setPageSize(pagesize);
                    }}
                />
            </BaseModal>
        </BaseCard>
    );
};

export default RawMaterialAdd;

/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-13 15:11:03
 * @LastEditTime: 2022-11-02 15:59:24
 * @LastEditors: PhilRandWu
 */

import React from 'react';

import './styles.less';

const PageTitle = (props: {
    title: string;
    type?: string;
    size?: any;
    bmagin?: number;
    bgColor?: string;
    bg?: string;
}) => {
    return (
        <div
            className={`page-title-container ${props.bg ? props.bg : ''}`}
            style={{ marginTop: props?.size, marginBottom: props?.bmagin, background: props?.bgColor }}
        >
            <div className={`title-icon  ${props.bg ? 'containerType' : props?.type}`}></div>
            <div>{props.title}</div>
        </div>
    );
};

export default PageTitle;

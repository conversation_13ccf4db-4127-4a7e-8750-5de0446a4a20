/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-06-29 14:56:29
 * @LastEditTime: 2022-07-19 12:01:15
 * @LastEditors: PhilRandWu
 */
import React, { useState } from 'react';
import { Input, Select } from 'antd';
import type { SelectProps } from 'antd';
import './override.less';
const { Option } = Select;

type optionsConfigType = {
    value?: string | number;
    label: string;
};

interface selectTypes extends SelectProps {
    dropdownRender?: any;
    search?: boolean;
    options?: optionsConfigType[];
}

const BaseSelect = (props: selectTypes) => {
    // const arr=[{label:'参与',value:'参与'}]
    return (
        <Select className='base-select' allowClear getPopupContainer={triggerNode => triggerNode.parentNode} {...props}>
            {props.children}
            {props.options?.map((item) => (
                <Option key={item.value} value={item.value}>
                    {item.label}
                </Option>
            ))}
        </Select>
    );
};

export default React.memo(BaseSelect);

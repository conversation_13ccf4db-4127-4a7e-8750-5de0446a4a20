import BaseButton from '@components/base-button';
import BaseCard from '@components/base-card';
import BaseFormItem from '@components/base-form-item';
import BaseInput from '@components/base-input';
import FilterForm from '@components/filter-form';
import ImgCropUpload from '@components/img-upload';
import PageTitle from '@components/page-title';
import { RoleEnum } from '@config';
import { editBasicInfo, selectParticipantDetail } from '@services/company';
import { useAppSelector } from '@store';
import { updataisFill } from '@store/slice-reducer/user';
import { getLocalPrivatekey } from '@utils/blockChainUtils';
import { ReformChainError } from '@utils/errorCodeReform';
import { Button, Form, Input, message, Upload } from 'antd';
import { useMutation, useQuery } from 'react-query';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { signData } from '../../utils/blockChainUtils';
import styles from './index.module.less';

import { UploadOutlined } from '@ant-design/icons';
import {
    decryJsonRes,
    decryptedUrl,
    decryptStr,
    fileUpload,
    fileUrlsToUploadFileList,
    getFileUrlFormUploadedFile,
    isArrayArr
} from '@utils';
import { useState } from 'react';

const { TextArea } = Input;

const productVideoAcceptTypes = ['.mp4', '.webm', '.mov', '.m4v'];
const handleBeforeProductVideoUpload = (file: any) => {
    const fileType = file?.name?.split('.').at(-1).toLowerCase();
    console.log(fileType, file, 'fffffff');
    if (!productVideoAcceptTypes.includes('.' + fileType)) {
        message.error('文件格式不正确');
        return Upload.LIST_IGNORE;
    }
    if (file.size / 1024 / 1024 > 50) {
        message.error('文件过大');
        return Upload.LIST_IGNORE;
    }
    return true;
};

const BasicInfoEdit = () => {
    const navigate = useNavigate();
    const [editEnterpriseForm] = Form.useForm();
    const dispatch = useDispatch();
    const [isUpLoading, setIsUpLoading]: any = useState();
    const userInfo = useAppSelector((store) => store.user);
    const [promotionPicData, setPromotionPicData] = useState<any>();
    const [qualification, setQualification] = useState<any>();
    const [url, setUrl] = useState<any>();
    const [video, setVideo] = useState<any>();
    console.log('userInfo', userInfo);
    // const user = JSON.parse(localStorage.getItem('userdata') || '');
    const { data } = useQuery(
        [' selectParticipant'],
        () => {
            return selectParticipantDetail({
                orgId: userInfo.userInfo.orgId
            });
        },
        {
            async onSuccess(res) {
                console.log('res===========', res);
                // const qualification= await fileUrlsToUploadFileList([res?.data?.qualification])
                const qualification = await decryptedUrl(res?.data?.qualification);
                console.log('qualification', qualification);
                const qualificationFile: any = await fileUrlsToUploadFileList([qualification]);
                // console.log('qualificationFile',qualificationFile)
                setQualification(qualificationFile);
                const arrayData = await Promise.all(
                    isArrayArr(res?.data?.picture)?.map((item: any) => {
                        return decryptedUrl(item);
                    })
                );
                const pictureArr = await fileUrlsToUploadFileList(arrayData);
                setPromotionPicData(pictureArr);
                const videosDec = await decryptedUrl(res?.data?.video);
                const videos = await fileUrlsToUploadFileList([videosDec]);
                setVideo(videos);
                setUrl(videosDec);
                console.log('qualification===s', qualification);

                //姓名电话解密
                const telephone = await decryptStr(res?.data?.telephone);
                const principal = await decryptStr(res?.data?.principal);
                editEnterpriseForm.setFieldsValue({
                    companyName: res?.data?.companyName,
                    creditCode: res?.data?.creditCode,
                    address: res?.data?.address,
                    principal: principal,
                    email: res?.data?.email,
                    telephone: telephone,
                    introduce: res?.data?.introduce,
                    credentials: res?.data?.qualification && qualification ? qualificationFile : undefined,
                    promotionPic: res?.data?.picture ? pictureArr : undefined,
                    promotionVideo: res?.data?.video ? videos : undefined
                });

                console.log('editEnterpriseForm', editEnterpriseForm.getFieldsValue());

                getLocalPrivatekey(dispatch);
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    const decfn = async (json: any) => {
        return await decryJsonRes(json);
    };
    const decData = decfn(data);
    //编辑
    const foodcategory = useMutation(editBasicInfo, {
        onSuccess(res) {
            sessionStorage.setItem('isFill', '1');
            setTimeout(() => {
                dispatch(updataisFill());
            }, 0);
            message.success('编辑成功');
            // 编辑成功刷下侧边栏

            navigate('/account/basicInfo');
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    const enterpriseConfig = [
        {
            label: '企业名称',
            name: 'companyName',
            value: 'companyName',
            type: 'Input',
            placeholder: '请输入企业/机构名称，不超过80字符',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,80}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入企业/机构名称');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 80) {
                            callback('请保持字符在80字符以内!');
                        } else if (verify === false) {
                            callback('请输入企业名称，支持中文、字母或数字!');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            label: '统一社会信用代码',
            name: 'creditCode',
            value: 'creditCode',
            type: 'Input',
            placeholder: '请输入统一社会信用代码，18字符',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[A-Z0-9]{18}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入统一社会信用代码');
                        } else if (value[0] === ' ' || value[value.length - 1] === ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            callback('请输入正确的统一社会信用代码!');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            label: '地址',
            name: 'address',
            value: 'address',
            type: 'Input',
            placeholder: '请输入地址，不超过80字符',
            rules: [
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{0,80}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback();
                        } else if (value[0] === ' ' || value[value.length - 1] === ' ') {
                            callback('字段前后不能输入空格!');
                        } else if (value.length > 80) {
                            callback('请保持字符在80字符以内!');
                        } else if (verify === false) {
                            callback('请输入地址，支持中文、字母或数字!');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            label: '企业资质',
            name: 'credentials',
            value: 'credentials',
            type: 'Custom',
            children: (
                <Form.Item noStyle>
                    <div className={styles.prodPic}>
                        <Form.Item
                            key='credentials'
                            name='credentials'
                            rules={[
                                {
                                    required: false,
                                    message: '请上传'
                                }
                            ]}
                        >
                            <ImgCropUpload
                                setIsUpLoading={setIsUpLoading}
                                isUpLoading={isUpLoading}
                                isCrop={false}
                                maxAmount={1}
                            ></ImgCropUpload>
                        </Form.Item>
                    </div>
                </Form.Item>
            )
        }
    ];
    const headConfig = [
        {
            label: '姓名',
            name: 'principal',
            type: 'input',
            placeholder: '请输入姓名，不超过30字符',
            rules: [
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{0,30}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback();
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 30) {
                            callback('请保持字符在30字符以内!');
                        } else if (verify === false) {
                            callback('请输入姓名，支持中文、字母或数字!');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            label: '邮箱',
            name: 'email',
            type: 'input',
            placeholder: '请输入邮箱',
            rules: [
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(
                            /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
                        );
                        const regChe = new RegExp(/^[^\u4e00-\u9fa5]+$/);
                        const verify2 = regChe.test(value);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback();
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify2 === false) {
                            callback('邮箱内不能包含中文！');
                        } else if (verify == false) {
                            callback('请输入正确的邮箱！');
                        } else if (value.length > 254) {
                            callback('邮箱长度不能超过254个字符！');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            label: '手机号',
            name: 'telephone',
            type: 'input',
            placeholder: '请输入手机号',
            rules: [
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^(?:(?:\+|00)86)?1[3-9]\d{9}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback();
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            callback('请输入正确的手机号');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        }
    ];
    const businessConfig = [
        {
            label: '企业介绍',
            name: 'introduce',
            value: 'introduce',
            type: 'Custom',
            children: (
                <TextArea
                    showCount
                    maxLength={200}
                    className='summarytextinput'
                    placeholder='请输入公司介绍'
                    style={{ height: 160, resize: 'none' }}
                />
            )
        },
        {
            label: '宣传图片',
            name: 'promotionPic',
            value: 'promotionPic',
            type: 'Custom',
            children: (
                <Form.Item noStyle>
                    <div className={styles.prodPic}>
                        <Form.Item
                            key='promotionPic'
                            name='promotionPic'
                            rules={[
                                {
                                    required: false,
                                    message: '请上传'
                                }
                            ]}
                        >
                            <ImgCropUpload
                                setIsUpLoading={setIsUpLoading}
                                isUpLoading={isUpLoading}
                                isCrop={false}
                                maxAmount={3}
                                tips={'最多支持上传3张图片，大小不超过5MB'}
                            ></ImgCropUpload>
                        </Form.Item>
                    </div>
                </Form.Item>
            )
        },
        {
            type: 'Custom',
            label: '宣传视频',
            value: 'promotionVideo',
            placeholder: '大小不超过50MB，支持mp4、webm、mov、m4v格式',
            children: (
                <Form.Item noStyle>
                    <div className={styles.prodPic}>
                        <Form.Item
                            name='promotionVideo'
                            rules={[
                                {
                                    required: false,
                                    message: '请上传'
                                }
                            ]}
                            extra={<div style={{ color: '#333' }}>大小不超过50MB，支持mp4、webm、mov、m4v格式</div>}
                            valuePropName='fileList'
                            getValueFromEvent={(e: any) => {
                                if (e.fileList[0]?.response?.status == 200) {
                                    setUrl(e.fileList[0]?.response.fileUrl);
                                }
                                if (e.file?.status == 'removed') {
                                    setUrl(null);
                                }
                                if (Array.isArray(e)) {
                                    return e;
                                }
                                return e && e.fileList;
                            }}
                        >
                            <Upload
                                accept={productVideoAcceptTypes.join(',')}
                                customRequest={({ file, onError, onProgress, onSuccess }) => {
                                    // @ts-ignore
                                    fileUpload({
                                        ...{ file, onError, onProgress, onSuccess },
                                        isUploading: isUpLoading,
                                        setIsUpLoading: setIsUpLoading
                                    });
                                }}
                                beforeUpload={handleBeforeProductVideoUpload}
                                maxCount={1}
                            >
                                <Button icon={<UploadOutlined rev={undefined} />}>上传文件</Button>
                            </Upload>
                        </Form.Item>
                    </div>
                </Form.Item>
            )
        }
    ];

    const onFinish = (values: any) => {
        console.log('Success:', values);
        if (isUpLoading) {
            message.warning('正在上传文件请稍等～');
            return;
        }
        const user = JSON.parse(localStorage.getItem('userdata') || '');
        const params: any = {
            address: values?.address,
            companyName: values?.companyName,
            creditCode: values?.creditCode,
            email: values?.email,
            qualification: getFileUrlFormUploadedFile(values?.credentials)?.[0],
            video: url ? url : null,
            picture: getFileUrlFormUploadedFile(values?.promotionPic),
            introduce: values.introduce,
            principal: values.principal,
            telephone: values.telephone
        };
        const paramStr = JSON.stringify(params);
        console.log('params,params', params);
        signData(dispatch, JSON.stringify(params), (error, result: any) => {
            if (!error && result) {
                foodcategory.mutate({
                    orgDetailVo: params,
                    paramStr: paramStr,
                    signature: result
                });
            } else if (error !== 'misprivatekey') {
                message.info('签名异常，请重试或联系管理员');
            }
        });
    };

    const onFinishFailed = (errorInfo: any) => {
        console.log('Failed:', errorInfo);
    };
    console.log('eee---', editEnterpriseForm.getFieldValue('promotionVideo'));

    return (
        <>
            <BaseCard title={<PageTitle title='编辑企业信息' />}>
                {/* <h1>{editEnterpriseForm.getFieldValue('credentials')}</h1> */}
                <Form
                    name='basic'
                    labelCol={{ span: 4 }}
                    wrapperCol={{ span: 8 }}
                    onFinish={onFinish}
                    onFinishFailed={onFinishFailed}
                    autoComplete='off'
                    form={editEnterpriseForm}
                    className='edit-label-title'
                    // labelAlign={'left'}
                >
                    <PageTitle title='企业基础信息' type='primaryIcon' bmagin={16} />
                    <FilterForm itemConfig={enterpriseConfig} labelCol={4} wrapperCol={8} />

                    <PageTitle title='负责人信息' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={headConfig} children={<BaseInput />} />

                    {[RoleEnum.生产加工企业, RoleEnum.销售企业]?.includes(RoleEnum[userInfo.role]) && (
                        <>
                            <PageTitle title='企业简介' type='primaryIcon' bmagin={16} />
                            <FilterForm itemConfig={businessConfig} labelCol={4} wrapperCol={8} />
                        </>
                    )}

                    <div className={styles.btnContainer}>
                        <BaseButton type='primary' className={styles.saveBtn} style={{ width: 120 }} htmlType='submit'>
                            保存
                        </BaseButton>
                        <BaseButton
                            className={styles.cancelBtn}
                            style={{ width: 120 }}
                            onClick={() => {
                                navigate('/account/basicInfo');
                            }}
                        >
                            取消
                        </BaseButton>
                    </div>
                </Form>
            </BaseCard>
        </>
    );
};

export default BasicInfoEdit;

import { useState } from 'react';
import { Col, Form, message, Row, Space } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';

import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';
import BaseInput from '@components/base-input/base-input';
import BaseSelect from '@components/base-select/base-select';
import BaseDatePicker from '@components/base-date-picker';

import styles from './index.module.less';

interface IUrlState {
    pageIndex: number;
    pageSize: number;
    // ...
}

const mock = (params: Partial<IUrlState>) => {
    return new Promise((reslove, reject) => {
        setTimeout(() => {
            reslove([
                {
                    id: ********,
                    account: 'Tom1',
                    time: new Date(),
                    module: '入库管理',
                    type: '新建',
                    content: '编辑了员工A'
                },
                {
                    id: ********,
                    account: 'Tom2',
                    time: new Date(),
                    module: '入库管理',
                    type: '新建',
                    content: '编辑了员工A'
                },
                {
                    id: ********,
                    account: 'Tom3',
                    time: new Date(),
                    module: '入库管理',
                    type: '新建',
                    content: '编辑了员工A'
                },
                {
                    id: ********,
                    account: 'Tom4',
                    time: new Date(),
                    module: '入库管理',
                    type: '新建',
                    content: '编辑了员工A'
                },
                {
                    id: ********,
                    account: 'Tom5',
                    time: new Date(),
                    module: '入库管理',
                    type: '新建',
                    content: '编辑了员工A'
                }
            ]);
        }, 1000);
    });
};

const FleeWarning = () => {
    const [queryParams, setQueryParams] = useState<Partial<IUrlState>>({ pageSize: 10, pageIndex: 1 });

    const [form] = Form.useForm();
    const navigate = useNavigate();

    const queryFleeWarning = useQuery(['queryFleeWarning', queryParams], () => mock(queryParams), {
        onSuccess() {},
        onError() {}
    });

    console.log(queryFleeWarning, 'queryFleeWarning queryFleeWarning');

    const columns: ColumnsType<any> = [
        {
            title: '产品编号',
            dataIndex: 'id',
            key: 'id',
            ellipsis: true
        },
        {
            title: '产品名称',
            dataIndex: 'account',
            key: 'account',
            ellipsis: true
        },
        {
            title: '扫码次数',
            dataIndex: 'module',
            key: 'module',
            ellipsis: true
        },
        {
            title: '窜货次数',
            dataIndex: 'module',
            key: 'module',
            ellipsis: true
        },

        {
            title: '操作',
            render: (_, row) => (
                <BaseButton type='primary' onClick={() => navigate('detail')}>
                    查看详情
                </BaseButton>
            )
        }
    ];

    return (
        <BaseCard className={styles.coreFIrmContainer} title={<PageTitle title='窜货预警报表' />}>
            <Form
                form={form}
                labelCol={{ span: 4 }}
                onFinish={(values) => {
                    console.log(values, 'values');
                }}
            >
                <Row gutter={[36, 12]}>
                    <Col span={8}>
                        <Form.Item label='产品名称' name='name'>
                            <BaseInput placeholder='请输入产品名称'></BaseInput>
                        </Form.Item>
                    </Col>

                    <Col span={8}>
                        <Form.Item label='扫码时间' name='date'>
                            <BaseDatePicker style={{ width: '100%' }}></BaseDatePicker>
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <div style={{ display: 'flex', justifyContent: 'end' }}>
                            <Space>
                                <BaseButton type='primary' htmlType='submit'>
                                    查询
                                </BaseButton>
                                <BaseButton
                                    onClick={() => {
                                        form.resetFields();
                                    }}
                                >
                                    重置
                                </BaseButton>
                            </Space>
                        </div>
                    </Col>
                </Row>
            </Form>
            <BaseTable
                loading={queryFleeWarning.isFetching}
                columns={columns}
                dataSource={(queryFleeWarning.data as unknown[]) || []}
            />
            <BasePagination
                shouldShowTotal
                showQuickJumper
                showSizeChanger
                current={1}
                pageSize={10}
                total={100}
                onShowSizeChange={() => {}}
                onChange={() => {}}
            />
        </BaseCard>
    );
};

export default FleeWarning;

FROM node:14.18-alpine AS build
WORKDIR /app
COPY package-lock.json ./
COPY package.json ./
RUN npm config set registry https://registry.npmmirror.com/
RUN npm install
COPY . .
RUN npm run build
# COPY build/* build/web/

FROM nginx:alpine AS final
COPY  --from=build /app/nginx/app.conf /etc/nginx/conf.d/app.template
COPY  --from=build /app/build /usr/share/nginx/html
ENV CM_RICETRACE_WEB_SERVER_HOST=http://**********:9099
ENV CM_RICETRACE_WEB_HOST=http://**********:9099
ENV RICETRACE_WEB_URL=http://**********:9099
EXPOSE 80
WORKDIR /etc/nginx/conf.d/
ENTRYPOINT envsubst '${CM_RICETRACE_WEB_SERVER_HOST} ${CM_RICETRACE_WEB_HOST}' < app.template > app.conf && cat app.conf && cp /usr/share/nginx/html/config.json /usr/share/nginx/html/config.template && envsubst '${RICETRACE_WEB_URL}' < /usr/share/nginx/html/config.template > /usr/share/nginx/html/config.json && nginx -g 'daemon off;'

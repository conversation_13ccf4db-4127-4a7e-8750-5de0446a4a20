.app-loading {
    width: 100%;
    margin-top: 200px;
}
.ant-modal-content {
    border-radius: 5px !important;
}
.ant-modal-header {
    border-radius: 5px 5px 0 0 !important; /* 只对左上角和右上角设置圆角 */
}
.employees_header > div.ant-modal-content > div.ant-modal-header > div.ant-modal-title {
    position: relative;
    left: 33px;
}
.modal {
    .ant-modal-header {
        border-bottom: 0px solid #f0f0f0;
        background: #76ae55 !important;
    }
    .ant-modal-footer {
        border-top: 0px solid #f0f0f0;
    }
    .modle-title {
        font-family: PingFangSC-Regular, PingFang SC;
        position: absolute;
        top: 14%;
        left: 6.5%;
        color: #fa5151;
    }
    .modle-titles {
        font-family: PingFangSC-Regular, PingFang SC;
        position: absolute;
        top: 14%;
        left: 4.5%;
        color: #fa5151;
    }
    .none {
        display: none;
    }
}
.ant-modal-header {
    border-bottom: 0px solid #f0f0f0;
    background: #76ae55 !important;
}
.ant-modal-title {
    color: #fff !important;
}
.ant-modal-footer {
    .ant-btn {
        background: #969696;
        color: #fff;
    }
    .ant-btn:hover,
    .ant-btn:focus {
        background: #969696;
        color: #fff;
        border: 1px solid #969696;
    }
}

.ant-modal-confirm-btns {
    .ant-btn {
        background: #969696;
        color: #fff;
    }
    .ant-btn:hover,
    .ant-btn:focus {
        background: #969696;
        color: #fff;
        border: 1px solid #969696;
    }
}

.ant-modal-close-x {
    color: #ffffff;
}

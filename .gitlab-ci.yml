image: docker:latest

stages:
    - build

job_deploy:
    stage: build
    services:
        - docker:dind
    only:
        - dev
    before_script:
        - echo 'docker deploy'
    script:
        - docker build -t ut-web .
        - docker save ut-web -o ut-web.tar
        - tar -czf ut-web.tar.gz ut-web.tar
        - ls -a
        - eval $(ssh-agent -s)
        - echo "$SSH_PRIVATE_KEY" > ~/deploy.key
        - chmod 0600 ~/deploy.key
        - ssh-add ~/deploy.key
        - mkdir -p ~/.ssh
        - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
        - ls -a
        - scp -oPort=17382 -c aes128-ctr -r ut-web.tar.gz $CUSTOM_USERNAME@$CUSTOM_IP:/home/<USER>/zlt/cm-ut
        - ssh -oPort=17382 $CUSTOM_USERNAME@$CUSTOM_IP "cd /home/<USER>/zlt/cm-ut && tar -xzf ut-web.tar.gz && docker images && docker stop ut-web || true && docker rm ut-web || true && docker load -i ut-web.tar && docker run --name ut-web -p 8015:80 -e 'CM_RICETRACE_WEB_SERVER_HOST=$CM_RICETRACE_WEB_SERVER_HOST_V2' -e 'CM_RICETRACE_WEB_HOST=$WEB_HOST' -d ut-web && rm ut-web.tar.gz"
    tags:
        - webTestRunner
    retry: 0

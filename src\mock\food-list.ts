// /*
//  * @Description:
//  * @Author: PhilRandWu
//  * @Github: https://github/PhilRandWu
//  * @Date: 2022-06-28 10:36:58
//  * @LastEditTime: 2022-11-01 18:29:53
//  * @LastEditors: PhilRandWu
//  */
// import Mock, { Random } from 'mockjs';

// Mock.Random.extend({
//     phone: function () {
//         const phonePrefixs = ['132']; // 自己写前缀哈
//         return this.pick(phonePrefixs) + Mock.mock(/\d{8}/); //Number()
//     }
// });

// export const requestfoodList = () => {
//     const requestList = new Array(10).fill(1).map((item, index) => {
//         return Mock.mock({
//             no: '@phone',
//             name: Random.string(5),
//             'code|100': 1,
//             'type|3': ['粮油调味'],
//             status: Random.boolean()
//         });
//     });
//     return new Promise((resolve, reject) => {
//         setTimeout(() => {
//             resolve(requestList);
//         }, 1000);
//     });
// };

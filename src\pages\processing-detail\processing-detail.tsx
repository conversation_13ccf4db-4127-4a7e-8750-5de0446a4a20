/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-11 11:04:25
 * @LastEditTime: 2022-11-01 18:15:51
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BaseCollapse from '@components/base-collapse';
import BaseFormItem from '@components/base-form-item';
import BaseModal from '@components/base-modal';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import { Form, message } from 'antd';
import React, { useRef, useState } from 'react';
import { productionBatchDetail, productionDetail } from '@services/production';
import { useMutation, useQuery } from 'react-query';
import { useLocation } from 'react-router-dom';
import { ProductInfoConfig, chainInfoConfig } from './config';
import { ReformChainError } from '@utils/errorCodeReform';
import BaseTooptip from '@components/base-tooltip';
import dayjs from 'dayjs';
import ChainDetailModal from '@components/chain_detail_modal';
import { decryptedUrl } from '@utils';
// import './index.less'

function ProductDetail() {
    const { state } = useLocation();
    console.log('state', state);
    const productionlist: any = useRef('');
    const [SourceForm] = Form.useForm();
    const [upfile, setupfile]: any = useState('');
    const [ProductForm] = Form.useForm();
    const [ChainForm] = Form.useForm();

    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    const [productionAccessory, setProductionAccessory] = useState<any>();

    const productionBatchdetail = useQuery(
        ['productionBatchDetail'],
        () => {
            return productionBatchDetail({
                id: state.data.productionId
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            },
            async onSuccess(res) {
                console.log('productionAccessory--', res?.data);
                const productionAccessory = await decryptedUrl(res?.data?.productionAccessory);
                setProductionAccessory(productionAccessory);
            }
        }
    );
    // console.log(890999999,productiondetail)
    console.log('productiondetail?.data?.data?.materialInfo', productionBatchdetail?.data?.data?.materialInfo);
    //原料信息
    const SourceInfoConfig = productionBatchdetail?.data?.data?.materialList
        ? [
              ...productionBatchdetail?.data?.data?.materialList?.map((item: any, index: any) => {
                  // console.log(7777777,item)
                  const material = {
                      value: item?.materialId,
                      // label: item.materialName,
                      label: (
                          <BaseTooptip
                              changeplay='true'
                              data={item?.materialName}
                              slice={true}
                              maxWidth={200}
                              sliceData={`${
                                  typeof item?.materialName === 'string' &&
                                  item?.materialName &&
                                  item?.materialName.length > 8
                                      ? item?.materialName?.slice(0, 8)?.trim() + '...'
                                      : item?.materialName
                              }`}
                          ></BaseTooptip>
                      ),
                      name: item?.materialName,
                      type: 'Display',
                      span: 8,
                      displayDom: item?.purchaseBatch,
                      purchaseId: item?.purchaseId
                  };
                  return material;
              })
          ]
        : [];

    const ProductInfoConfig = [
        {
            label: '产品名称',
            name: 'code',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: productionBatchdetail?.data?.data?.productName || '-'
            // size: 15
        },
        {
            label: '生产批次',
            name: 'time',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: productionBatchdetail?.data?.data?.productionBatch || '-'
        },
        {
            label: '数量',
            name: 'time',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: productionBatchdetail?.data?.data?.amount || '-'
        },
        {
            label: '生产线',
            name: 'time',
            type: 'Display',
            value: 'productionLine',
            span: 8,
            // show: productiondetail?.data?.data?.productionLine ? null : '1',
            displayDom: productionBatchdetail?.data?.data?.line || '-'
        },
        {
            type: 'Display',
            name: 'time',
            label: '种植户',
            value: 'productionShift',
            span: 8,
            // show: productiondetail?.data?.data?.productionShift ? null : '1',
            displayDom: productionBatchdetail?.data?.data?.grower || '-'
        },
        // 二期去除
        // {
        //     label: '生产日期',
        //     name: 'package',
        //     value: '123',
        //     type: 'Display',
        //     span: 12,
        //     size: 15,
        //     displayDom: dayjs(productiondetail?.data?.data?.productionDate).format('YYYY-MM-DD')
        // },
        // {
        //     type: 'Display',
        //     name: 'time',
        //     label: '生产地点',
        //     value: 'productionPlace',
        //     span: 12,
        //     show: productiondetail?.data?.data?.productionPlace ? null : '1',
        //     displayDom: productiondetail?.data?.data?.productionPlace
        // },
        // {
        //     type: 'Display',
        //     name: 'time',
        //     label: '生产环境信息',
        //     value: 'environmentInfo',
        //     span: 12,
        //     show: productiondetail?.data?.data?.environmentInfo ? null : '1',
        //     displayDom: productiondetail?.data?.data?.environmentInfo
        // },
        // {
        //     type: 'Display',
        //     name: 'time',
        //     label: '责任人员',
        //     value: 'personLiable',
        //     span: 12,
        //     show: productiondetail?.data?.data?.personLiable ? null : '1',
        //     displayDom: productiondetail?.data?.data?.personLiable
        // },
        // {
        //     type: 'Display',
        //     name: 'time',
        //     label: '联系电话',
        //     value: 'contactNumber',
        //     span: 12,
        //     show: productiondetail?.data?.data?.contactNumber ? null : '1',
        //     displayDom: productiondetail?.data?.data?.contactNumber
        // },
        // {
        //     type: 'DatePicker',
        //     name: 'time',
        //     label: '抽检时间',
        //     value: 'checkTime',
        //     span: 12,
        //     show: productiondetail?.data?.data?.checkTime ? null : '1',
        //     displayDom: dayjs(productiondetail?.data?.data?.checkTime).format('YYYY-MM-DD')
        // },
        // {
        //     type: 'Display',
        //     name: 'time',
        //     label: '抽检记录',
        //     value: 'checkRecord',
        //     span: 12,
        //     show: productiondetail?.data?.data?.checkRecord ? null : '1',
        //     displayDom: productiondetail?.data?.data?.checkRecord
        // },
        // {
        //     type: 'DatePicker',
        //     name: 'time',
        //     label: '留样时间',
        //     value: 'sampleTime',
        //     span: 12,
        //     show: productiondetail?.data?.data?.sampleTime ? null : '1',
        //     displayDom: dayjs(productiondetail?.data?.data?.sampleTime).format('YYYY-MM-DD')
        // },
        // {
        //     type: 'Display',
        //     name: 'time',
        //     label: '留样记录',
        //     value: 'sampleRecord',
        //     span: 12,
        //     show: productiondetail?.data?.data?.sampleRecord ? null : '1',
        //     displayDom: productiondetail?.data?.data?.sampleRecord
        // },
        {
            label: '附件',
            name: 'time',
            value: '123',
            type: 'Url',
            span: 8,
            // show: productiondetail?.data?.data?.productionAccessory ? null : '1',
            display: productionAccessory ? <a href={productionAccessory}>{'下载'}</a> : '-'
        }
    ];

    const ChainDetailModalConfig = {
        transactionId: productionBatchdetail?.data?.data?.transactionId,
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };

    const valuesForm = SourceForm.getFieldsValue();
    const chainInfoConfig = [
        // {
        //     label: '区块号',
        //     name: 'blockNum',
        //     value: '123',
        //     type: 'Display',
        //     write: 'true',
        //     span: 24,
        //     tooltip: '信息上链时所在的区块编号',
        //     size: 15,
        //     displayDom: productiondetail?.data?.data?.blockNum
        // },
        {
            label: '链上哈希',
            name: 'transactionId',
            value: '123',
            type: 'custom',
            write: 'true',
            span: 24,
            tooltip: '信息的链上的哈希值',
            children: productionBatchdetail?.data?.data?.transactionId ? (
                <a onClick={() => setChainDetailModalVisible(true)} style={{ color: '#76ae55' }}>
                    {productionBatchdetail?.data?.data?.transactionId}
                </a>
            ) : (
                '-'
            )
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: '123',
            write: 'true',
            type: 'Display',
            span: 24,
            tooltip: '信息上链的时间',
            displayDom: productionBatchdetail?.data?.data?.transactionTime
                ? dayjs(productionBatchdetail?.data?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
                : '-'
        }
    ];
    // ChainForm.setFieldsValue({
    //     blockNum: productiondetail?.data?.data?.blockNum,
    //     transactionId: productiondetail?.data?.data?.transactionId,
    //     transactionTime: productiondetail?.data?.data?.transactionTime
    //         ? dayjs(productiondetail?.data?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
    //         : null
    // });
    return (
        <>
            <BaseCard title={<PageTitle title='生产批次详情' bg='container sheng' />}>
                {productionBatchdetail?.data?.data?.materialList &&
                    productionBatchdetail?.data?.data?.materialList.length > 0 && (
                        <Form form={SourceForm}>
                            <PageTitle title='原料信息' type='primaryIcon' bmagin={16} />
                            <BaseFormItem configs={SourceInfoConfig} handle={() => {}} />
                        </Form>
                    )}
                <Form form={ProductForm}>
                    <PageTitle title='生产加工信息' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={ProductInfoConfig} />
                </Form>
                <Form form={ProductForm}>
                    <PageTitle title='生产过程' type='primaryIcon' />

                    <BaseTable
                        rowKey='account'
                        style={{ width: 800, marginBottom: 50 }}
                        className='baseTable-title-nopadding'
                        columns={[
                            {
                                title: '过程编号',
                                dataIndex: 'id',
                                key: 'id'
                            },
                            {
                                title: '过程名称',
                                dataIndex: 'processName',
                                key: 'processName',
                                width: 300
                            },
                            {
                                title: '创建时间',
                                dataIndex: 'createTime',
                                key: 'createTime',
                                width: 300,
                                ellipsis: true,
                                render: (time: any) => {
                                    return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
                                }
                            }
                        ]}
                        dataSource={productionBatchdetail?.data?.data?.processList}
                    />
                </Form>
                {/* 收购 */}
                <Form form={ProductForm}>
                    <PageTitle title='收购过程' type='primaryIcon' />
                    <BaseTable
                        rowKey='account'
                        style={{ width: 1300, marginBottom: 50 }}
                        className='baseTable-title-nopadding'
                        columns={[
                            {
                                title: '收购批次',
                                dataIndex: 'purchaseBatch',
                                key: 'purchaseBatch',
                                ellipsis: true
                            },
                            {
                                title: '收购时间',
                                dataIndex: 'purchaseTime',
                                key: 'purchaseTime',
                                ellipsis: true,
                                render: (time) => {
                                    return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '';
                                }
                            },
                            {
                                title: '农作物类型',
                                dataIndex: 'plantName',
                                key: 'plantName',
                                ellipsis: true
                            },

                            {
                                title: '收割时间',
                                dataIndex: 'harvestTime',
                                key: 'harvestTime',
                                ellipsis: true,
                                render: (time) => {
                                    return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '';
                                }
                            },
                            {
                                title: '加工数量(吨)',
                                dataIndex: 'productionNum',
                                key: 'productionNum',
                                ellipsis: true
                            }
                            // {
                            //     title: '可加工数量(吨)',
                            //     dataIndex: 'availablePurchaseWeight',
                            //     key: 'availablePurchaseWeight',
                            //     ellipsis: true
                            // }
                        ]}
                        dataSource={productionBatchdetail?.data?.data?.materialPurchaseList}
                    />
                </Form>

                <Form form={ProductForm}>
                    <PageTitle title='种植过程' type='primaryIcon' />
                    <BaseTable
                        rowKey='account'
                        style={{ width: 1300, marginBottom: 50 }}
                        className='baseTable-title-nopadding'
                        columns={[
                            {
                                title: '地块名称',
                                dataIndex: 'landName',
                                key: 'landName',
                                ellipsis: true
                            },
                            {
                                title: '种植批次',
                                dataIndex: 'plantBatch',
                                key: 'plantBatch',
                                ellipsis: true
                            },
                            {
                                title: '农作物类型',
                                dataIndex: 'plantName',
                                key: 'plantName',
                                ellipsis: true
                            },

                            {
                                title: '播种时间',
                                dataIndex: 'sowTime',
                                key: 'sowTime',
                                ellipsis: true,
                                render: (time) => {
                                    return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
                                }
                            },
                            {
                                title: '收割时间',
                                dataIndex: 'harvestTime',
                                key: 'harvestTime',
                                ellipsis: true,
                                render: (time) => {
                                    return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '';
                                }
                            }
                            // {
                            //     title: '可加工数量',
                            //     dataIndex: 'availableNum',
                            //     key: 'availableNum',
                            //     ellipsis: true
                            // }
                        ]}
                        dataSource={productionBatchdetail?.data?.data?.landPlantBatchList}
                    />
                </Form>
                <Form form={ChainForm}>
                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={chainInfoConfig} />
                </Form>
            </BaseCard>
            <ChainDetailModal {...ChainDetailModalConfig} />
        </>
    );
}

export default ProductDetail;

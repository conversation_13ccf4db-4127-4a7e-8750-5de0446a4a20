import moment from "moment";

/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 18:07:30
 * @LastEditTime: 2022-10-12 10:00:49
 * @LastEditors: PhilRandWu
 */
export const addConfigs = [
    {
        type: 'Select',
        label: '原料名称',
        value: 'name',
        rules: [{ required: true, message: '请输入原料名称!' }],
        placeholder: '请输入原料名称',
        span: 12,
        fields: [
            {
                value: '12发给3',
                label: '123vfv123'
            },
            {
                value: '12dfvdfdf3',
                label: '1231vdfvdfvdd23'
            },
            {
                value: '12ddvsdfsffgnf3',
                label: '123gfbgfbfgb123'
            }
        ]
    },
    {
        type: 'Input',
        label: '数量',
        value: 'count',
        rules: [{ required: true, message: '请输入数量!' }],
        placeholder: '请输入数量',
        span: 12
    },
    {
        type: 'Input',
        label: '批次号',
        value: 'batchNumber',
        rules: [{ required: true, message: '请输入批次号!' }],
        placeholder: '请输入批次号',
        span: 12
    },
    {
        type: 'Input',
        label: '规格',
        value: 'specification',
        rules: [{ required: true, message: '请输入规格!' }],
        placeholder: '请输入规格',
        span: 12
    },
    {
        type: 'DatePicker',
        label: '生产日期',
        value: 'productionDate',
        rules: [{ required: true, message: '请输入!' }],
        placeholder: '请输入',
        span: 12
    },
    {
        type: 'DatePicker',
        label: '保质期',
        value: 'expirationDate',
        rules: [{ required: true, message: '请输入!' }],
        placeholder: '请输入',
        span: 12
    },
    {
        type: 'Input',
        label: '生产批号',
        value: 'productionNumber',
        placeholder: '请输入',
        span: 12
    },
    {
        type: 'DatePicker',
        label: '供货日期',
        value: 'num',
        placeholder: '请输入',
        span: 12
    },
    {
        type: 'UploadFile',
        label: '产品合格证明材料',
        value: 'num',
        rules: [{ required: true, message: '请输入!' }],
        placeholder: '最大可上传20M,不限制类型',
        span: 12
    },
    {
        type: 'UploadFile',
        label: '附件',
        value: 'num',
        placeholder: '最大可上传20M,不限制类型',
        span: 12
    }
];

export function disabledDateTime() {
    const currentDate = moment(); // 获取当前日期和时间

    return {
        disabledHours: () => generateOptions(0, currentDate.hour()),
        disabledMinutes: () => generateOptions(0, currentDate.minute()),
        disabledSeconds: () => generateOptions(0, currentDate.second()),
    };
}

function generateOptions(from: any, to: any) {
    const options = [];
    for (let i = from; i <= to; i++) {
        options.push(i);
    }
    return options;
}
.base-card {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 2;
    .ant-card {
        // background: linear-gradient(180deg, #f5f7f9 0%, #ffffff 100%);
        // box-shadow: 0px 7px 7px 0px #e5eaf1, -4px 0px 13px 0px #ffffff;
        border-radius: 4px;
        // border: 2px solid #ffffff;

        .ant-card-head-title {
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #3d73ef;
        }
    }
}
.base-card-mt24 {
    margin-top: 24px;
}
.mai {
    position: absolute;
    right: -20px;
    top: -30px;
    width: 120px;
    z-index: 99;
}

// .page-title-container{
//   color: @blueBtn !important;
//   padding-left: 30px;
//   background: url('../../assets/landicon/title.png') no-repeat;
//   background-size: 150px  100%;
// }
// .page-title-container .title-icon{
//   background: transparent !important;
// }

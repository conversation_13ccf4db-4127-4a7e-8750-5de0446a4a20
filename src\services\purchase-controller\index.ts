/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-11-01 18:02:21
 * @LastEditTime: 2022-11-01 18:02:21
 * @LastEditors: PhilRandWu
 */
import request from '../request';
//原料采购分页接口
export const purchasePage = (obj: any) => {
    return request({
        url: '/purchase/page',
        method: 'post',
        data: obj
    });
};
//原料采购详情
export const purchaseDetail = (obj: any) => {
    return request({
        url: `/purchase/detail`,
        method: 'get',
        params: obj
    });
};
//作废
export const cancelPurchase = (obj: any) => {
    return request({
        url: `purchase/cancel`,
        method: 'post',
        data: obj
    });
};
//新增采购信息
export const addPurchase = (obj: any) => {
    return request({
        url: '/purchase/add',
        method: 'post',
        data: obj
    });
};
//可用原料列表
export const validMaterialList = (obj: any) => {
    return request({
        url: '/material/validMaterialList',
        method: 'post',
        data: obj
    });
};
//原料采购列表
export const purchaseBatchList = (obj: any) => {
    return request({
        url: `/purchase/purchaseBatchList?materialId=${obj.materialId}`,
        method: 'post',
        data: obj
    });
};
// 原料列表
export const materialNameList = (obj: any) => {
    return request({
        url: `/material/list`,
        method: 'get',
        params: obj
    });
};
import request from './request';
//文件上传接口
export const getUploadUrl = async (fileName: string) => {
    const {data} = await request({
        method: "get",
        url: "/minio/get-url",
        params: {
            fileName
        }
    });
    return data;
};
//文件预览接口
export const getPreviewUrl = async (url : string) => {
    const {data} = await request({
        method: "get",
        url: "/minio/get-presigned-url",
        params: {
            url
        }
    });
    return data;
};
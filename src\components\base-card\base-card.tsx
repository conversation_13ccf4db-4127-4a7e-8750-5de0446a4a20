/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-06-28 22:18:58
 * @LastEditTime: 2022-10-13 14:49:45
 * @LastEditors: PhilRandWu
 */
import React from 'react';
import { Card } from 'antd';
import type { CardProps } from 'antd';
import Imai from './mai.png';
import './override.less';

interface IProps extends CardProps {
    mt24?: boolean;
    title?: any;
}

const BaseCard = (props: IProps) => {
    // console.log('props7789', props);
    const { className, style, ...reset } = props;
    return (
        <div className={`base-card ${props.mt24 && 'base-card-mt24'} ${props?.className} baseCard`} style={style}>
            {/* <img src={Imai} alt='' className='mai' /> */}
            {/* {props?.title} */}
            <Card {...reset} />
        </div>
    );
};

export default React.memo(BaseCard);

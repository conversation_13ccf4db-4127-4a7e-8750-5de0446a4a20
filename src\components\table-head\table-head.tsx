import React, { ReactElement } from 'react';
import styles from './index.module.less';

interface tableHeadPropsInterFace {
    type?: 'left' | 'right' | 'spaceBetween';
    LeftDom?: ReactElement | null;
    RightDom?: ReactElement | null;
}

const TableHead = ({ type = 'spaceBetween', LeftDom, RightDom }: tableHeadPropsInterFace) => {
    return (
        <div className={styles[type]}>
            {LeftDom}
            {RightDom}
        </div>
    );
};

export default TableHead;

import BaseInput from '@components/base-input';
import { Col, Form, Input, Row, Tooltip } from 'antd';
import React, { ReactElement, useRef } from 'react';
import BaseTooptip from '@components/base-tooltip';
import './index.less';
interface itemInterface {
    children?: any;
    value?: any;
    label: string;
    name: string;
    span?: number;
    type?: string;
    tips?: string;
    status?: string;
    tooltip?: string;
    displayDom?: any;
    size?: any;
    rules?: any;
    show?: any;
    write?: any;
    display?: any;
    placeholder?: any;
    input?: any;
}

interface propsInterface {
    configs: itemInterface[];
    children?: ReactElement;
    handle?: () => void;
}

export default function BaseFormItem({ configs, children, handle }: propsInterface) {
    const urlRef: any = useRef();
    const details: any = useRef();
    const statusRef: any = useRef();
    const handleRef: any = useRef();
    return (
        <Row>
            {configs.map((item) => {
                let displayDom = children;
                if (item?.type === 'Display') {
                    displayDom = (
                        <BaseInput
                            disabled={true}
                            // placeholder={item?.placeholder}
                            // SuccessStatus ErrorStatus
                            className={`displayInput ${item?.status}`}
                        />
                    );
                } else if (item?.type === 'custom') {
                    displayDom = item?.children ? item.children : '-';
                } else if (item?.type === 'input') {
                    displayDom = (
                        <BaseInput
                            // disabled={true}
                            placeholder={item?.placeholder}
                            // SuccessStatus ErrorStatus
                            // className={`displayInput ${item?.status}`}
                        />
                    );
                } else if (item?.type === 'Url') {
                    displayDom = (
                        <a ref={urlRef} href={urlRef?.current?.getAttribute('value')}>
                            {item?.tips ? item?.tips : '查看详情'}
                        </a>
                    );
                } else if (item?.type === 'Status') {
                    displayDom = (
                        <p ref={statusRef} className={item?.status}>
                            {statusRef?.current?.getAttribute('value')}
                        </p>
                    );
                } else if (item?.type === 'Modal') {
                    displayDom = (
                        <a
                            ref={handleRef}
                            onClick={(e: any) => {
                                e.preventDefault();
                                handle && handle();
                            }}
                        >
                            {item?.tips ? item?.tips : '查看详情'}
                        </a>
                    );
                    // displayDom = <BaseInput
                    //     disabled={true}
                    //     // SuccessStatus ErrorStatus
                    //     className={`displayInput ${item?.status}`}
                    //     onClick={() => {
                    //         console.log("Modal")
                    //         handle && handle()
                    //     }}
                    //      />
                }

                return (
                    <Col span={item?.span ? item?.span : 24} className={item?.show ? 'show_1' : 'BaseFormItem'}>
                        <Form.Item
                            key={item.name}
                            label={item.label}
                            rules={item?.rules}
                            name={item.name}
                            tooltip={item?.tooltip}
                            style={{ marginTop: item?.size }}
                        >
                            {/* {item?.displayDom} */}
                            {item?.write && item?.displayDom ? (
                                <BaseTooptip
                                    data={item?.displayDom}
                                    slice={true}
                                    maxWidth={200}
                                    sliceData={`${
                                        typeof item?.displayDom === 'string' &&
                                        item?.displayDom &&
                                        item?.displayDom.length > 24
                                            ? item?.displayDom?.slice(0, 66)?.trim()
                                            : item?.displayDom
                                    }`}
                                ></BaseTooptip>
                            ) : item?.displayDom ? (
                                <BaseTooptip
                                    data={item?.displayDom}
                                    slice={true}
                                    maxWidth={200}
                                    sliceData={`${
                                        typeof item?.displayDom === 'string' &&
                                        item?.displayDom &&
                                        item?.displayDom.length > 24
                                            ? item?.displayDom?.slice(0, 16)?.trim() + '...'
                                            : item?.displayDom
                                    }`}
                                ></BaseTooptip>
                            ) : item?.display ? (
                                <p>{item?.display}</p>
                            ) : (
                                // displayDom
                                displayDom
                            )}
                            {/* <p>{item?.displayDom ? item?.displayDom : displayDom}</p> */}
                            {/* {item?.text} */}
                        </Form.Item>
                    </Col>
                );
            })}
        </Row>
    );
}

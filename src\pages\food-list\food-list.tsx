/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-09 16:17:21
 * @LastEditTime: 2022-10-14 10:15:21
 * @LastEditors: PhilRandWu
 */
/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-10-09 16:16:29
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space, Button } from 'antd';
import { foodPage, FoodState } from '@services/food';
import useUrlState from '@ahooksjs/use-url-state';
import { useMutation, useQuery } from 'react-query';
import styles from './index.module.less';
import { useRef, useState } from 'react';
import PageTitle from '@components/page-title';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { useFoodList } from '../../myhooks/usefoodlist';
import copyToClipboard from 'copy-to-clipboard';
import BaseInput from '@components/base-input';
import SingleSearch from '@components/single-search';
import { Navigate, useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import { ReformChainError } from '@utils/errorCodeReform';
import { ColumnsType } from 'antd/lib/table';
import SalesChannelModal from './sales-channel-modal';
const FoodList = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;
    const navigate = useNavigate();
    const [addEmployeesForm] = Form.useForm();
    const [editEmployeesForm] = Form.useForm();
    const querylist = useRef('');

    // 销售渠道弹窗相关状态
    const [salesChannelVisible, setSalesChannelVisible] = useState(false);
    const [currentProduct, setCurrentProduct] = useState<any>(null);

    const queryList: any = useFoodList({
        pageIndex: pageInfo.pageIndex,
        pageSize: pageInfo.pageSize
    });

    console.log('queryList', queryList);
    //修改状态
    const foodstate = useMutation(FoodState, {
        onSuccess(res) {
            message.success('修改状态成功');
            foodquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            foodquery.refetch();
        }
    });
    const foodquery = useQuery(
        ['foodquery', pageInfo],
        () => {
            // if (!userInfo.user?.organization_id) {
            //     message.error('未获取到机构id');
            //     return Promise.reject('未获取到机构id');
            // }
            return foodPage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                param: querylist?.current
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    console.log('staffquery', foodquery);
    const tableData = foodquery?.data?.data?.records;
    const listColumn: ColumnsType<any> = [
        {
            title: '产品编号',
            dataIndex: 'id',
            key: 'no',
            ellipsis: true
        },
        {
            title: '产品名称',
            dataIndex: 'productName',
            key: 'name',
            ellipsis: true
        },
        // {
        //     title: '产品编码',
        //     dataIndex: 'code',
        //     key: 'code',
        //     ellipsis: true
        // },
        {
            title: '产品品类',
            dataIndex: 'productCategory',
            key: 'type',
            ellipsis: true
        },
        {
            title: '产品信息',
            dataIndex: 'operation',
            key: 'operation',
            ellipsis: true,
            render: (data: any, record: any) => (
                <span
                    className={styles.configBtn}
                    onClick={() => {
                        navigate('infoConfig', {
                            state: {
                                id: record
                            }
                        });
                    }}
                >
                    配置
                </span>
            )
        },
        {
            title: '溯源码信息',
            dataIndex: 'operation',
            key: 'operation',
            ellipsis: true,
            render: (data: any, record: any) => (
                <span
                    className={styles.configBtn}
                    onClick={() => {
                        navigate('source', {
                            state: {
                                id: record?.id
                            }
                        });
                    }}
                >
                    配置
                </span>
            )
        },
        {
            title: '状态',
            dataIndex: 'state',
            key: 'state',
            ellipsis: true,
            render: (data: any) => (
                <span style={{ color: data ? '#F64041' : '#666666' }}>
                    <Badge
                        status={data ? 'error' : 'success'}
                        color={data ? '#F64041' : 'rgb(36, 171, 59)'}
                        text={data ? '禁用' : '可用'}
                    />
                </span>
            )
        },
        {
            width: 330,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle' className='operation'>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            setCurrentProduct(record);
                            setSalesChannelVisible(true);
                        }}
                    >
                        维护销售渠道
                    </BaseButton>
                    <BaseButton
                        // type='dashed'
                        className={record.state ? 'primaryBtn' : 'warnBtn'}
                        // ghost
                        onClick={() => {
                            // console.log("record",record)
                            const opp = foodstate.mutate({
                                opt: record?.state ? 'ENABLE' : 'DISABLE',
                                id: record?.id
                            });
                        }}
                    >
                        {record.state ? '启用' : '禁用'}
                    </BaseButton>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            const foodid = record?.id;
                            navigate('detail', {
                                state: {
                                    id: foodid
                                }
                            });
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    const searchConfig = {
        label: '',
        classname: 'searchConfig-input',
        handleSearch: (values: any) => {
            handlePaginationChange(1);
            foodquery.refetch();
        },
        placeholder: '输入产品编号/产品名称',
        setSearchValue: (values: any) => {
            // console.log("values",values)
            querylist.current = values;
        }
    };

    return (
        <>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
                title={<PageTitle title='产品列表' bg='container chan' />}
            >
                <BaseTable
                    rowKey='account'
                    className='food-table-operation'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return (
                            <TableHead
                                LeftDom={<div></div>}
                                RightDom={
                                    <div
                                        style={{
                                            display: 'flex',
                                            marginBottom: '20px'
                                        }}
                                    >
                                        <SingleSearch {...searchConfig} />
                                        <BaseButton
                                            type='dashed'
                                            icon={<PlusOutlined rev={undefined} />}
                                            className='bgBtn'
                                            onClick={() => {
                                                navigate('add');
                                            }}
                                        >
                                            新建产品
                                        </BaseButton>
                                    </div>
                                }
                            />
                        );
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={foodquery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={foodquery?.data?.data?.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>

            {/* 销售渠道维护弹窗 */}
            <SalesChannelModal
                visible={salesChannelVisible}
                onCancel={() => {
                    setSalesChannelVisible(false);
                    setCurrentProduct(null);
                }}
                product={currentProduct}
                onSuccess={() => {
                    setSalesChannelVisible(false);
                    setCurrentProduct(null);
                    // 可以在这里刷新列表或显示成功消息
                    message.success('销售渠道维护成功');
                }}
            />
        </>
    );
};

export default WithPaginate(FoodList);

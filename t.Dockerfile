# syntax=docker/dockerfile:1
# FROM node:12-alpine AS build
# WORKDIR /app
# COPY package.json package-lock.json ./
# RUN npm install
# COPY . .
# RUN npm run build
# RUN npm run build

FROM nginx:alpine
COPY nginx/app.conf /etc/nginx/conf.d/app.template
COPY build /usr/share/nginx/html
ENV CM_RICETRACE_WEB_SERVER_HOST=http://**********:3923
ENV FT_WEB_URL=http://baidu.com
EXPOSE 80
WORKDIR /etc/nginx/conf.d/
ENTRYPOINT envsubst '${CM_RICETRACE_WEB_SERVER_HOST}' < app.template > app.conf && cat app.conf && cp /usr/share/nginx/html/config.json /usr/share/nginx/html/config.template && envsubst '${FT_WEB_URL}' < /usr/share/nginx/html/config.template > /usr/share/nginx/html/config.json && nginx -g 'daemon off;'
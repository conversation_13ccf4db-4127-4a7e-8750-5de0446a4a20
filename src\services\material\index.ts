/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-11-01 18:29:13
 * @LastEditTime: 2022-11-01 18:29:13
 * @LastEditors: PhilRandWu
 */

import request from '../request';
//原料列表
export const materialPage = (obj: any) => {
    return request({
        url: '/material/getMaterialList',
        method: 'post',
        data: obj
    });
};
//修改原料状态
export const modiMater = (obj: any) => {
    return request({
        url: `/material/updateState`,
        method: 'post',
        data: obj
    });
};
//新增原料
export const addMaterial = (obj: any) => {
    return request({
        url: '/material/addMaterial',
        method: 'post',
        data: obj
    });
};
//可用食品表
export const alidFoodList = (obj: any) => {
    return request({
        url: '/product/list',
        method: 'get',
        params: obj
    });
};
//供应商列表
export const supplierList = () => {
    return request({
        url: '/orgPart/getSupplierList',
        method: 'get',
        params: {}
    });
};
//编辑原料
export const updateMaterial = (obj: any) => {
    return request({
        url: '/material/modifyMaterial',
        method: 'post',
        data: obj
    });
};
// 产品选择列表
export const selectProductList = (obj: any) => {
    return request({
        url: '/product/list',
        method: 'get',
        params: obj
    });
};
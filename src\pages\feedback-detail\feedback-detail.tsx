/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 17:15:57
 * @LastEditTime: 2022-11-01 18:12:36
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BaseFormItem from '@components/base-form-item';
import PageTitle from '@components/page-title';
import React, { useState } from 'react';
import { Form, message } from 'antd';
import { useMutation, useQuery } from 'react-query';
import { foodDetail } from '@services/food';
import { enumeration } from './config';
import SwitchList from '@components/switch-list';
import styles from './index.module.less';
import { useLocation } from 'react-router-dom';
import { ReformChainError } from '@utils/errorCodeReform';
import dayjs from 'dayjs';
import { Image } from 'antd';
import FilterForm from '@components/filter-form';

import { FormItemImages, FormItemVideo } from '@components';
import ChainDetailModal from '@components/chain_detail_modal';
import { decryptedUrl, isArrayArr } from '@utils';
import { getSalesDetail } from '@services/land-test';
import { getScoreDetail } from '@services/score';

const FoodDetail = (props: any) => {
    const { state } = useLocation();
    const [detailForm] = Form.useForm();
    const [detailsForm] = Form.useForm();
    const [channelInformation] = Form.useForm();
    const [basicForm] = Form.useForm();
    const [returnForm] = Form.useForm();
    const [chainForm] = Form.useForm();

    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    console.log('state', state);

    const detailquery = useQuery(
        ['detailquery9999'],
        () => {
            return getScoreDetail({
                productId: state?.id
            });
        },
        {
            async onSuccess(res) {
                console.log('res0990099', res);
                const arrayData = await Promise.all(
                    isArrayArr(res?.data?.productImg)?.map((item: any) => {
                        return decryptedUrl(item);
                    })
                );
                // setPromotionPicData(arrayData)
                // const videos = await decryptedUrl(res?.data?.productVideo);
                // detailForm.setFieldsValue({
                //   ...res.data,
                //   productImg: arrayData && arrayData.length > 0 ? arrayData : null,
                //   productVideo: videos ? videos : null,
                //   transactionTime: res?.data?.transactionTime
                //     ? dayjs(res?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
                //     : '-'
                // });
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    console.log('detailquery', detailquery?.data?.data);
    const fooddetail = detailquery?.data?.data?.food;
    // 系统功能
    const feedbackToData = detailquery?.data?.data?.feedbackTo;

    // 商品质量
    const ReturnData = detailquery?.data?.data?.productFeedbackTo;
    // 其他建议
    const OtherDetailData = detailquery?.data?.data?.otherFeedbackTo;

    const chainData = detailquery?.data?.data?.chainData;

    // 系统反馈
    const feedbackToConfig = [
        {
            label: '反馈类型',
            name: 'type',
            value: 'type',
            type: 'ShowText'
        },
        {
            label: '具体功能',
            name: 'issues',
            value: 'issues',
            type: 'ShowText',
            maxWidth: 400
        }
    ];

    // 商品信息
    const returnInformation = [
        {
            label: '反馈类型',
            name: 'type',
            value: 'type',
            type: 'ShowText'
        },
        {
            label: '问题描述',
            name: 'content',
            value: 'content',
            type: 'ShowText',
            maxWidth: 400
        },
        {
            label: '企业处理意见',
            name: 'result',
            value: 'result',
            type: 'ShowText',
            maxWidth: 400
        }
    ];

    // 其他建议
    const productionConfig = [
        {
            label: '反馈类型',
            name: 'type',
            value: 'type',
            type: 'ShowText'
        },
        {
            label: '问题描述',
            name: 'content',
            value: 'content',
            type: 'ShowText',
            maxWidth: 400
        },
        {
            label: '处理意见',
            name: 'result',
            value: 'result',
            type: 'ShowText',
            maxWidth: 400
        }
    ];

    // 其他建议
    const chainConfig = [
        {
            label: '联系电话',
            name: 'phone',
            value: 'phone',
            type: 'ShowText'
        },

        {
            label: '反馈时间',
            name: 'createTime',
            value: 'createTime',
            type: 'ShowText'
        }
    ];
    //系统反馈
    channelInformation.setFieldsValue({
        type: feedbackToData?.type,
        issues: feedbackToData?.issues
    });

    // 商品质量
    returnForm.setFieldsValue({
        type: ReturnData?.type ? ReturnData?.type : '-',
        content: ReturnData?.content ? ReturnData?.content : '-',
        result: ReturnData?.result ? ReturnData?.result : '-',
        applyTime: ReturnData?.applyTime ? dayjs(ReturnData?.applyTime).format('YYYY-MM-DD HH:mm:ss') : '-'
    });

    // 其他信息
    detailsForm.setFieldsValue({
        type: OtherDetailData?.type ? OtherDetailData?.type : '-',
        content: OtherDetailData?.content ? OtherDetailData?.content : '-',
        result: OtherDetailData?.result ? OtherDetailData?.result : '-',
        applyTime: OtherDetailData?.applyTime ? dayjs(OtherDetailData?.applyTime).format('YYYY-MM-DD HH:mm:ss') : '-'
    });
    chainForm.setFieldsValue({
        createTime: detailquery?.data?.data?.createTime
            ? dayjs(detailquery?.data?.data?.createTime).format('YYYY-MM-DD HH:mm:ss')
            : '-',
        phone: detailquery?.data?.data?.phone ? detailquery?.data?.data?.phone : '-'
    });
    const onFinish = (values: any) => {
        console.log('Success:', values);
    };

    const onFinishFailed = (errorInfo: any) => {
        console.log('Failed:', errorInfo);
    };
    console.log('detailquery?.data?.data?.material', detailquery?.data?.data?.material);
    return (
        <div>
            <BaseCard title={<PageTitle title='用户反馈详情' bg='container fei' />}>
                {feedbackToData && (
                    <Form form={channelInformation}>
                        <PageTitle title='反馈类型' type='primaryIcon' bmagin={16} />
                        <FilterForm showMode itemConfig={feedbackToConfig} labelCol={false} />
                    </Form>
                )}
                {ReturnData && (
                    <Form form={returnForm}>
                        <PageTitle title='反馈类型' type='primaryIcon' bmagin={16} />
                        <FilterForm showMode itemConfig={returnInformation} labelCol={false} />
                    </Form>
                )}
                {OtherDetailData && (
                    <Form form={detailsForm}>
                        <PageTitle title='反馈类型' type='primaryIcon' bmagin={16} />
                        <FilterForm showMode itemConfig={productionConfig} labelCol={false} />
                    </Form>
                )}

                <Form form={chainForm}>
                    <FilterForm showMode itemConfig={chainConfig} labelCol={false} />
                </Form>

                {/* <Form
          name='basic'
          form={detailForm}
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          autoComplete='off'
        >
          <PageTitle title='食品信息' type='primaryIcon' />
                    <BaseFormItem configs={feedbackToConfig} />
          <PageTitle title='渠道信息' type='primaryIcon' bmagin={16} />
          <FilterForm showMode itemConfig={feedbackToConfig} labelCol={false} />

          <PageTitle title='订单详情信息' type='primaryIcon' bmagin={16} />
          <FilterForm showMode itemConfig={productionConfig} labelCol={false} />
          <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
          <FilterForm showMode itemConfig={chainConfig} labelCol={false} />
        </Form> */}
            </BaseCard>
            <ChainDetailModal
                transactionId={chainData?.transactionId}
                open={ChainDetailModalVisible}
                onCancel={() => setChainDetailModalVisible(false)}
            />
        </div>
    );
};

export default FoodDetail;

import { useState } from 'react';
import { Col, Form, message, Row, Space, Badge, Card } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useLocation, useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';

import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';
import BaseInput from '@components/base-input/base-input';
import BaseSelect from '@components/base-select/base-select';
import BaseDatePicker from '@components/base-date-picker';
import { DownOutlined, UpOutlined } from '@ant-design/icons';

import styles from './index.module.less';
import { RoleEnum } from '@config';
import { Enum2Object } from '@utils/enum';
import SourceLogisticsService, {
    SourceLogisticsStateEnum,
    SourceLogisticsTypeEnum
} from '@services/traceability_data/source_logistics';
import { useAppSelector } from '@store';
import { QueryTime } from '@utils';
import PublicSourceService from '@services/traceability_data/public_source';

interface IUrlState {
    pageIndex: number;
    pageSize: number;
    // ...
}

const Inspect = () => {
    const userInfo = useAppSelector((store) => store.user);
    const LocalLoginIdentity = Number(userInfo?.userInfo?.identity);
    const [queryParams, setQueryParams] = useState<any>({ pageSize: 10, pageIndex: 1 });

    const [form] = Form.useForm();
    const navigate = useNavigate();

    const [isSimpleSearch, setIsSimpleSearch] = useState(true);

    const queryOrgList = useQuery(['queryOrgList'], () =>
        PublicSourceService.getOrgList({
            identity: RoleEnum['物流企业']
        })
    );
    const queryOrgListData: any[] = queryOrgList?.data?.data;

    const queryCoreOrgList = useQuery(['queryCoreOrgList'], () => PublicSourceService.getCoreList(), {
        enabled: [RoleEnum.平台方]?.includes(LocalLoginIdentity)
    });
    const queryCoreOrgListData: any[] = queryCoreOrgList?.data?.data;

    const queryLogisticsList = useQuery(
        ['queryLogisticsList', queryParams],
        () => SourceLogisticsService.Query(queryParams),
        {
            onSuccess() {},
            onError() {}
        }
    );

    console.log('queryLogisticsList', queryLogisticsList);
    const queryLogisticsListData = queryLogisticsList?.data?.data;
    const coreEnterpriseColumns = () => {
        return [RoleEnum.平台方].includes(LocalLoginIdentity)
            ? [
                  {
                      title: '生产加工企业',
                      dataIndex: 'coreName',
                      key: 'coreName',
                      ellipsis: true
                  }
              ]
            : [];
    };

    const columns: ColumnsType<any> = [
        {
            title: '运输单号',
            dataIndex: 'transNumber',
            key: 'transNumber',
            ellipsis: true
        },
        {
            title: '运输类型',
            dataIndex: 'type',
            key: 'type',
            ellipsis: true,
            render(type) {
                if (SourceLogisticsTypeEnum.自行运输 === type) return '自行运输';
                if (SourceLogisticsTypeEnum.委托运输 === type) return '委托运输';
            }
        },
        // {
        //     title: '企业名称',
        //     dataIndex: 'shortName',
        //     key: 'shortName',
        //     ellipsis: true
        // },
        {
            title: '操作人',
            dataIndex: 'optName',
            key: 'optName',
            ellipsis: true
        },
        ...coreEnterpriseColumns(),
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true,
            render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
        },
        {
            title: '状态',
            dataIndex: 'state',
            key: 'state',
            ellipsis: true,
            render(state) {
                if (SourceLogisticsStateEnum.运输中 === state) return <Badge status='processing' text='运输中' />;
                if (SourceLogisticsStateEnum.已完成 === state) return <Badge status='success' text='已完成' />;
                if (SourceLogisticsStateEnum.已作废 === state) return <Badge status='error' text='已作废' />;
            }
        },
        {
            title: '哈希',
            dataIndex: 'transactionId',
            key: 'transactionId',
            ellipsis: true
        },
        {
            title: '操作',
            render: (_, row: any, index) => (
                <BaseButton
                    type='dashed'
                    className='primaryBtn'
                    onClick={() =>
                        navigate(`detail/${row.id}`, {
                            state: {
                                type: row.type,
                                state: row.state
                            }
                        })
                    }
                >
                    查看详情
                </BaseButton>
            )
        }
    ];

    const searchFormItems = [
        <Form.Item label='运输单' name='transNumber'>
            <BaseInput placeholder='请输入运输单号'></BaseInput>
        </Form.Item>,
        <Form.Item label='运输类型' name='type'>
            <BaseSelect placeholder='请选择' options={Enum2Object(SourceLogisticsTypeEnum)}></BaseSelect>
        </Form.Item>,
        <Form.Item label='创建时间' name='createTime'>
            <BaseDatePicker style={{ width: '100%' }}></BaseDatePicker>
        </Form.Item>,
        <Form.Item label='状态' name='state'>
            <BaseSelect placeholder='请选择' options={Enum2Object(SourceLogisticsStateEnum)}></BaseSelect>
        </Form.Item>,
        // <Form.Item label='企业名称' name='orgId'>
        //     <BaseSelect
        //         placeholder='请选择'
        //         options={queryOrgListData?.map((item) => ({
        //             label: item?.shortName,
        //             value: item?.id
        //         }))}
        //     ></BaseSelect>
        // </Form.Item>,
        ...([RoleEnum.平台方].includes(LocalLoginIdentity)
            ? [
                  <Form.Item label='生产加工企业' name='coreId'>
                      <BaseSelect
                          placeholder='请选择'
                          options={queryCoreOrgListData?.map((item) => ({
                              label: item?.shortName,
                              value: item?.id
                          }))}
                      ></BaseSelect>
                  </Form.Item>
              ]
            : [
                  <Form.Item label='生产加工企业' name='coreId'>
                      <BaseSelect
                          placeholder='请选择'
                          options={queryCoreOrgListData?.map((item) => ({
                              label: item?.shortName,
                              value: item?.id
                          }))}
                      ></BaseSelect>
                  </Form.Item>
              ])
    ];

    return (
        <>
            <Card style={{ marginBottom: 10 }} title={<PageTitle title='运输单列表' />}>
                <Form
                    form={form}
                    labelCol={{ span: 5 }}
                    labelAlign='left'
                    className='label-title label-title-more'
                    onFinish={(values) => {
                        console.log(values, 'values');
                        const TimeArr = QueryTime(values?.createTime);
                        setQueryParams({
                            ...values,
                            transNumber: values?.transNumber?.trim(),
                            startTime: TimeArr?.[0],
                            endTime: TimeArr?.[1],
                            pageIndex: 1,
                            pageSize: 10
                        });
                    }}
                >
                    <Row gutter={[36, 12]}>
                        {searchFormItems.slice(0, isSimpleSearch ? 2 : searchFormItems.length).map((searchFormItem) => (
                            <Col key={searchFormItem.key} span={8}>
                                {searchFormItem}
                            </Col>
                        ))}
                        <Col span={[RoleEnum.平台方].includes(LocalLoginIdentity) && !isSimpleSearch ? 24 : 8}>
                            <div style={{ display: 'flex', justifyContent: 'end' }}>
                                <Space>
                                    <BaseButton type='primary' htmlType='submit'>
                                        查询
                                    </BaseButton>
                                    <BaseButton
                                        onClick={() => {
                                            form.resetFields();
                                            setQueryParams({
                                                pageIndex: 1,
                                                pageSize: 10
                                            });
                                        }}
                                    >
                                        重置
                                    </BaseButton>
                                    <BaseButton
                                        style={{ color: '#80a932' }}
                                        type='link'
                                        onClick={() => {
                                            setIsSimpleSearch(!isSimpleSearch);
                                        }}
                                    >
                                        {isSimpleSearch ? '展开' : '收起'}
                                        {isSimpleSearch ? (
                                            <DownOutlined rev={undefined} />
                                        ) : (
                                            <UpOutlined rev={undefined} />
                                        )}
                                    </BaseButton>
                                </Space>
                            </div>
                        </Col>
                    </Row>
                </Form>
            </Card>
            <BaseCard className={styles.coreFIrmContainer}>
                <BaseTable
                    loading={queryLogisticsList.isLoading}
                    columns={columns}
                    dataSource={queryLogisticsListData?.records || []}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={queryParams.pageIndex}
                    pageSize={queryParams.pageSize}
                    total={queryLogisticsListData?.total}
                    onShowSizeChange={(page, pageSize) => {
                        setQueryParams({
                            ...queryParams,
                            pageIndex: page,
                            pageSize
                        });
                    }}
                    onChange={(page, pageSize) => {
                        setQueryParams({
                            ...queryParams,
                            pageIndex: page,
                            pageSize
                        });
                    }}
                />
            </BaseCard>
        </>
    );
};

export default Inspect;

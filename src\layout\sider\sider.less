.sider-container {
    background: #101845;
    background-image: url('../../assets/icon/sider-background1.png');
    color: #0a1135;
    background-repeat: no-repeat;
    background-size: cover;
    // background-position: center bottom -17px;
    background-position: center bottom;
    // position: relative;
    // padding-bottom: 40px;
    .ant-menu-inline .ant-menu-item::after {
        border-right: 3px solid #76ae55;
    }
    .ant-menu-sub.ant-menu-inline {
        background: rgba(255, 255, 255, 0.3) !important;
    }
    // .ant-menu-inline.ant-menu-root .ant-menu-item,
    // .ant-menu-inline.ant-menu-root .ant-menu-submenu-title {
    //       background: rgba(255, 255, 255, 0.3) !important;
    // }

    .ant-menu-submenu-title {
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.3) !important;
    }
    .sider-header {
        display: flex;
        justify-content: center;
        padding: 20px 0 23px 0;
        height: 84px;
        background: rgba(255, 255, 255, 0.3) !important;
        margin-bottom: 20px;
        .sider-header-logo {
            width: 42px;
            margin-right: 8px;
        }
        .sider-header-app-name {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            line-height: 41px;
            height: 41px;
        }
    }
    // 允许滚动
    // .ant-layout-sider-children {
    //     max-height: 100vh;
    //     display: flex;
    //     flex-direction: column;
    //     .ant-menu.ant-menu-dark {
    //         flex: 1;
    //         max-height: 100vh;
    //         overflow: hidden auto;
    //         &::-webkit-scrollbar {
    //             width: 6px;
    //             height: 6px;
    //         }
    //         &::-webkit-scrollbar-thumb {
    //             background-image: initial;
    //             background-color: #0a1135;
    //             box-shadow: rgb(15 28 41 / 5%) 0px 0px 5px inset;
    //             border-radius: 6px;
    //         }
    //         &::-webkit-scrollbar-track {
    //             background-color: transparent;
    //         }
    //     }
    // }

    .sider-menu-icon {
        width: 24px;
        height: 24px;
        //opacity: 0.5;
        //background: linear-gradient(180deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.5) 100%);
    }
    // antd sider 样式调整
    .ant-menu.ant-menu-inline-collapsed > .ant-menu-item,
    .ant-menu.ant-menu-inline-collapsed > .ant-menu-item-group > .ant-menu-item-group-list > .ant-menu-item,
    .ant-menu.ant-menu-inline-collapsed
        > .ant-menu-item-group
        > .ant-menu-item-group-list
        > .ant-menu-submenu
        > .ant-menu-submenu-title,
    .ant-menu.ant-menu-inline-collapsed > .ant-menu-submenu > .ant-menu-submenu-title {
        padding: 0 calc(50% - 24px / 2);
    }
    .ant-menu-dark .ant-menu-inline.ant-menu-sub {
        background-color: transparent;
    }
    .ant-menu.ant-menu-dark {
        background-color: transparent;
    }
    .ant-menu-dark.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item-selected {
        background-color: #76ae55;
    }
    .ant-menu-title-content {
        font-size: 18px;
        font-weight: 400;
        // color: #a5a4bf;
        color: #757575;
        // display: flex;
        // align-items: center;
    }
    .ant-menu-sub .ant-menu-title-content {
        font-size: 16px;
    }
    .ant-menu-submenu-selected .ant-menu-submenu-title .ant-menu-title-content,
    .ant-menu-item-selected .ant-menu-title-content {
        // color: white;
        color: #000;
    }
    // 子菜单对齐
    .ant-menu-submenu .ant-menu-sub .ant-menu-item *:first-child {
        margin-left: -6px;
    }
    // antd sider 样式调整 end
    .bottom-font {
        // position: absolute;
        height: 30px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        color: #757575;
        // font-weight: 500;
        width: 100%;
        bottom: 18px;
    }
}
.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
    background: url('./sider.png') no-repeat;
    background-size: 100% 100%;
}
.sider-container .sider-header {
    background: url('../../assets/landicon/sider-background.png') no-repeat !important;
    background-size: 100% 100%;
}
.bottomDev {
    height: 60px;
    // margin-top: 90%;
    position: absolute;
    bottom: 30px;
}

.siderMenu {
    background: rgba(255, 255, 255, 0.3) !important;
    margin-bottom: 90px;
    height: 65vh;
    overflow-y: auto;
    /* 隐藏滚动条 */
    &::-webkit-scrollbar {
        /* 整个滚动条 */
        width: 0; /* 隐藏滚动条宽度 */
    }

    &::-webkit-scrollbar-track {
        /* 轨道 */
        background: transparent; /* 设置背景颜色为透明 */
    }

    &::-webkit-scrollbar-thumb {
        /* 滚动条滑块 */
        background: transparent; /* 设置滑块颜色为透明 */
    }
}
.ant-menu-item {
    display: flex;
    align-items: center;
}
.ant-menu-submenu:hover > .ant-menu-submenu-title > .ant-menu-submenu-expand-icon,
.ant-menu-submenu:hover > .ant-menu-submenu-title > .ant-menu-submenu-arrow {
    color: #76ae55;
}

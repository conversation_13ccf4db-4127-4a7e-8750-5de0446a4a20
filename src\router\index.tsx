import { useRoutes, Navigate, resolvePath } from 'react-router-dom';
import type { RouteObject } from 'react-router-dom';

import { useAppSelector } from '@store';
import { openRoutes, authRoutes } from './routers';

import Layout from '@layout';

import type { IUserState } from '@store/slice-reducer/user';
const PageNoPermission = () => (
    <div
        style={{
            display: 'flex',
            height: '100vh',
            fontSize: '20px'
            // fontWeight: 'bold'
        }}
    >
        权限变动请手动进入有权限的菜单
    </div>
);
const PagePermissionLoading = () => (
    <div
        style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100vh',
            fontSize: '16px'
        }}
    >
        权限加载中...
    </div>
);
const PageNotFound = () => <div>404</div>;

// 递归标记路由及其所有子路由为无权限
const markRouteAsNoPermission = (route: any): any => {
    const { element, children, ...restRouteInfo } = route;
    return {
        ...restRouteInfo,
        element: <PageNoPermission></PageNoPermission>,
        isNoPermission: true,
        children: children ? children.map((child: any) => markRouteAsNoPermission(child)) : undefined
    };
};

export const getInitialRoute: any = (config: any) => {
    for (let i = 0; i < config.length; i++) {
        const curRoute = config[i];
        if (
            curRoute.isNoPermission ||
            curRoute.isPermissionLoading ||
            (curRoute.children &&
                curRoute.children.every(
                    (item: any) => item.isNoPermission === true || item.isPermissionLoading === true
                ))
        ) {
            continue;
        }
        if (curRoute?.children) {
            return getInitialRoute(
                curRoute.children.map((item: any) => {
                    return { ...item, path: resolvePath(item.path || '', curRoute.path).pathname };
                })
            );
        } else {
            return curRoute.path;
        }
    }
};

export const getCurUserAuthRoute = (
    userMenuPermissions: string[],
    userInfo: IUserState,
    isPermissionLoaded: boolean
) => {
    const getRoutes = (routes: any) => {
        return routes.map((route: any) => {
            const getRouteCanAuth = (canAuth: string | ((userInfo: IUserState) => boolean)) => {
                if (typeof canAuth === 'string') {
                    return userMenuPermissions.includes(route.canAuth);
                } else {
                    return route.canAuth(userInfo);
                }
            };
            console.log(userMenuPermissions, 'userMenuPermissions');

            // 如果权限还未加载完成，显示加载状态
            if (!isPermissionLoaded) {
                const { element, children, ...restRouteInfo } = route;
                return {
                    ...restRouteInfo,
                    // element: <PagePermissionLoading></PagePermissionLoading>,
                    isPermissionLoading: true
                };
            }

            // 权限已加载，进行正常的权限判断
            let canUseRoute: boolean;
            if (route.canAuth !== undefined) {
                // 需要权限检查的路由
                if (!userMenuPermissions || userMenuPermissions.length === 0) {
                    // 如果权限列表为空，且已经加载完成，说明用户没有任何权限
                    canUseRoute = false;
                } else {
                    canUseRoute = getRouteCanAuth(route.canAuth);
                }
            } else {
                // 不需要权限检查的路由，直接允许访问
                canUseRoute = true;
            }

            if (canUseRoute) {
                if (route.children) {
                    return {
                        ...route,
                        children: getRoutes(route.children)
                    };
                } else {
                    return route;
                }
            } else {
                const { element, children, ...restRouteInfo } = route;
                return {
                    ...restRouteInfo,
                    // element: <PageNoPermission></PageNoPermission>,
                    element: <PageNotFound></PageNotFound>,
                    isNoPermission: true,
                    // 如果有子路由，也需要递归将所有子路由标记为无权限
                    children: children ? children.map((child: any) => markRouteAsNoPermission(child)) : undefined
                };
            }
        });
    };

    return getRoutes(authRoutes);
};

const Routes = () => {
    const userInfo = useAppSelector((store) => store.user); //侧边导航栏列表

    // 使用状态中的权限加载标识
    const isPermissionLoaded = !userInfo.isLogin || userInfo.isPermissionLoaded;

    const getRoutes = () => {
        const routes: RouteObject[] = [...openRoutes];
        const authRouteConfig = getCurUserAuthRoute(userInfo.menuPermissionList, userInfo, isPermissionLoaded);
        console.log('authRouteConfig', authRouteConfig, userInfo.menuPermissionList, userInfo);
        if (userInfo.isLogin) {
            routes.push({
                path: '',
                element: <Layout></Layout>,
                children: [
                    {
                        index: true,
                        element: <Navigate replace to={getInitialRoute(authRouteConfig)}></Navigate>
                    },
                    ...authRouteConfig,
                    {
                        path: '*',
                        // element: isPermissionLoaded ? <PageNotFound /> : null
                        element: <PageNotFound />
                    }
                ]
            });
        } else {
            routes.push({
                path: '*',
                element: <Navigate replace to={'/login'}></Navigate>
            });
        }
        return routes;
    };

    return useRoutes(getRoutes());
};

export default Routes;

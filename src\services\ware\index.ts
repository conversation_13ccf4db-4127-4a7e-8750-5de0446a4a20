import request from '../request';
// 仓储管理列表

export const warehouseList = (obj: any) => {

  return request({
      url: '/warehouse/page',
      method: 'post',
      data: obj
  });
};
// 仓储信息详情

export const getWareDetail = (obj: any) => {
  return request({
      url: `/warehouse/detail`,
      method: 'get',
      params: obj
  });
};


//作废
export const cancelWare = (obj: any) => {
  return request({
      url: `/warehouse/cancel`,
      method: 'get',
      params: obj
  });
};

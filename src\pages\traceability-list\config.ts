/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:27:42
 * @LastEditTime: 2022-10-12 18:01:15
 * @LastEditors: PhilRandWu
 */
export const addTraceabilityConfigs = [
    {
        label: '食品',
        type: 'Select',
        value: 'foodname',
        placeholder: '请选择',
        rules: [{ required: true, message: '请选择!' }],
        fields: [
            {
                value: '12发给3',
                label: '123vfv123'
            },
            {
                value: '12dfvdfdf3',
                label: '1231vdfvdfvdd23'
            },
            {
                value: '12ddvsdfsffgnf3',
                label: '123gfbgfbfgb123'
            }
        ]
    },
    {
        label: '生产批次',
        type: 'Select',
        value: 'production',
        placeholder: '请选择',
        rules: [{ required: true, message: '请选择!' }],
        fields: [
            {
                value: '12发给3',
                label: '123vfv123'
            },
            {
                value: '12dfvdfdf3',
                label: '1231vdfvdfvdd23'
            },
            {
                value: '12ddvsdfsffgnf3',
                label: '123gfbgfbfgb123'
            }
        ]
    },
    {
        label: '溯源码数量',
        type: 'InputNumber',
        value: 'name',
        placeholder: '请输入溯源码数量',
        rules: [{ required: true, message: '请输入溯源码数量!' }],
        wide:292
    }
];

export const editEmployeesConfigs = [
    {
        label: '员工名称',
        type: 'Input',
        value: 'name',
        placeholder: 'erferferf',
        rules: [{ required: true, message: 'Please input your username!' }]
    },
    {
        label: '联系方式',
        type: 'Input',
        value: 'name',
        placeholder: 'erferferf',
        rules: [{ required: true, message: 'Please input your username!' }]
    },
    {
        label: '管理员账号',
        type: 'TagsSelect',
        value: 'account',
        placeholder: 'fefer',
        rules: [{ required: true, message: 'Please input your username!' }],
        fields: [
            {
                value: '12发给3',
                label: '123vfv123'
            },
            {
                value: '12dfvdfdf3',
                label: '1231vdfvdfvdd23'
            },
            {
                value: '12ddvsdfsffgnf3',
                label: '123gfbgfbfgb123'
            }
        ]
    }
];

export const searchConfig = [
    {
        label: '搜索',
        type: 'Input',
        value: 'name',
        placeholder: '输入码包ID/食品/生产批次号',
        span: 10,
        // className: 'find',
        wide:230
    },
    {
        label: '生码时间',
        type: 'RangePicker',
        value: 'Time',
        placeholder: ['请选择'],
        span: 14,
        size: 32,
        wide: 260
    }
];

import BaseCard from '@components/base-card';
import BaseCollapse from '@components/base-collapse';
import BaseFormItem from '@components/base-form-item';
import BaseModal from '@components/base-modal';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import { Form, message, Image, Space } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { productionDetail } from '@services/production';
import { useMutation, useQuery } from 'react-query';
import { useLocation } from 'react-router-dom';
import { ProductInfoConfig, chainInfoConfig } from './config';
import { ReformChainError } from '@utils/errorCodeReform';
import BaseTooptip from '@components/base-tooltip';
import dayjs from 'dayjs';
import FilterForm from '@components/filter-form/filter-form';
import { FormItemVideo } from '@components';
import ChainDetailModal from '@components/chain_detail_modal';
import { decryptedUrl, isArrayArr } from '@utils';
// import './index.less'

function ProductDetail() {
    const { state } = useLocation();
    console.log('state', state);
    const productionlist: any = useRef('');
    const [SourceForm] = Form.useForm();
    const [upfile, setupfile]: any = useState('');
    const [ProductForm] = Form.useForm();
    const [ChainForm] = Form.useForm();

    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    const [promotionPicData, setPromotionPicData] = useState<any>();

    // 仅仅模拟数据
    useEffect(() => {}, []);

    const productiondetail = useQuery(
        ['productionDetail'],
        () => {
            return productionDetail({
                processId: state.data.id
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            },
            async onSuccess(res) {
                const arrayData = await Promise.all(
                    isArrayArr(res?.data?.processImg)?.map((item: any) => {
                        return decryptedUrl(item);
                    })
                );
                setPromotionPicData(arrayData);
                // setPromotionPicData(arrayData)
                const videos = await decryptedUrl(res?.data?.processVideo);
                ProductForm.setFieldsValue({
                    name: res?.data?.processName,
                    process: res?.data?.processInstructions,
                    processImg: arrayData && arrayData.length > 0 ? arrayData : null,
                    processVideo: videos || null
                });
            }
        }
    );

    const ChainDetailModalConfig = {
        transactionId: productiondetail?.data?.data?.transactionId,
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };

    const ProductInfoConfig = [
        {
            label: '过程名称',
            name: 'name',
            value: 'name',
            type: 'ShowText'
        },
        {
            label: '过程说明',
            name: 'process',
            value: 'process',
            type: 'ShowScrollText'
        },
        {
            type: 'Custom',
            label: '过程图片',
            name: 'processPic',
            value: 'processPic',
            children:
                promotionPicData && promotionPicData.length > 0 ? (
                    <Image.PreviewGroup>
                        <Space>
                            {promotionPicData?.map((item: any) => {
                                return <Image height={100} src={item}></Image>;
                            })}
                        </Space>
                    </Image.PreviewGroup>
                ) : (
                    '-'
                )
        },
        {
            type: 'Custom',
            label: '过程视频',
            name: 'processVideo',
            value: 'processVideo',
            children: (
                <Form.Item key='processVideo' name='processVideo'>
                    <FormItemVideo width={200} controls></FormItemVideo>
                </Form.Item>
            )
        }
    ];

    const valuesForm = SourceForm.getFieldsValue();
    console.log('SourceCodeForm', SourceForm, valuesForm);

    const chainInfoConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: '123',
            type: 'custom',
            write: 'true',
            span: 24,
            tooltip: '信息的链上的哈希值',
            children: productiondetail?.data?.data?.transactionId ? (
                <a onClick={() => setChainDetailModalVisible(true)} style={{ color: '#76ae55' }}>
                    {productiondetail?.data?.data?.transactionId}
                </a>
            ) : (
                '-'
            )
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: '123',
            write: 'true',
            type: 'Display',
            span: 24,
            tooltip: '信息上链的时间',
            displayDom: productiondetail?.data?.data?.transactionTime
                ? dayjs(productiondetail?.data?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
                : '-'
        }
    ];

    return (
        <>
            <BaseCard title={<PageTitle title='生产过程详情' bg='container sheng' />}>
                <Form form={ProductForm}>
                    <PageTitle title='生产过程信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={ProductInfoConfig} labelCol={false} />
                </Form>
                <Form form={ChainForm}>
                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={chainInfoConfig} />
                </Form>
            </BaseCard>
            <ChainDetailModal {...ChainDetailModalConfig} />
        </>
    );
}

export default ProductDetail;

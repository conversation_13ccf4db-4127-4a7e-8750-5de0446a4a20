import React, { useState, useEffect } from 'react';
import { Form, message, Badge, Space } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';

import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';

import BaseModal from '@components/base-modal';
import FilterForm from '@components/filter-form';
import TableHead from '@components/table-head';
import SingleSearch from '@components/single-search';

import styles from './index.module.less';

interface SegmentationWord {
    id: string;
    word: string;
    type: string;
    frequency: number;
    status: 'active' | 'inactive';
    createTime: string;
    updateTime: string;
}

const SegmentationManagement: React.FC = () => {
    const [form] = Form.useForm();
    const [searchForm] = Form.useForm();

    const [loading, setLoading] = useState(false);
    const [dataSource, setDataSource] = useState<SegmentationWord[]>([]);
    const [total, setTotal] = useState(0);
    const [current, setCurrent] = useState(1);
    const [pageSize, setPageSize] = useState(10);

    const [modalVisible, setModalVisible] = useState(false);
    const [editingRecord, setEditingRecord] = useState<SegmentationWord | null>(null);
    const [modalType, setModalType] = useState<'add' | 'edit'>('add');

    // 模拟数据
    const mockData: SegmentationWord[] = [
        {
            id: '1',
            word: '分词1',
            type: '名词',
            frequency: 1250,
            status: 'active',
            createTime: '2025-05-01 12:00:00',
            updateTime: '2025-05-01 12:00:00'
        },
        {
            id: '2',
            word: '分词2',
            type: '动词',
            frequency: 980,
            status: 'active',
            createTime: '2025-04-01 12:00:00',
            updateTime: '2025-04-01 12:00:00'
        },
        {
            id: '3',
            word: '分词3',
            type: '形容词',
            frequency: 756,
            status: 'inactive',
            createTime: '2025-04-01 12:00:00',
            updateTime: '2025-04-01 12:00:00'
        },
        {
            id: '4',
            word: '分词4',
            type: '名词',
            frequency: 432,
            status: 'active',
            createTime: '2025-02-01 12:00:00',
            updateTime: '2025-02-01 12:00:00'
        },
        {
            id: '5',
            word: '分词5',
            type: '副词',
            frequency: 321,
            status: 'active',
            createTime: '2025-01-20 12:00:00',
            updateTime: '2025-01-20 12:00:00'
        },
        {
            id: '6',
            word: '分词6',
            type: '动词',
            frequency: 198,
            status: 'inactive',
            createTime: '2025-01-10 12:00:00',
            updateTime: '2025-01-10 12:00:00'
        }
    ];

    useEffect(() => {
        fetchData();
    }, [current, pageSize]);

    const fetchData = async () => {
        setLoading(true);
        try {
            // 模拟API调用
            setTimeout(() => {
                setDataSource(mockData);
                setTotal(mockData.length);
                setLoading(false);
            }, 500);
        } catch (error) {
            message.error('获取数据失败');
            setLoading(false);
        }
    };

    const handleSearch = (values: any) => {
        console.log('搜索条件:', values);
        fetchData();
    };

    const handleReset = () => {
        searchForm.resetFields();
        fetchData();
    };

    const handleAdd = () => {
        setModalType('add');
        setEditingRecord(null);
        form.resetFields();
        setModalVisible(true);
    };

    const handleEdit = (record: SegmentationWord) => {
        setModalType('edit');
        setEditingRecord(record);
        form.setFieldsValue(record);
        setModalVisible(true);
    };

    const handleDelete = (record: SegmentationWord) => {
        message.success('删除成功');
        fetchData();
    };

    const handleToggleStatus = (record: SegmentationWord) => {
        const newStatus = record.status === 'active' ? 'inactive' : 'active';
        const statusText = newStatus === 'active' ? '启用' : '禁用';
        message.success(`${statusText}成功`);
        fetchData();
    };

    const handleModalOk = async () => {
        try {
            const values = await form.validateFields();
            console.log('表单数据:', values);

            if (modalType === 'add') {
                message.success('添加成功');
            } else {
                message.success('编辑成功');
            }

            setModalVisible(false);
            fetchData();
        } catch (error) {
            console.error('表单验证失败:', error);
        }
    };

    const handleModalCancel = () => {
        setModalVisible(false);
        form.resetFields();
        setEditingRecord(null);
    };

    // 搜索配置
    const searchConfig = {
        placeholder: '请输入分词',
        handleSearch: () => {
            console.log('搜索');
            fetchData();
        },
        setSearchValue: (value: string) => {
            console.log('搜索值:', value);
        }
    };

    // 筛选表单配置
    const filterConfig = [
        {
            type: 'Select',
            label: '状态',
            value: 'status',
            placeholder: '请选择状态',
            fields: [
                { label: '启用', value: 'active' },
                { label: '禁用', value: 'inactive' }
            ],
            span: 8
        }
    ];

    // 表格列配置
    const columns: ColumnsType<SegmentationWord> = [
        {
            title: '分词',
            dataIndex: 'word',
            key: 'word',
            width: 150
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            width: 180
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: 100,
            render: (status: string) => (
                <Badge
                    status={status === 'active' ? 'success' : 'error'}
                    text={status === 'active' ? '启用' : '禁用'}
                />
            )
        },
        {
            title: '操作',
            key: 'action',
            width: 200,
            render: (_, record) => (
                <Space size='middle'>
                    <BaseButton
                        type='dashed'
                        className={record.status === 'active' ? 'warnBtn' : 'primaryBtn'}
                        onClick={() => handleToggleStatus(record)}
                    >
                        {record.status === 'active' ? '禁用' : '启用'}
                    </BaseButton>
                    <BaseButton type='dashed' className='editBtn' onClick={() => handleEdit(record)}>
                        编辑
                    </BaseButton>
                    <BaseButton type='dashed' className='warnBtn' onClick={() => handleDelete(record)}>
                        删除
                    </BaseButton>
                </Space>
            )
        }
    ];

    // 表单配置
    const formConfig = [
        {
            type: 'Input',
            label: '分词',
            value: 'word',
            placeholder: '请输入分词',
            rules: [{ required: true, message: '请输入分词' }],
            span: 24
        }
    ];

    return (
        <div className={styles.container}>
            <BaseCard className={styles.cardContainer} title={<PageTitle title='分词管理' />}>
                <BaseTable
                    rowKey='id'
                    loading={loading}
                    dataSource={dataSource}
                    columns={columns}
                    btnDisplay={() => (
                        <TableHead
                            LeftDom={
                                <div className={styles.searchContainer}>
                                    <Form
                                        layout='inline'
                                        labelAlign='left'
                                        onFinish={handleSearch}
                                        form={searchForm}
                                        className='label-title'
                                    >
                                        <FilterForm itemConfig={filterConfig} size={100} labelCol={6} />
                                        <BaseButton
                                            type='primary'
                                            style={{ width: 100 }}
                                            className={`${styles.searchBtn} ${styles.baseBtn}`}
                                            icon={<SearchOutlined />}
                                            htmlType='submit'
                                        >
                                            查询
                                        </BaseButton>
                                        <BaseButton
                                            style={{ width: 100, marginLeft: 8 }}
                                            className={styles.baseBtn}
                                            onClick={handleReset}
                                        >
                                            重置
                                        </BaseButton>
                                    </Form>
                                </div>
                            }
                            RightDom={
                                <div style={{ display: 'flex' }}>
                                    <SingleSearch {...searchConfig} />
                                    <BaseButton
                                        type='dashed'
                                        icon={<PlusOutlined />}
                                        className='greenBtn'
                                        onClick={handleAdd}
                                        style={{ marginLeft: 8 }}
                                    >
                                        新建分词
                                    </BaseButton>
                                </div>
                            }
                        />
                    )}
                />

                <BasePagination
                    current={current}
                    pageSize={pageSize}
                    total={total}
                    onChange={(page, size) => {
                        setCurrent(page);
                        setPageSize(size || 10);
                    }}
                />
            </BaseCard>

            <BaseModal
                title={modalType === 'add' ? '新建分词' : '编辑分词'}
                visible={modalVisible}
                onOk={handleModalOk}
                onCancel={handleModalCancel}
                width={600}
            >
                <Form form={form} layout='vertical'>
                    <FilterForm itemConfig={formConfig} labelCol={false} wrapperCol={24} />
                </Form>
            </BaseModal>
        </div>
    );
};

export default SegmentationManagement;

import { useState, useRef } from 'react'
import { Select, Spin } from 'antd'
import { useInfiniteScroll, useDebounce } from 'ahooks'
import { debounce } from 'lodash-es';
import type { SelectProps } from 'antd'

interface RequestProps {
    pageNumber: number;
    pageSize: number;
    search: string;
}

interface PaginationSelectProps extends SelectProps {
    pageSize?: number;
    request: (requestProps: RequestProps) => Promise<{ list: { label: any, value: any }[], total: number }>
}

export default (props: PaginationSelectProps) => {
    const { pageSize = 10, request, ...restSelectProps } = props

    const [search, setSearch] = useState("")
    const debouncedSearchString = useDebounce(
        search,
        {
            wait: 600
        }
    );

    const isInFetching = useRef(false)
    const getListRequest = useInfiniteScroll((d) => {
        const page = d ? Math.ceil(d.list.length / pageSize) + 1 : 1;
        isInFetching.current = true
        console.log('loadmore')
        return request({
            pageNumber: page,
            pageSize: pageSize,
            search: debouncedSearchString
        })

    }, {
        reloadDeps: [debouncedSearchString],
        onFinally() {
            isInFetching.current = false
        }
    })

    const hasMore = getListRequest.data && getListRequest.data.list.length < getListRequest.data.total;
    const data = getListRequest?.data?.list || []

    return <Select loading={getListRequest.loading} showSearch filterOption={false} onSearch={(v) => { setSearch(v) }} options={data} onPopupScroll={debounce((e: any) => {
        // @ts-ignore
        const { scrollTop, scrollHeight, clientHeight } = e.target;
        const isReach = scrollHeight - scrollTop - clientHeight < 100;
        if (hasMore && isReach && !isInFetching.current) {
            getListRequest.loadMore()
        }
    }, 600, {
        leading: true,
        trailing: false
    })} dropdownRender={menu => <>
        {menu}
        {
            getListRequest.loadingMore && <div style={{
                padding: "5px 0",
                display: "flex",
                justifyContent: 'center'
            }}>
                <Spin></Spin>
            </div>
        }
    </>} {...restSelectProps}></Select>
}
/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 18:07:55
 * @LastEditTime: 2022-10-12 09:58:07
 * @LastEditors: PhilRandWu
 */
import { useNavigate } from 'react-router-dom';
import BaseButton from '@components/base-button';
import BaseCard from '@components/base-card';
import FilterForm from '@components/filter-form';
import PageTitle from '@components/page-title';
import { Button, Form, FormInstance, message, Upload } from 'antd';
import { useMutation, useQuery } from 'react-query';
import React, { Component, useEffect, useState } from 'react';
import { addConfigs, disabledDateTime } from './config';
import { signData } from '../../utils/blockChainUtils';
import { materialNameList, validMaterialList } from '@services/purchase-controller';
import { addPurchase } from '@services/purchase-controller';
import styles from './index.module.less';
import { useDispatch } from 'react-redux';
import { ReformChainError } from '@utils/errorCodeReform';
import { getLocalPrivatekey } from '@utils/blockChainUtils';
import ImgCropUpload from '@components/img-upload';
import moment from 'moment';

import { fileUpload, getFileUrlFormUploadedFile } from '@utils';
import { UploadOutlined } from '@ant-design/icons';

function disabledDate(current: any) {
    return current > moment().subtract(0, 'days');
}

const RawMaterialAdd = (props: any) => {
    const [SourceCodeForm] = Form.useForm();
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const [isUpLoading, setIsUpLoading]: any = useState();
    const [uploadImageUrl, setUploadImageUrl]: any = useState('');
    const [upfile, setupfile]: any = useState('');
    //新增
    const addpurchase = useMutation(addPurchase, {
        onSuccess(res) {
            message.success('新增成功');
            navigate('/raw/rawlist');
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });

    //原料列表
    const validMateriallist = useQuery(
        ['validMaterialList'],
        () => {
            return materialNameList({
                valid: true
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            },
            onSuccess(res: any) {
                getLocalPrivatekey(dispatch);
                const date = new Date().getTime();
                const datetext = date.toString().slice(1, 13);
                SourceCodeForm.setFieldsValue({
                    batchNumber: datetext
                });
            }
        }
    );
    // console.log(99999999,validMateriallist)
    const label = '';

    const mapToEnum: any = {};

    (validMateriallist?.data?.data || [])?.forEach((item: any, index: any) => {
        // console.log(7777777,item)
        mapToEnum[item.id] = item.material_name;
    });
    let newdata = SourceCodeForm?.getFieldValue('productionDate');

    const productVideoAcceptTypes = ['.rar', '.zip', '.doc', '.docx', '.pdf', '.jpg'];
    const handleBeforeProductVideoUpload = (file: any) => {
        const fileType = file?.name?.split('.').at(-1).toLowerCase();
        console.log(fileType, file, 'fffffff');
        if (!productVideoAcceptTypes.includes('.' + fileType)) {
            message.error('文件格式不正确');
            return Upload.LIST_IGNORE;
        }
        if (file.size / 1024 / 1024 > 20) {
            message.error('附件最大上传20MB');
            return Upload.LIST_IGNORE;
        }
        return true;
    };

    //原料列表
    const addConfigs = [
        {
            type: 'Select',
            label: '原料名称',
            value: 'name',
            rules: [{ required: true, message: '请选择原料!' }],
            placeholder: '请选择原料',
            span: 12,
            fields: [
                ...(validMateriallist?.data?.data || [])?.map((item: any, index: any) => {
                    // console.log(7777777,item)
                    const materialdata = {
                        value: item.id,
                        label: item.materialName
                    };
                    return materialdata;
                })
            ],
            // onChange: label,
            className: 'rawMaterial'
        },
        {
            type: 'Input',
            label: '原料采购批次',
            value: 'batchNumber',
            rules: [
                { required: true, message: '' },
                {
                    max: 50,
                    message: '请保持字符在50字符以内!'
                },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^\+?[1-9]\d*$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入批次号！');
                        } else if (value[0] === ' ' || value[value.length - 1] === ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            callback('请输入正整数');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入批次号',
            span: 12,
            className: 'count'
        },
        {
            type: 'DatePicker',
            label: '生产日期',
            value: 'productionDate',
            rules: [{ required: true, message: '请输入!' }],
            placeholder: '请输入',
            name: 'productionDate',
            showTime: true,
            span: 12,
            wide: 280,
            disabledTime: disabledDate
            // disabledSecondsTime: disabledDateTime
        },
        {
            type: 'Input',
            label: '保质期',
            value: 'expirationDate',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback('请输入保质期！');
                        } else if (value[0] === ' ' || value[value.length - 1] === ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入',
            span: 12,
            className: 'count'
        },
        {
            type: 'Input',
            label: '数量',
            value: 'count',
            rules: [
                { required: false, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[1-9]\d{0,9}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback();
                        } else if (value[0] === ' ' || value[value.length - 1] === ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            callback('请输入正整数，并保持在10字符内');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入数量',
            span: 12,
            wide: 280
        },
        {
            type: 'Input',
            label: '规格',
            value: 'specification',
            rules: [
                { required: false, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback();
                        } else if (value[0] === ' ' || value[value.length - 1] === ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入规格',
            span: 12,
            className: 'count'
        },
        {
            type: 'Custom',
            label: '合格证明',
            value: 'certificateQualification',
            span: 12,
            rules: [{ required: true, message: '请上传合格证明' }],
            placeholder: '大小不超过5MB',
            children: (
                // <Form.Item noStyle>
                //     <div className={styles.prodPic}>
                // <Form.Item name='certificateQualification'>
                <ImgCropUpload
                    setIsUpLoading={setIsUpLoading}
                    isUpLoading={isUpLoading}
                    isCrop={false}
                    maxAmount={1}
                    tips={'大小不超过5MB'}
                ></ImgCropUpload>
                // </Form.Item>
                //     </div>
                // </Form.Item>
            )
        },
        {
            type: 'Custom',
            label: '原料图片',
            value: 'rawMaterialImg',
            span: 12,
            children: (
                <Form.Item noStyle>
                    <div className={styles.prodPic}>
                        <Form.Item name='rawMaterialImg'>
                            <ImgCropUpload
                                setIsUpLoading={setIsUpLoading}
                                isUpLoading={isUpLoading}
                                isCrop={false}
                                maxAmount={1}
                                tips={'大小不超过5MB'}
                            ></ImgCropUpload>
                        </Form.Item>
                    </div>
                </Form.Item>
            )
        },
        {
            type: 'Custom',
            label: '附件',
            value: 'accessory',
            placeholder: '支持扩展名：.rar .zip .doc .docx .pdf .jpg...',
            span: 12,
            children: (
                <Form.Item noStyle>
                    <div className={styles.prodPic}>
                        <Form.Item
                            name='accessory'
                            rules={[
                                {
                                    required: false,
                                    message: '请上传'
                                }
                            ]}
                            extra={
                                <div style={{ color: '#333', width: 280 }}>
                                    支持扩展名：.rar .zip .doc .docx .pdf .jpg...
                                </div>
                            }
                            valuePropName='fileList'
                            getValueFromEvent={(e: any) => {
                                if (Array.isArray(e)) {
                                    return e;
                                }
                                return e && e.fileList;
                            }}
                        >
                            <Upload
                                accept={productVideoAcceptTypes.join(',')}
                                customRequest={({ file, onError, onProgress, onSuccess }) => {
                                    // @ts-ignore
                                    fileUpload({
                                        ...{ file, onError, onProgress, onSuccess },
                                        isUploading: isUpLoading,
                                        setIsUpLoading: setIsUpLoading
                                    });
                                }}
                                beforeUpload={handleBeforeProductVideoUpload}
                                maxCount={1}
                            >
                                <Button icon={<UploadOutlined rev={undefined} />}>上传文件</Button>
                            </Upload>
                        </Form.Item>
                    </div>
                </Form.Item>
            )
        }
    ];

    // 监听表单变化
    const onFieldsChange = (values: any, errorFields: any) => {
        //限制供货日期必须在生产日期之后
        newdata = SourceCodeForm?.getFieldValue('productionDate');
        if (values[0].name[0] === 'productionDate' || values[0].name[0] === 'deliveryDate') {
            if (
                SourceCodeForm?.getFieldValue('productionDate')?.$d >= SourceCodeForm?.getFieldValue('deliveryDate')?.$d
            ) {
                SourceCodeForm.setFieldsValue({ deliveryDate: null });
            }
        }
    };

    const onFinish = (values: any) => {
        console.log('values', values);
        // console.log('1239990090', mapToEnum[values?.name]);
        if (isUpLoading) {
            message.warning('正在上传文件请稍等～');
            return;
        }
        const params: any = {
            materialId: values.name,
            materialImg: getFileUrlFormUploadedFile(values?.rawMaterialImg)?.[0],
            count: values.count,
            purchaseBatch: values.batchNumber,
            specification: values.specification,
            productionDate: values.productionDate,
            expiration: values.expirationDate,
            certificate: getFileUrlFormUploadedFile(values?.certificateQualification)?.[0],
            accessory: getFileUrlFormUploadedFile(values?.accessory)?.[0]
        };
        const paramStr = JSON.stringify(params);
        signData(dispatch, JSON.stringify(params), (error, result: any) => {
            if (!error && result) {
                addpurchase.mutate({
                    addPurchaseVo: params,
                    paramStr: paramStr,
                    signature: result
                });
            } else if (error !== 'misprivatekey') {
                message.info('签名异常，请重试或联系管理员');
            }
        });
    };
    return (
        <BaseCard title={<PageTitle title='新建采购批次' />}>
            <Form
                onFinish={onFinish}
                onFieldsChange={onFieldsChange}
                form={SourceCodeForm}
                className={'raw-material-add-form-cor-10 edit-label-title'}
            >
                <FilterForm itemConfig={addConfigs} labelCol={6} wrapperCol={9} />
                <div className={styles.addBtnContainer}>
                    <Form.Item className={styles.saveBtn}>
                        <BaseButton type='primary' htmlType='submit' className={styles.submitBtn}>
                            提交
                        </BaseButton>
                    </Form.Item>
                    <Form.Item>
                        <BaseButton
                            htmlType='button'
                            type='dashed'
                            className={styles.primaryBtn}
                            onClick={() => {
                                navigate('/raw/rawlist');
                            }}
                        >
                            取消
                        </BaseButton>
                    </Form.Item>
                </div>
            </Form>
        </BaseCard>
    );
};
// }

export default RawMaterialAdd;

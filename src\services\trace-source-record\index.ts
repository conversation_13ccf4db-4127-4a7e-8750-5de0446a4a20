import request from '../request';
//溯源码分页接口
export const traceSourceRecordPage = (obj: any) => {
    return request({
        url: '/trace-source-record/traceSourceRecordPage',
        method: 'post',
        data: obj
    });
};
//原料详情
export const traceMaterialDetail = (obj: any) => {
    return request({
        url:`/trace-source-record/traceMaterialDetail?purchaseId=${obj.id}`,
        method: 'post',
        data: obj
    });
};
//生产详情
export const traceProductionDetail = (obj: any) => {
    return request({
        url:`/trace-source-record/traceProductionDetail?productionId=${obj.id}`,
        method: 'post',
        data: obj
    });
};
//质检详情
export const traceTestDetail = (obj: any) => {
    return request({
        url:`/trace-source-record/traceTestDetail?qualityTestId=${obj.id}`,
        method: 'post',
        data: obj
    });
};
/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-12 13:41:01
 * @LastEditTime: 2022-11-01 18:43:57
 * @LastEditors: PhilRandWu
 */
export const qcqaInfoConfigs = [
    {
        type: 'Input',
        label: '食品名称',
        value: 'num',
        placeholder: '请输入',
        span: 12
    },
    {
        type: 'Input',
        label: '生产批次',
        value: 'num',
        placeholder: '请输入',
        span: 12
    },
    {
        type: 'DatePicker',
        label: '质检时间',
        value: 'num',
        placeholder: '请输入',
        span: 12
    },
    {
        type: 'Input',
        label: '食品批号',
        value: 'num',
        placeholder: '请输入',
        span: 12
    },
    {
        type: 'Input',
        label: '质检内容',
        value: 'num',
        placeholder: '请输入',
        span: 12
    },
    {
        type: 'Select',
        label: '质检结果',
        value: 'name',
        rules: [{ required: true, message: '请输入!' }],
        placeholder: '请输入',
        span: 12,
        fields: [
            {
                value: 0,
                label: '合格'
            },
            {
                value: 1,
                label: '不合格'
            }
        ]
    },
    {
        type: 'Input',
        label: '质检人员',
        value: 'num',
        placeholder: '请输入',
        span: 12
    },
    {
        type: 'Input',
        label: '联系电话',
        value: 'num',
        placeholder: '请输入',
        span: 12
    },
    {
        type: 'UploadFile',
        label: '质检报告',
        value: 'num',
        placeholder: '最大可上传20M,不限制类型',
        span: 12
    },
    {
        type: 'UploadFile',
        label: '附件',
        value: 'num',
        placeholder: '最大可上传20M,不限制类型',
        span: 12
    }
];

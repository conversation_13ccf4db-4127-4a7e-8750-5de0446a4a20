import BaseCard from '@components/base-card';
import PageTitle from '@components/page-title';
import React, { useState, useEffect, useMemo, useRef } from 'react';
import ReactDOM from 'react-dom';
import dayjs from 'dayjs';
import { traceFoodRank, traceSourceOverView } from '@services/traceFoodRank';
import { useMutation, useQuery } from 'react-query';
import { ColumnsType } from 'antd/lib/table';
import { Column } from '@ant-design/plots';
import styles from './index.module.less';
import { Divider, message, Table, Select, Space, Image, Carousel } from 'antd';
import useUrlState from '@ahooksjs/use-url-state';
import BaseTable from '@components/base-table';
import BasePagination from '@components/base-pagination';
import { useAccountList } from '../../myhooks/useaccountlist';
import CardTips from '@components/card-tips';
import WithPaginate from '../../hoc/withpaginate';
import OverViewService from '@services/overview';
import { useAppSelector } from '@store';
import * as echarts from 'echarts';

import ReactEcharts from 'echarts-for-react';
import chinaMapJson from './china.json';
import { geoCoordMap } from './dataMap.js';
import { useNavigate } from 'react-router-dom';
import { getHasPermissions } from '@services/menu';
import Top from '../../assets/icon/top.png';
interface IUrlState {
    pageIndex: number;
    pageSize: number;
}
const registerMap = async () => {
    echarts.registerMap('china', chinaMapJson as any);
};
const DemoColumn = () => {
    const traceSourceoverCheatview = useQuery(['traceSourceoverCheatview'], () => {
        return OverViewService.getTraceCount();
    });
    const data =
        traceSourceoverCheatview?.data?.data
            // ?.sort((a: any, b: any) => Number(a.month) - Number(b.month))
            // .filter(
            //     (obj: any, index: any, self: any) =>
            //         index === self.findIndex((o: any) => o.month === obj.month && o.count === obj.count)
            // )
            .map((item: any) => {
                return {
                    month: item?.month,
                    count: Number(item?.count)
                };
            }) || [];
    const config: any = {
        data,
        xField: 'month',
        yField: 'count',
        adjust: true,
        xAxis: {
            label: {
                autoHide: true,
                autoRotate: false
            }
        },
        label: {
            position: 'bottom',
            style: {
                fill: '#FFFFFF',
                opacity: 0.6
            }
        },
        columnStyle: {
            fill: 'l(90) 0:#80A932 1:#347934', // 从下到上的渐变色
            radius: [2, 2, 0, 0] // 设置柱体圆角
        },
        meta: {
            month: {
                alias: '月份'
            },
            count: {
                alias: '溯源次数'
            }
        }
    };
    return <Column {...config} />;
};
const TraceabilityOverview = (props: any) => {
    const navigate = useNavigate();

    const [sortBy, setSortBy] = useState();
    const { pageInfo, handlePaginationChange } = props;
    const userInfo: any = useAppSelector((store) => store.user);
    console.log(userInfo.userInfo.identity);
    const chartRef = useRef(null);
    // 天气
    const addLandWeather: any = useQuery(['addLandWeather'], OverViewService.LandState);
    const addLandWeatherData = addLandWeather?.data?.data;
    //气象

    const LandWeather: any = useQuery(['LandWeather'], OverViewService.LandWeather);
    const LandWeatherData = LandWeather?.data?.data;
    // 获取商品上链信息
    const ChainProductCount: any = useQuery(['ChainProductCount'], OverViewService.ChainProductCount);
    const ChainProductCountData = ChainProductCount?.data?.data;

    // 销售量信息
    const getOverviewInfo: any = useQuery(['getOverviewInfo'], OverViewService.getOverviewInfo);
    const getOverviewInfoData = getOverviewInfo?.data?.data;

    // 生产批次图表
    const ProductionCountGroupMonth: any = useQuery(
        ['ProductionCountGroupMonth'],
        OverViewService.ProductionCountGroupMonth
    );
    const ProductionCountGroupMonthData = ProductionCountGroupMonth?.data?.data;

    // 销售折线图数据
    const [timeArray, setTimeArray] = useState([]);
    const [salesAmountArray, setSalesAmountArray] = useState([]);
    const [salesVolumeArray, saleSVolumeArray] = useState([]);
    const getSalesChart: any = useQuery(['getSalesChart'], OverViewService.getSalesChart);
    const getSalesChartData = getSalesChart?.data?.data;
    useEffect(() => {
        if (getSalesChart.isSuccess && getSalesChart.data) {
            const getSalesChartData = getSalesChart.data.data;
            const newTimeArray = getSalesChartData.map((item: any) => item.salesMonth);
            const newSalesAmountArray = getSalesChartData.map((item: any) => item.salesAmount);
            const newSalesVolumeArray = getSalesChartData.map((item: any) => item.salesVolume);

            setTimeArray(newTimeArray);
            setSalesAmountArray(newSalesAmountArray);
            saleSVolumeArray(newSalesVolumeArray);
        }
    }, [getSalesChart.isSuccess]);
    console.log(getSalesChartData, 'getSalesChartDatagetSalesChartDatagetSalesChartDatagetSalesChartData');
    console.log(timeArray, 'timeArraytimeArraytimeArray');
    const ProductionStatistical: any = useQuery(['ProductionStatistical'], OverViewService.ProductionStatistical);
    const ProductionStatisticalData = ProductionStatistical?.data?.data;

    const traceSourceoverview: any = useQuery(['traceSourceoverview'], OverViewService.Query);
    const traceSourceoverviewData: any = traceSourceoverview?.data?.data;

    // 溯源
    const getTraceCountDay: any = useQuery(['getTraceCountDay'], OverViewService.getTraceCountDay);
    const getTraceCountDayData = getTraceCountDay?.data?.data;
    // 仓储
    const getTraceCountDayWar: any = useQuery(['getTraceCountDayWar'], OverViewService.getTraceCountDayWar);
    const getTraceCountDayDataWar = getTraceCountDayWar?.data?.data;
    // 收购
    const PurchaseSummary: any = useQuery(['PurchaseSummary'], OverViewService.getMaterialPurchaseSummary);
    const purchaseSummaryData = PurchaseSummary?.data?.data;
    const traceabilityConfig = {
        title: '溯源查询总次数',
        num: traceSourceoverviewData?.traceSourceStatistical?.totalNumber,
        monthDec: '当月增加',
        monthNum: traceSourceoverviewData?.traceSourceStatistical?.monthNumber,
        dayDes: '当日新增',
        dayNum: traceSourceoverviewData?.traceSourceStatistical?.todayNumber,
        className: styles.downCard1
    };
    // 仓储

    const warehouseConfig = {
        title: '产品仓储总数',
        num: getTraceCountDayDataWar?.totalInboundCount,
        monthDec: '当月增加',
        monthNum: getTraceCountDayDataWar?.addMonthInboundCount,
        dayDes: '当日增加',
        dayNum: getTraceCountDayDataWar?.addDayInboundCount,
        className: styles.downCard1,
        unit: '袋'
    };
    const productConfig = {
        title: '生产总批次',
        num: ProductionStatisticalData?.productionStatistical?.totalNumber,
        monthDec: '当月增加',
        monthNum: ProductionStatisticalData?.productionStatistical?.monthNumber,
        dayDes: '当日新增',
        dayNum: ProductionStatisticalData?.productionStatistical?.todayNumber,
        className: 'downCardGoods'
    };

    const sourceConfig = {
        title: '原料批次数',
        num: traceSourceoverviewData?.purchaseStatistical?.totalNumber,
        monthDec: '本月增加',
        monthNum: traceSourceoverviewData?.purchaseStatistical?.monthNumber,
        dayDes: '今日新增',
        dayNum: traceSourceoverviewData?.purchaseStatistical?.todayNumber,
        className: styles.downCard
    };
    const qcqaConfig = {
        title: '质检批次数',
        num: traceSourceoverviewData?.qualityTestStatistical?.totalNumber,
        monthDec: '本月增加',
        monthNum: traceSourceoverviewData?.qualityTestStatistical?.monthNumber,
        dayDes: '今日新增',
        dayNum: traceSourceoverviewData?.qualityTestStatistical?.todayNumber,
        className: styles.downCard
    };

    // 未对接的数据配置

    // 收购新增
    const purchaseConfig = {
        title: '收购总批次',
        num: purchaseSummaryData?.sumCount,
        monthDec: '当月增加',
        monthNum: purchaseSummaryData?.monthAdd,
        dayDes: '当日新增',
        dayNum: purchaseSummaryData?.productionStatistical?.todayNumber,
        className: 'downCardGoods'
    };
    // 新增种植模块

    const landConfig = {
        title: '原料批次数',
        num: traceSourceoverviewData?.purchaseStatistical?.totalNumber,
        monthDec: '本月增加',
        monthNum: traceSourceoverviewData?.purchaseStatistical?.monthNumber,
        dayDes: '今日新增',
        dayNum: traceSourceoverviewData?.purchaseStatistical?.todayNumber,
        className: styles.downCard
    };
    const weatherConfig = {
        title: '质检批次数',
        num: traceSourceoverviewData?.qualityTestStatistical?.totalNumber,
        monthDec: '本月增加',
        monthNum: traceSourceoverviewData?.qualityTestStatistical?.monthNumber,
        dayDes: '今日新增',
        dayNum: traceSourceoverviewData?.qualityTestStatistical?.todayNumber,
        className: styles.downCard
    };
    let sortBY: any = null;
    if (sortBy === 'ascend') {
        sortBY = 0;
    } else if (sortBy === 'descend') {
        sortBY = 1;
    }
    const [page, setPage] = useState({
        pageIndex: 1,
        pageSize: 10
    });
    const [pageIndex, setpageIndex] = useState(1);
    const [pageIndexQuality, setpageIndexQuality] = useState(1);
    const [pageIndexPurchase, setpageIndexPurchase] = useState(1);
    const [hasMore, setHasMore] = useState(true); // 新增状态变量

    // const [tableData, setTableData] = useState<any[]>([]);
    // 地块实时作物表
    const [tableDataPlantList, setTableDataPlantList] = useState<any[]>([]);

    const traceFoodrank = useQuery(
        ['traceFoodrank', pageIndex],
        () => {
            return OverViewService.getLandGrowingPage({
                // coreId: Number(userInfo?.coreId),
                // sortBy: sortBY,
                pageIndex: pageIndex,
                pageSize: page.pageSize
            });
        },
        {
            onSuccess: (data) => {
                setTableDataPlantList((prevData) => {
                    console.log(prevData, 'prevData');
                    const newData = data?.data?.records?.map((item: any) => {
                        const sowTime = dayjs(item.sowTime).format('YYYY-MM-DD HH:mm:ss');
                        return {
                            ...item,
                            sowTime: sowTime
                        };
                    });
                    return [...prevData, ...newData];
                });
                console.log(data?.data?.records);
                if (data?.data?.records?.length === 0) {
                    setHasMore(false); // 如果没有更多数据，则设置 `hasMore` 为 `false`
                }
            }
        }
    );
    // 获取质检图表信息
    const [year, setYear] = useState(2);

    const qualityInfo: any = useQuery(['qualityInfo'], OverViewService.getQualityInfo);
    const qualityInfoData = qualityInfo?.data?.data;
    // 最近质检批次列表
    const [tableDataQualityPage, setTableDataQualityPage] = useState<any[]>([]);
    const latestQualityPage = useQuery(
        ['latestQualityPage', pageIndexQuality],
        () => {
            return OverViewService.getLatestQualityPage({
                // coreId: Number(userInfo?.coreId),
                // sortBy: sortBY,
                pageIndex: pageIndexQuality,
                pageSize: page.pageSize
            });
        },
        {
            onSuccess: (data) => {
                setTableDataQualityPage((prevData) => {
                    console.log(prevData, 'prevData');
                    // const newData = data?.data?.records?.map((item: any) => {
                    //     const sowTime = dayjs(item.sowTime).format('YYYY-MM-DD HH:mm:ss');
                    //     return {
                    //         ...item,
                    //         sowTime: sowTime
                    //     };
                    // });
                    return [...prevData, ...data?.data?.records];
                });
                console.log(data?.data?.records);
                if (data?.data?.records?.length === 0) {
                    setHasMore(false); // 如果没有更多数据，则设置 `hasMore` 为 `false`
                }
            }
        }
    );
    // 种植权限控制种植11111
    const getHasPermiss = useQuery(['getHasPermiss'], () => {
        return getHasPermissions({
            permission: 'land_plant'
        });
    });
    // 收购
    //   const getHasPermiss = useQuery(['getHasPermiss'], () => {
    //     return getHasPermissions({
    //         permission: 'land_plant'
    //     });
    // });
    // 销售
    const getHasPermisssale = useQuery(['getHasPermisssale'], () => {
        return getHasPermissions({
            permission: 'sale'
        });
    });

    // 仓储
    const getHasPermissWarehouse = useQuery(['getHasPermissWarehouse'], () => {
        return getHasPermissions({
            permission: 'warehouse'
        });
    });

    const getHasPermissData = getHasPermiss?.data?.data;

    // const getHasPermissData = getHasPermiss?.data?.data;
    const getHasPermisssaleData = getHasPermisssale?.data?.data;

    // 仓储
    const getHasPermissWarehouseData = getHasPermissWarehouse?.data?.data;
    console.log(getHasPermissWarehouseData);

    // 销售区地域分布
    const [numvalue, setNumvalue] = useState<any>(1);
    const handleChangeMarket = (value: string) => {
        console.log(`selected ${value}`);
        setNumvalue(value);
        //  const marketPie=useQuery(['marketPie'],()=>{
        //   return OverViewService.getsalesDonutChart({
        //     flag:value
        //   })
        //  })
        //  OverViewService.getsalesDonutChart({flag:value}).then((res)=>{
        //   console.log('marketPiemarketPie',res)
        //  })
    };
    // 销售额/量下拉

    // handleChangeSale
    const [sale, setSale] = useState<any>(1);
    const handleChangeSale = (value: string) => {
        if (value === '销售量') {
            setSale(1);
        }
        if (value === '销售额') {
            setSale(2);
        }
        console.log(`selected ${value}`);
    };
    const marketPie = useQuery(['marketPie', numvalue], () => {
        return OverViewService.getsalesDonutChart({
            flag: numvalue
        });
    });
    const marketPieData = marketPie?.data?.data?.map((item: any) => {
        return {
            ...item,
            value: item.qtySkuTotal
        };
        delete item.qtySkuTotal; // 直接删除ratio键
    });
    // 销售饼图
    const initMarket = () => {
        return {
            tooltip: {
                trigger: 'item',
                // formatter: '{b}: {c} ({d}%)'
                formatter: '{b}:{c}单'
            },
            // legend: {
            //     orient: 'vertical',
            //     top: '60%', // 离底部 10% 的位置
            //     left: '70%', // 离右边 10% 的位置
            //     selectedMode: false
            // },

            legend: {
                orient: 'vertical',
                top: '0%',
                left: '65%',
                selectedMode: false,
                width: 200, // 增加宽度
                itemWidth: 10, // 减少图例项的宽度
                itemHeight: 10, // 减少图例项的高度
                textStyle: { fontSize: 12 }, // 减少字体大小
                formatter: function (name: any) {
                    // 自定义格式化函数，根据需要进行换行
                    // return name.replace(/(.{15})/g, '$1\n');
                    return name.length > 5 ? name.substring(0, 5) + '...' : name;
                },
                scrollable: true // 开启滚动条
            },
            series: [
                {
                    // name: 'Access From',
                    type: 'pie',
                    radius: ['40%', '55%'],
                    center: ['35%', '40%'], // 使饼图中心偏上
                    avoidLabelOverlap: false,
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: false,
                            fontSize: 20,
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: marketPieData
                }
            ]
        };
    };

    // 轮播图
    const productImageList = useQuery(
        ['productImageList'],
        () => {
            return OverViewService.getProductImageList();
        }
        // {
        //     onSuccess: (data) => {
        //         setTableDataPlantList((prevData) => {
        //             console.log(prevData, 'prevData');
        //             const newData = data?.data?.records?.map((item: any) => {
        //                 const sowTime = dayjs(item.sowTime).format('YYYY-MM-DD HH:mm:ss');
        //                 return {
        //                     ...item,
        //                     sowTime: sowTime
        //                 };
        //             });
        //             return [...prevData, ...newData];
        //         });
        //         console.log(data?.data?.records);
        //         if (data?.data?.records?.length === 0) {
        //             setHasMore(false); // 如果没有更多数据，则设置 `hasMore` 为 `false`
        //         }
        //     }
        // }
    );
    const ratioGroupProduct = useQuery(
        ['ratioGroupProduct', year],
        () => {
            return OverViewService.getProductionRatioGroupProduct({
                type: year
            });
        }
        // {
        //     onSuccess: (data) => {
        //         setTableDataPlantList((prevData) => {
        //             console.log(prevData, 'prevData');
        //             const newData = data?.data?.records?.map((item: any) => {
        //                 const sowTime = dayjs(item.sowTime).format('YYYY-MM-DD HH:mm:ss');
        //                 return {
        //                     ...item,
        //                     sowTime: sowTime
        //                 };
        //             });
        //             return [...prevData, ...newData];
        //         });
        //         console.log(data?.data?.records);
        //         if (data?.data?.records?.length === 0) {
        //             setHasMore(false); // 如果没有更多数据，则设置 `hasMore` 为 `false`
        //         }
        //     }
        // }
    );
    const ratioGroupProductPicdata = ratioGroupProduct?.data?.data?.map((item: any) => {
        return {
            ...item,
            value: item.ratio
        };
        delete item.ratio; // 直接删除ratio键
    });

    // 生产饼图
    const handleChangePic = (value: string) => {
        console.log(`selected ${value}`);
        // series.map((series, index) => {
        //     series.data = [];
        // });
        // rawData = [];
        // value === '总批次' ? setSelectedYear(new Date().getFullYear()) : setSelectedYear(new Date().getFullYear() - 2);
        value === '总批次' ? setYear(2) : setYear(1);
    };

    const handleChange = (value: string) => {
        console.log(`selected ${value}`);
        // series.map((series, index) => {
        //     series.data = [];
        // });
        // rawData = [];
        value === '本年度' ? setSelectedYear(new Date().getFullYear()) : setSelectedYear(new Date().getFullYear() - 2);
    };

    //列表数据
    // const tableData = traceFoodrank?.data?.data?.records?.map((item: any, index: any) => ({
    //     ranking: (index + 1) * (pageInfo.pageIndex - 1) + 1,
    //     foodName: item.productName,
    //     todayCount: item.todayCount,
    //     monthCount: item.monthCount,
    //     totalCount: item.totalCount
    // }));

    // console.log(tableData);
    const listColumnPurchase: ColumnsType<any> = [
        // {
        //     title: '排名',
        //     dataIndex: 'ranking',
        //     key: 'ranking',
        //     ellipsis: true,
        //     render(value, record, index) {
        //         return index + 1 + pageInfo.pageSize * (pageInfo.pageIndex - 1);
        //     }
        // },
        {
            title: '收购批次',
            dataIndex: 'purchaseBatch',
            key: 'purchaseBatch',
            ellipsis: true
        },
        {
            // width: 150,
            title: '农作物类型',
            dataIndex: 'plantName',
            key: 'plantName',
            ellipsis: true
            // sorter: true
        },
        {
            title: '收购重量（吨）',
            dataIndex: 'purchaseWeight',
            key: 'purchaseWeight',
            ellipsis: true
        },
        {
            title: '收购时间',
            dataIndex: 'purchaseTime',
            key: 'purchaseTime',
            ellipsis: true,
            render: (_, row) => (row.purchaseTime ? dayjs(row.purchaseTime).format('YYYY-MM-DD HH:mm:ss') : '-')
        }
    ];
    // 地块实时
    const listColumnLand: ColumnsType<any> = [
        {
            title: '地块名称',
            dataIndex: 'landName',
            key: 'landName',
            ellipsis: true
        },
        {
            width: 150,
            title: '农作物类型',
            dataIndex: 'plantName',
            key: 'plantName',
            ellipsis: true
        },
        {
            title: '播种时间',
            dataIndex: 'sowTime',
            key: 'sowTime',
            ellipsis: true,
            render: (_, row) => (row.sowTime ? dayjs(row.sowTime).format('YYYY-MM-DD HH:mm:ss') : '-')
        }
    ];
    // 质检地块
    const listColumnQuality: ColumnsType<any> = [
        {
            title: '生产批次号',
            dataIndex: 'productionBatch',
            key: 'productionBatch',
            ellipsis: true
        },
        {
            width: 150,
            title: '商品名称',
            dataIndex: 'productName',
            key: 'productName',
            ellipsis: true
        },
        {
            title: '质检结果',
            dataIndex: 'inspectionResults',
            key: 'inspectionResults',
            ellipsis: true,
            render: (_, row) => (row.inspectionResults == 1 ? '合格' : '不合格')
        },
        {
            title: '质检时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true,
            render: (_, row) => (row.createTime ? dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '-')
        }
    ];

    // const year = new Date().getFullYear();
    // const januaryFirst = new Date(Date.UTC(year, 0, 1, 0, 0, 0, 0));
    // const formattedDate = januaryFirst.toISOString();

    const getFormattedDateForYearStart = (year: any) => {
        const januaryFirst = new Date(Date.UTC(year, 0, 1, 0, 0, 0, 0));
        return januaryFirst.toISOString();
    };
    // 获取当前时间
    let datetime = new Date();
    let timezoneOffset = datetime.getTimezoneOffset() * 60000; // 获取当前时区与UTC的时间差（以毫秒为单位）
    let localDatetime = new Date(Number(datetime) - Number(timezoneOffset)); // 调整时间，得到当前时区时间
    let isoString = localDatetime.toISOString();

    interface ChartSeries {
        // 假设我们不需要 name 属性，因为它在 map 函数中被注释掉了
        type: string;
        stack: string;
        barWidth: string;
        itemStyle: {
            barBorderRadius: number[];
            color: string;
        };
        data: number[]; // 假设 rawData[sid] 是一个数字数组
    }

    // ... 在你的组件中
    const [seriesdata, setSeriesdata] = useState<ChartSeries[]>([]);
    const [arrx, setArrx] = useState<string[]>([]);

    const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());

    const LandPlantBatchBarChart = useQuery(['LandPlantBatchBarChart', selectedYear], () => {
        return OverViewService.BarChart({
            startTime: getFormattedDateForYearStart(selectedYear),
            endTime: isoString
        });
    });

    const data: { [key: string]: { name: string; value: number }[] } = LandPlantBatchBarChart.data?.data || {};

    // Memoize rawData computation based on data
    const rawData = useMemo(() => {
        const raw: any[] = [];
        for (let i = 0; i < 13; i++) {
            raw[i] = [];
            for (const key in data) {
                if (data[key][i]) {
                    raw[i].push(data[key][i]);
                }
            }
        }
        return raw;
    }, [data]);
    // Update arrx whenever data changes
    useEffect(() => {
        console.log(data);
        const arr = Object.keys(data);
        setArrx(arr);
    }, [data]);
    // Memoize series computation based on arrx and data

    const series = useMemo(() => {
        return rawData.map((name, sid) => {
            const colors = [
                '#c1f167',
                '#77ac56',
                '#97FFFF',
                '#7FFFD4',
                '#9ACD32',
                '#76EE00',
                '#66CD00',
                '#00CD00',
                '#00FF00',
                '#00CD66',
                '#008B00',
                '#92ad84',
                '#9AFF9A'
            ];
            const color = colors[sid] || '#c1f167';
            return {
                type: 'bar',
                stack: 'total',
                barWidth: '35px',
                itemStyle: {
                    barBorderRadius: [2, 2, 0, 0],
                    color: color
                },
                data: rawData[sid] || []
            };
        });
    }, [arrx, rawData]);

    const getOption = (): any => {
        return {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: (params: any) => {
                    const titles = `<div style="font-weight:bold;">名称:种植批次</div>`;
                    const content = params
                        .map((item: any) => {
                            const { data, color } = item; // 假设data对象中包含color信息，或者从系列中映射
                            const valueDisplay = data.value !== '0' ? `${data.value}` : ''; // 处理值为0的情况
                            return data.value != '0'
                                ? `
                              <div style="display:flex;align-items:center;">
                                  <div style="width:8px;height:8px;margin-right:5px;background-color:${color};"></div>
                                  <div>${data.type}:${data.name}</div>
                              </div>`
                                : '';
                            // <span style="margin-left:5px;">${valueDisplay}</span>
                        })
                        .join('');
                    return titles + content;
                }
            },
            grid: {
                top: '10%',
                left: '2%',
                bottom: '2%',
                right: 130,
                containLabel: true
            },
            xAxis: {
                type: 'category',
                name: '地块编号',
                data: arrx,
                interval: 0, // 设置x轴刻度标签间隔为0，尝试显示所有标签
                axisTick: {
                    alignWithLabel: true // 让刻度线对齐标签
                },
                axisLine: {
                    // 修改轴线颜色
                    // lineStyle: {
                    //     color: '#feab05' // 指定轴线颜色
                    // }
                },

                axisLabel: {
                    color: '#333', // 这里的颜色应与你原本的轴标签颜色相同，或你想要的颜色
                    interval: 'auto', // 或者使用 'auto' 让ECharts自动决定最佳间隔
                    rotate: 45, // 如果标签过多导致重叠，可以尝试旋转标签文本，例如旋转45度
                    overflow: 'break', // 当文本过长时，自动换行
                    ellipsis: '' // 不使用省略号
                }
            },
            yAxis: {
                type: 'value',
                name: '产量(吨)',
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#feab05'
                        // type: 'dashed'
                    }
                },
                nameTextStyle: {
                    fontSize: 16, // 单位文字大小
                    fontWeight: 'normal', // 单位文字粗细，默认为normal，可选'bold'等
                    color: '#333' // 单位文字颜色
                }
            },
            dataZoom: [
                {
                    show: false,
                    start: 0,
                    end: 100
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
                // {
                //     show: true,
                //     yAxisIndex: 0,
                //     filterMode: 'empty',
                //     width: 30,
                //     height: '80%',
                //     showDataShadow: false,
                //     left: '93%'
                // }
            ],
            series
        };
    };
    // useEffect(() => {
    //     let chartInstance = echarts.init(chartRef.current);
    //     echarts.registerMap('china', geoCoordMap as any);
    //     // 配置项
    //     const option = {
    //         title: {
    //             //标题样式
    //             text: 'ECharts 中国地图',
    //             x: 'center',
    //             textStyle: {
    //                 fontSize: 18,
    //                 color: 'red'
    //             }
    //         },
    //         geo: {
    //             map: 'china'
    //         },
    //         tooltip: {
    //             //这里设置提示框
    //             trigger: 'item', //数据项图形触发
    //             backgroundColor: 'red', //提示框浮层的背景颜色。
    //             //字符串模板(地图): {a}（系列名称），{b}（区域名称），{c}（合并数值）,{d}（无）
    //             formatter: '地区：{b}<br/>模拟数据：{c}'
    //         },
    //         visualMap: {
    //             type: 'piecewise',
    //             pieces: [
    //                 { gt: 500000, label: '>=500000', color: 'orangered' },
    //                 { gt: 100000, lte: 500000, label: '100000-500000', color: 'yellow' },
    //                 { gte: 10, lte: 100000, label: '<=100000', color: 'lightskyblue' }
    //             ]
    //         },

    //         series: [
    //             {
    //                 name: '模拟数据',
    //                 type: 'map',
    //                 mapType: 'china',
    //                 roam: false, //是否开启鼠标缩放和平移漫游
    //                 itemStyle: {
    //                     //地图区域的多边形 图形样式
    //                     normal: {
    //                         //是图形在默认状态下的样式
    //                         label: {
    //                             show: true, //是否显示标签
    //                             textStyle: {
    //                                 color: 'black'
    //                             }
    //                         }
    //                     },
    //                     zoom: 1.5, //地图缩放比例,默认为1
    //                     emphasis: {
    //                         //是图形在高亮状态下的样式,比如在鼠标悬浮或者图例联动高亮时
    //                         label: { show: true }
    //                     }
    //                 },
    //                 top: '3%', //组件距离容器的距离
    //                 data: [
    //                     {
    //                         name: '广东省',
    //                         value: 1000
    //                     }
    //                 ]
    //             }
    //         ]
    //     };
    //     chartInstance.setOption(option);
    // }, []); // 当chinaMapJson变化时重新注册
    const getOptionMap = (): any => {
        return {
            backgroundColor: 'black',
            title: {
                text: '中国地图',
                left: 'center'
            },
            // 提供基础的地理坐标系配置
            geo: {
                map: 'china',
                roam: true // 是否允许缩放和平移
            },
            // 系列配置，这里使用地图系列
            series: [
                {
                    type: 'map',
                    map: 'china',
                    // 数据数组，这里简化处理，实际使用时可传入省份数据及值
                    data: [],
                    // 高亮时的样式
                    emphasis: {
                        label: {
                            show: true
                        }
                    }
                }
            ]
        };
    };
    // 商品生产批次曲线图
    const getOptionGoods = (): any => {
        return {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: (params: any) => {
                    return `
                    <div">
                        <div>${params[0]?.name}</div>
                        <div>生产批次数:${params[0]?.value}</div>
                    </div>`;
                }
            },
            grid: {
                top: '10%',
                left: '4%',
                right: 40,
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                name: '日期',
                axisLine: {
                    // 修改轴线颜色
                    // lineStyle: {
                    //     color: '#feab05' // 指定轴线颜色
                    // }
                },
                axisLabel: {
                    // 确保轴标签颜色保持不变
                    color: '#333' // 这里的颜色应与你原本的轴标签颜色相同，或你想要的颜色
                },
                data: ProductionCountGroupMonthData?.prompt
            },
            yAxis: {
                type: 'value',
                name: '批次数',
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#feab05'
                        // type: 'dashed'
                    }
                },
                nameTextStyle: {
                    fontSize: 16, // 单位文字大小
                    fontWeight: 'normal', // 单位文字粗细，默认为normal，可选'bold'等
                    color: '#333' // 单位文字颜色
                }
            },
            dataZoom: [
                {
                    show: false,
                    start: 0,
                    end: 100
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
            ],
            series: [
                {
                    data: ProductionCountGroupMonthData?.y,
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        // 使用lineStyle来定义线条样式
                        color: '#729283' // 将线条颜色设置为红色
                    },
                    symbol: 'circle', // 设置数据点的形状，这里是圆形
                    symbolSize: 6, // 设置数据点的大小
                    itemStyle: {
                        // 设置数据点样式
                        normal: {
                            color: '#386642' // 设置数据点颜色
                        }
                    },
                    areaStyle: {
                        // 配置渐变色
                        color: new echarts.graphic.LinearGradient(
                            0,
                            0,
                            0,
                            1,
                            [
                                { offset: 0, color: 'rgba(58, 129, 74, 0.5)' }, // 使用十六进制颜色值，并添加透明度
                                { offset: 1, color: 'rgba(56, 102, 66, 0)' } // 渐变为透明
                            ],
                            false
                        ),
                        // 配置阴影
                        shadowColor: '#386642',
                        shadowBlur: 10
                    }
                }
            ]
        };
    };

    // 商品生产批次-饼图
    const getOptionGoodsPie = (): any => {
        return {
            tooltip: {
                trigger: 'item',
                // formatter: '{b}: {c} ({d}%)'
                formatter: '{b}:{d}%'
            },
            // legend: {
            //     orient: 'vertical',
            //     top: '60%', // 离底部 10% 的位置
            //     left: '70%', // 离右边 10% 的位置
            //     selectedMode: false
            // },

            legend: {
                orient: 'vertical',
                top: '0%',
                left: '67%',
                selectedMode: false,
                width: 200, // 增加宽度
                itemWidth: 10, // 减少图例项的宽度
                itemHeight: 10, // 减少图例项的高度
                textStyle: { fontSize: 12 }, // 减少字体大小
                formatter: function (name: any) {
                    // 自定义格式化函数，根据需要进行换行
                    // return name.replace(/(.{15})/g, '$1\n');
                    return name.length > 5 ? name.substring(0, 5) + '...' : name;
                },
                scrollable: true // 开启滚动条
            },
            series: [
                {
                    // name: 'Access From',
                    type: 'pie',
                    radius: ['40%', '60%'],
                    center: ['40%', '45%'], // 使饼图中心偏上
                    avoidLabelOverlap: false,
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: false,
                            fontSize: 20,
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: ratioGroupProductPicdata
                }
            ]
        };
    };

    // 溯源查询曲线图
    const getOptionWar = (): any => {
        return {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: (params: any) => {
                    return `
                  <div">
                      <div>${params[0]?.name}</div>
                      <div>入库数量: ${params[0]?.value}袋</div>
                  </div>`;
                }
            },
            grid: {
                top: '10%',
                left: '4%',
                right: 60,
                bottom: '6%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                name: '日期',
                axisLine: {
                    // 修改轴线颜色
                    // lineStyle: {
                    //     color: '#feab05' // 指定轴线颜色
                    // }
                },
                axisLabel: {
                    // 确保轴标签颜色保持不变
                    color: '#333' // 这里的颜色应与你原本的轴标签颜色相同，或你想要的颜色
                },
                data: getTraceCountDayDataWar?.datas?.x
            },
            yAxis: {
                type: 'value',
                name: '入库数量',
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#feab05'
                        // type: 'dashed'
                    }
                },
                nameTextStyle: {
                    fontSize: 16, // 单位文字大小
                    fontWeight: 'normal', // 单位文字粗细，默认为normal，可选'bold'等
                    color: '#333' // 单位文字颜色
                }
            },
            dataZoom: [
                {
                    show: false,
                    start: 0,
                    end: 100
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
            ],
            series: [
                {
                    data: getTraceCountDayDataWar?.datas?.y,
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        // 使用lineStyle来定义线条样式
                        color: '#729283' // 将线条颜色设置为红色
                    },
                    symbol: 'circle', // 设置数据点的形状，这里是圆形
                    symbolSize: 6, // 设置数据点的大小
                    itemStyle: {
                        // 设置数据点样式
                        normal: {
                            color: '#386642' // 设置数据点颜色
                        }
                    },
                    areaStyle: {
                        // 配置渐变色
                        color: new echarts.graphic.LinearGradient(
                            0,
                            0,
                            0,
                            1,
                            [
                                { offset: 0, color: 'rgba(58, 129, 74, 0.5)' }, // 使用十六进制颜色值，并添加透明度
                                { offset: 1, color: 'rgba(56, 102, 66, 0)' } // 渐变为透明
                            ],
                            false
                        ),
                        // 配置阴影
                        shadowColor: '#386642',
                        shadowBlur: 10
                    }
                }
            ]
        };
    };
    // 仓储概览

    const getOptionSu = (): any => {
        return {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: (params: any) => {
                    return `
                    <div">
                        <div>${params[0]?.name}</div>
                        <div>查询次数: ${params[0]?.value}次</div>
                    </div>`;
                }
            },
            grid: {
                top: '10%',
                left: '4%',
                right: 60,
                bottom: '6%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                name: '日期',
                axisLine: {
                    // 修改轴线颜色
                    // lineStyle: {
                    //     color: '#feab05' // 指定轴线颜色
                    // }
                },
                axisLabel: {
                    // 确保轴标签颜色保持不变
                    color: '#333' // 这里的颜色应与你原本的轴标签颜色相同，或你想要的颜色
                },
                data: getTraceCountDayData?.x
            },
            yAxis: {
                type: 'value',
                name: '查询次数',
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#feab05'
                        // type: 'dashed'
                    }
                },
                nameTextStyle: {
                    fontSize: 16, // 单位文字大小
                    fontWeight: 'normal', // 单位文字粗细，默认为normal，可选'bold'等
                    color: '#333' // 单位文字颜色
                }
            },
            dataZoom: [
                {
                    show: false,
                    start: 0,
                    end: 100
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
            ],
            series: [
                {
                    data: getTraceCountDayData?.y,
                    type: 'line',
                    smooth: true,
                    lineStyle: {
                        // 使用lineStyle来定义线条样式
                        color: '#729283' // 将线条颜色设置为红色
                    },
                    symbol: 'circle', // 设置数据点的形状，这里是圆形
                    symbolSize: 6, // 设置数据点的大小
                    itemStyle: {
                        // 设置数据点样式
                        normal: {
                            color: '#386642' // 设置数据点颜色
                        }
                    },
                    areaStyle: {
                        // 配置渐变色
                        color: new echarts.graphic.LinearGradient(
                            0,
                            0,
                            0,
                            1,
                            [
                                { offset: 0, color: 'rgba(58, 129, 74, 0.5)' }, // 使用十六进制颜色值，并添加透明度
                                { offset: 1, color: 'rgba(56, 102, 66, 0)' } // 渐变为透明
                            ],
                            false
                        ),
                        // 配置阴影
                        shadowColor: '#386642',
                        shadowBlur: 10
                    }
                }
            ]
        };
    };

    // 商品生产批次曲线图
    const getOptionSales = (): any => {
        return {
            tooltip: {
                trigger: 'axis',
                formatter: (params: any) => {
                    console.log(params);
                    let tooltipContent = '<div>';
                    tooltipContent += `<div style="display: flex; align-items: center;">
                    <span>${params[0].name}</span>
                       </div>
                       ${sale == 1 ? '销售量' : '销售额'}
                           ${params[0].value} ${sale == 1 ? '单' : '元'}
                       <div>


                       </div>
                       `;

                    // params.forEach((param: any, index: any) => {
                    //     tooltipContent += ` <div style="display: flex; align-items: center;">
                    //         <span>${param.seriesName}: ${param.value}${
                    //         param.seriesName == '销售额' ? '元' : '单'
                    //     }</span>
                    //     </div>`;
                    // });

                    tooltipContent += '</div>';
                    return tooltipContent;
                }
            },
            grid: {
                top: '10%',
                left: '4%',
                right: 40,
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                name: '日期',
                axisLine: {
                    // 修改轴线颜色
                    // lineStyle: {
                    //     color: '#feab05' // 指定轴线颜色
                    // }
                },
                axisLabel: {
                    // 确保轴标签颜色保持不变
                    color: '#333' // 这里的颜色应与你原本的轴标签颜色相同，或你想要的颜色
                },
                data: timeArray
            },
            yAxis: {
                type: 'value',
                name: sale == 1 ? '销售量（单）' : '销售额（元）',
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#feab05'
                        // type: 'dashed'
                    }
                },
                nameTextStyle: {
                    fontSize: 16, // 单位文字大小
                    fontWeight: 'normal', // 单位文字粗细，默认为normal，可选'bold'等
                    color: '#333' // 单位文字颜色
                }
            },
            dataZoom: [
                {
                    show: false,
                    start: 0,
                    end: 100
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
            ],
            series: [
                {
                    // name:sale == 1 ?'销售量': '销售额',
                    data: sale == 1 ? salesVolumeArray : salesAmountArray,
                    type: 'line',
                    smooth: false, //将smooth设置为false，以显示直线连接的点
                    lineStyle: {
                        // 使用lineStyle来定义线条样式
                        color: '#c4f16d' // 将线条颜色设置为红色
                    },
                    symbol: 'circle', // 设置数据点的形状，这里是圆形
                    symbolSize: 6, // 设置数据点的大小
                    itemStyle: {
                        // 设置数据点样式
                        normal: {
                            color: '#c0f064' // 设置数据点颜色
                        }
                    }
                }
                // {
                //     name: '销售量',
                //     data: salesVolumeArray,
                //     type: 'line',
                //     smooth: false, //将smooth设置为false，以显示直线连接的点
                //     lineStyle: {
                //         // 使用lineStyle来定义线条样式
                //         color: '#729283' // 将线条颜色设置为红色
                //     },
                //     symbol: 'circle', // 设置数据点的形状，这里是圆形
                //     symbolSize: 6, // 设置数据点的大小
                //     itemStyle: {
                //         // 设置数据点样式
                //         normal: {
                //             color: '#386642' // 设置数据点颜色
                //         }
                //     }
                // }
            ]
        };
    };
    /**
     * 左右两边折线图
     const getOptionSales = (): any => {
    return {
        tooltip: {
            trigger: 'axis',
            formatter: (params: any) => {
                console.log(params);

                let tooltipContent = '<div>';
                tooltipContent += `<div style="display: flex; align-items: center;">
                  <span>${params[0].name}</span>
                </div>`;

                params.forEach((param: any, index: any) => {
                    tooltipContent += ` <div style="display: flex; align-items: center;">
                      <span>${param.seriesName}: ${param.value}${param.seriesName === '销售额' ? '元' : '单'}</span>
                    </div>`;
                });

                tooltipContent += '</div>';
                return tooltipContent;
            }
        },
        grid: {
            top: '10%',
            left: '2%',
            right: 40,
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            name: '日期',
            axisLine: {},
            axisLabel: {
                color: '#333'
            },
            data: timeArray
        },
        yAxis: [
            // 第一个 y 轴 - 用于销售量
            {
                type: 'value',
                name: '销售量',
                position: 'left',
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#feab05'
                    }
                },
                nameTextStyle: {
                    fontSize: 16,
                    fontWeight: 'normal',
                    color: '#333'
                }
            },
            // 第二个 y 轴 - 用于销售额
            {
                type: 'value',
                name: '销售额',
                position: 'right',
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#feab05'
                    }
                },
                nameTextStyle: {
                    fontSize: 16,
                    fontWeight: 'normal',
                    color: '#333'
                }
            }
        ],
        dataZoom: [
            {
                show: false,
                start: 0,
                end: 100
            },
            {
                type: 'inside',
                start: 0,
                end: 100
            }
        ],
        series: [
            {
                name: '销售额',
                data: salesAmountArray,
                type: 'line',
                smooth: false,
                yAxisIndex: 1, // 指定使用第二个 y 轴
                lineStyle: {
                    color: '#c4f16d'
                },
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                    normal: {
                        color: '#c0f064'
                    }
                }
            },
            {
                name: '销售量',
                data: salesVolumeArray,
                type: 'line',
                smooth: false,
                yAxisIndex: 0, // 指定使用第一个 y 轴
                lineStyle: {
                    color: '#729283'
                },
                symbol: 'circle',
                symbolSize: 6,
                itemStyle: {
                    normal: {
                        color: '#386642'
                    }
                }
            }
        ]
    };
};
     */
    // 轮播图
    const originalImages = productImageList?.data?.data || [];
    console.log(productImageList?.data?.data);

    // 表格滚动
    const handleScroll = (event: any) => {
        const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
        if (scrollTop + clientHeight >= scrollHeight && hasMore) {
            setpageIndex(pageIndex + 1);
            // pageInfo.pageIndex += 1;
            // 当距离底部小于10px时加载
            // handlePaginationChange(pageInfo.pageIndex); // 触发分页变化
            // 当距离底部小于10px时加载
            // setPage(page + 1); // 加载下一页数据
        }
    };

    // 最近质检批次列表

    const handleScrollQualityPage = (event: any) => {
        const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
        console.log(scrollTop + clientHeight);
        console.log(scrollHeight);
        if (Math.ceil(scrollTop + clientHeight) >= scrollHeight && hasMore) {
            setpageIndexQuality(pageIndexQuality + 1);
            // pageInfo.pageIndex += 1;
            // 当距离底部小于10px时加载
            // handlePaginationChange(pageInfo.pageIndex); // 触发分页变化
            // 当距离底部小于10px时加载
            // setPage(page + 1); // 加载下一页数据
        }
    };
    // 收购列表滚动
    const handleScrollPurchaseList = (event: any) => {
        const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
        console.log(scrollTop + clientHeight);
        console.log(scrollHeight);
        if (scrollTop + clientHeight >= scrollHeight && hasMore) {
            setpageIndexPurchase(pageIndexPurchase + 1);
            // pageInfo.pageIndex += 1;
            // 当距离底部小于10px时加载
            // handlePaginationChange(pageInfo.pageIndex); // 触发分页变化
            // 当距离底部小于10px时加载
            // setPage(page + 1); // 加载下一页数据
        }
    };

    // 收购列表
    const [tableDataPurchaseList, setTableDataPurchaseList] = useState<any[]>([]);

    const traceFoodrankPurchaseList = useQuery(
        ['traceFoodrankPurchaseList', pageIndexPurchase],
        () => {
            return OverViewService.getMaterialPurchaseList({
                // coreId: Number(userInfo?.coreId),
                // sortBy: sortBY,
                pageIndex: pageIndexPurchase,
                pageSize: page.pageSize
            });
        },
        {
            onSuccess: (data) => {
                setTableDataPurchaseList((prevData) => {
                    console.log(prevData, 'prevData');
                    const newData = data?.data?.records?.map((item: any) => {
                        const sowTime = dayjs(item.sowTime).format('YYYY-MM-DD HH:mm:ss');
                        return {
                            ...item,
                            sowTime: sowTime
                        };
                    });
                    return [...prevData, ...newData];
                });
                console.log(data?.data?.records);
                if (data?.data?.records?.length === 0) {
                    setHasMore(false); // 如果没有更多数据，则设置 `hasMore` 为 `false`
                }
            }
        }
    );
    // getMaterialPurchaseList
    return (
        <>
            <div className={styles.displayCard}>
                {userInfo.userInfo.identity === 1 ? (
                    <div className={styles.Column}>
                        <BaseCard
                            className={styles.overViewContainer}
                            title={<PageTitle title='产品概览' bg='container title' />}
                        >
                            <div className={styles.Details} onClick={() => navigate('/product-manage/food')}>
                                查看详情&gt;
                            </div>
                            <img src={require('../../assets/landicon/mai.png')} className={styles.mai} alt='' />

                            <div className={styles.overViewContainerTop}>
                                <div className={styles.overViewContainerTopLeft}>
                                    <div className={styles.columnTitle}>产品展示</div>
                                    {originalImages.length > 4 ? (
                                        <Carousel slidesToShow={4} slidesToScroll={1} dots={false} autoplay>
                                            {originalImages.map((item: any, index: number) => (
                                                <div className='silder-card' key={index}>
                                                    <div key={index}>
                                                        <Image
                                                            src={item.productImg}
                                                            preview={false} // 关闭预览功能
                                                            // fallback={img4}
                                                            style={{
                                                                width: '160px',
                                                                height: '100px',
                                                                margin: '0 20px'
                                                            }}
                                                        />
                                                        <p
                                                            className='image-slider_top singleText'
                                                            style={{
                                                                width: '160px',
                                                                textAlign: 'center',
                                                                margin: '0 30px',
                                                                overflow: 'hidden',
                                                                textOverflow: 'ellipsis',
                                                                whiteSpace: 'nowrap'
                                                            }}
                                                            title={item.productName}
                                                        >
                                                            {item.productName ? item.productName : '-'}
                                                        </p>
                                                    </div>
                                                </div>
                                            ))}
                                        </Carousel>
                                    ) : (
                                        <div className={styles.silderCard}>
                                            {originalImages.map((item: any, index: number) => (
                                                <div key={index}>
                                                    <Image
                                                        src={item.productImg}
                                                        preview={false} // 关闭预览功能
                                                        // fallback={img4}
                                                        style={{ width: '8vw', height: '120px', margin: '0 30px' }}
                                                    />
                                                    <p
                                                        // className='image-slider_top singleText'
                                                        style={{
                                                            width: '8vw',
                                                            textAlign: 'center',
                                                            margin: '0 30px',
                                                            overflow: 'hidden',
                                                            textOverflow: 'ellipsis',
                                                            whiteSpace: 'nowrap'
                                                        }}
                                                        title={item.productName}
                                                    >
                                                        {item.productName ? item.productName : '-'}
                                                    </p>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>

                                <div className={styles.overViewContainerTopRight}>
                                    <div className={styles.columnTitle}>上链商品数</div>
                                    <div className={styles.chainNum}>
                                        <div className={styles.chainNumSubscript}>
                                            {ChainProductCountData}
                                            <span style={{ fontSize: '14px', fontWeight: 600 }}>种</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </BaseCard>

                        {/* 种植概览 */}
                        <BaseCard
                            className={styles.overViewContainer}
                            style={{ marginTop: '30px' }}
                            title={<PageTitle title='种植概览' bg='container title' />}
                        >
                            <div className={styles.Details} onClick={() => navigate('/land/list')}>
                                查看详情&gt;
                            </div>
                            <img src={require('../../assets/landicon/mai.png')} className={styles.mai} alt='' />

                            <div className={styles.overViewContainerTop}>
                                <div className={styles.overViewContainerTopLeft}>
                                    <div>
                                        <div className={styles.weather}>
                                            <div className={styles.columnTitle}>气象数据</div>
                                            <div className={styles.time}>
                                                最近更新时间:
                                                {dayjs(LandWeatherData?.lastUpdateTime).format('YYYY-MM-DD HH:mm:ss')}
                                            </div>
                                        </div>
                                        <div className={styles.pubilc}>
                                            <div className={styles.pubilcDiv}>
                                                <div className={styles.textTitle}>光照度</div>
                                                <div className={styles.subscript}>
                                                    {LandWeatherData?.tsr}
                                                    <span style={{ fontSize: '14px' }}>lx</span>
                                                </div>
                                            </div>

                                            <div className={styles.pubilcDiv}>
                                                <div className={styles.textTitle}>温度</div>
                                                <div className={styles.subscript}>
                                                    {LandWeatherData?.temperature}
                                                    <span style={{ fontSize: '14px' }}>℃</span>
                                                </div>
                                            </div>

                                            <div className={styles.pubilcDiv}>
                                                <div className={styles.textTitle}>相对湿度</div>
                                                <div className={styles.subscript}>
                                                    {LandWeatherData?.humidity}
                                                    <span style={{ fontSize: '14px' }}>%</span>
                                                </div>
                                            </div>

                                            <div className={styles.pubilcDiv}>
                                                <div className={styles.textTitle}>降雨量</div>
                                                <div className={styles.subscript}>
                                                    {LandWeatherData?.rainfall}
                                                    <span style={{ fontSize: '14px' }}>mm</span>
                                                </div>
                                            </div>

                                            <div className={styles.pubilcDiv}>
                                                <div className={styles.textTitle}>大气压</div>
                                                <div className={styles.subscript}>
                                                    {LandWeatherData?.atm}
                                                    <span style={{ fontSize: '14px' }}>hPa</span>
                                                </div>
                                            </div>

                                            <div className={styles.pubilcDiv}>
                                                <div className={styles.textTitle}>风速</div>
                                                <div className={styles.subscript}>
                                                    {LandWeatherData?.windSpeed}
                                                    <span style={{ fontSize: '14px' }}>m/s</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div className={styles.weather}>
                                        <div className={styles.columnTitle}>地块产量分类柱状图</div>
                                        {/* <div>
              最近更新时间:
              {dayjs(LandWeatherData?.lastUpdateTime).format('YYYY-MM-DD HH:mm:ss')}
          </div> */}
                                    </div>
                                    <div>
                                        <div style={{ textAlign: 'right', paddingRight: 130 }}>
                                            <span>时间: </span>
                                            <Select
                                                defaultValue='本年度'
                                                onChange={handleChange}
                                                options={[
                                                    { value: '本年度', label: '本年度' },
                                                    { value: '近三年', label: '近三年' }
                                                ]}
                                            />
                                        </div>

                                        <ReactEcharts
                                            option={getOption()}
                                            opts={{ renderer: 'svg' }}
                                            style={{ height: '340px' }}
                                            theme='clear'
                                        />
                                    </div>
                                </div>

                                <div className={styles.overViewContainerTopRight}>
                                    <div className={styles.weather}>
                                        <div className={styles.columnTitle}>墒情数据</div>
                                        <div className={styles.time}>
                                            最近更新时间:
                                            {dayjs(addLandWeatherData?.lastUpdateTime).format('YYYY-MM-DD HH:mm:ss')}
                                        </div>
                                    </div>

                                    <div className={styles.pubilc}>
                                        <div className={styles.pubilcDiv}>
                                            <div className={styles.textTitle}>土壤温度</div>
                                            <div className={styles.subscript}>
                                                {addLandWeatherData?.soilTemperature5cm}
                                                <span style={{ fontSize: '14px' }}>℃</span>
                                            </div>
                                        </div>

                                        <div className={styles.pubilcDiv}>
                                            <div className={styles.textTitle}>土壤PH值</div>
                                            <div className={styles.subscript}>
                                                {addLandWeatherData?.soilMoisture5cm}
                                                <span style={{ fontSize: '14px' }}></span>
                                            </div>
                                        </div>

                                        <div className={styles.pubilcDiv}>
                                            <div className={styles.textTitle}>土壤电导率</div>
                                            <div className={styles.subscript}>
                                                {addLandWeatherData?.conductivityMuSe3}
                                                <span style={{ fontSize: '14px' }}>uS/cm</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div className={styles.weatherLand}>
                                        <div className={styles.columnTitle}>地块实时种植作物表</div>
                                        <div
                                            className='custom-table-wrap'
                                            style={{ height: 320, overflow: 'auto', border: '1px solid #92c37b' }} // 设置外部容器的高度和滚动条
                                            onScroll={handleScroll} // 添加滚动监听器
                                            // ref={containerRef} // 添加参考
                                        >
                                            <BaseTable
                                                rowKey='account'
                                                className={styles.TableContainer}
                                                // scroll={{ y: 400 }}
                                                columns={listColumnLand}
                                                dataSource={tableDataPlantList || []}
                                                // onChange={(_: any, filters: any, sorter: any) => {
                                                //     console.log('sorter', sorter, sorter?.order);
                                                //     setSortBy(sorter?.order);
                                                // }}
                                                loading={traceFoodrank?.isLoading}
                                                pagination={false}
                                            />
                                            {/* <div className={styles.down}>到底了</div> */}

                                            {/* <Table
                  className={styles.TableContainer}
                  // scroll={{ y: 400 }}
                  columns={listColumnLand}
                  dataSource={tableData || []}
                  // onChange={(_: any, filters: any, sorter: any) => {
                  //     console.log('sorter', sorter, sorter?.order);
                  //     setSortBy(sorter?.order);
                  // }}
                  loading={traceFoodrank?.isLoading}
                  pagination={false}
              /> */}
                                        </div>

                                        {/* <BasePagination
              shouldShowTotal
              showQuickJumper
              showSizeChanger
              current={pageInfo.pageIndex}
              pageSize={pageInfo.pageSize}
              total={traceFoodrank?.data?.data?.total}
              onShowSizeChange={handlePaginationChange}
              onChange={handlePaginationChange}
          /> */}
                                        {/* <div className={styles.time}>
              最近更新时间:
              {dayjs(addLandWeatherData?.lastUpdateTime).format('YYYY-MM-DD HH:mm:ss')}
          </div> */}
                                    </div>
                                </div>
                            </div>
                        </BaseCard>
                        {/* 收购概览 */}
                        <BaseCard
                            className={styles.overViewContainer}
                            style={{ marginTop: '30px' }}
                            title={<PageTitle title='收购概览' bg='container title' />}
                        >
                            <div className={styles.Details} onClick={() => navigate('/purchase/list')}>
                                查看详情&gt;
                            </div>
                            <img src={require('../../assets/landicon/mai.png')} className={styles.mai} alt='' />
                            <div className={styles.overViewContainerTop}>
                                <div className={styles.overViewContainerTopPurchase}>
                                    <div className={styles.weather}>
                                        <div className={styles.columnTitle}>最近收购信息</div>
                                    </div>
                                    <div
                                        style={{
                                            border: '1px solid #92c37b'
                                        }}
                                    >
                                        <div
                                            className='custom-table-wrap margin50'
                                            style={{
                                                height: 260,
                                                overflow: 'auto'
                                            }} // 设置外部容器的高度和滚动条
                                            onScroll={handleScrollPurchaseList} // 添加滚动监听器
                                            // ref={containerRef} // 添加参考
                                        >
                                            <BaseTable
                                                rowKey='account'
                                                className={styles.TableContainer}
                                                columns={listColumnPurchase}
                                                dataSource={tableDataPurchaseList || []}
                                                loading={traceFoodrankPurchaseList?.isLoading}
                                                pagination={false}
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className={styles.overViewContainerTopRightPurchase}>
                                    <div>
                                        {[purchaseConfig]?.map((config: any, index: number) => (
                                            <CardTips key={index} {...config} />
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </BaseCard>

                        <BaseCard
                            className={styles.overViewContainer}
                            style={{ marginTop: '30px' }}
                            title={<PageTitle title='生产概览' bg='container title' />}
                        >
                            <div className={styles.Details} onClick={() => navigate('/product/process')}>
                                查看详情&gt;
                            </div>
                            <img src={require('../../assets/landicon/mai.png')} className={styles.mai} alt='' />
                            <div className={styles.overViewContainerTop}>
                                <div className={styles.overViewContainerTopGoods}>
                                    <div className={styles.weather}>
                                        <div className={styles.columnTitle}>商品生产批次曲线图</div>
                                        {/* <div>
                                      最近更新时间:
                                      {dayjs(LandWeatherData?.lastUpdateTime).format('YYYY-MM-DD HH:mm:ss')}
                                  </div> */}
                                    </div>
                                    <div>
                                        <ReactEcharts
                                            option={getOptionGoods()}
                                            opts={{ renderer: 'svg' }}
                                            style={{ height: '350px' }}
                                            theme='clear'
                                        />
                                    </div>

                                    {/* <div>
                                  <ReactEcharts
                                      option={getOptionGoodsPie()}
                                      opts={{ renderer: 'svg' }}
                                      style={{ height: '380px' }}
                                      theme='clear'
                                  />
                              </div> */}
                                </div>

                                <div className={styles.overViewContainerTopRightGoodsPie}>
                                    <div className={styles.weather}>
                                        <div className={styles.columnTitle}>商品生产批次分布-环形图</div>
                                        {/* <div>
                                      最近更新时间:
                                      {dayjs(LandWeatherData?.lastUpdateTime).format('YYYY-MM-DD HH:mm:ss')}
                                  </div> */}
                                    </div>
                                    <div>
                                        <div>
                                            <Select
                                                defaultValue='总批次'
                                                onChange={handleChangePic}
                                                options={[
                                                    { value: '总批次', label: '总批次' },
                                                    { value: '当月新增', label: '当月新增' }
                                                ]}
                                            />
                                        </div>
                                        <ReactEcharts
                                            option={getOptionGoodsPie()}
                                            opts={{ renderer: 'svg' }}
                                            style={{ height: '310px' }}
                                            theme='clear'
                                        />
                                    </div>
                                </div>

                                <div className={styles.overViewContainerTopRightGoods}>
                                    <div>
                                        {
                                            // [traceabilityConfig, productConfig, sourceConfig, qcqaConfig, transformConfig]
                                            [productConfig]?.map((config: any, index: number) => (
                                                <CardTips key={index} {...config} />
                                            ))
                                        }
                                    </div>
                                </div>
                            </div>
                        </BaseCard>

                        <BaseCard
                            className={styles.overViewContainer}
                            mt24
                            style={{ marginTop: '30px' }}
                            title={<PageTitle title='质检概览' bg='container title' />}
                        >
                            <div className={styles.Details} onClick={() => navigate('/qcqa/list')}>
                                查看详情&gt;
                            </div>
                            <img src={require('../../assets/landicon/mai.png')} className={styles.mai} alt='' />
                            <img src={require('../../assets/landicon/mai.png')} className={styles.mai} alt='' />
                            <div className={styles.product}>
                                <div className={styles.product_left}>
                                    <div className={styles.weather}>
                                        <div className={styles.columnTitle}>最近质检信息</div>
                                        {/* <div>
                                      最近更新时间:
                                      {dayjs(LandWeatherData?.lastUpdateTime).format('YYYY-MM-DD HH:mm:ss')}
                                  </div> */}
                                    </div>
                                    <div
                                        style={{
                                            border: '1px solid #92c37b'
                                        }}
                                    >
                                        <div
                                            className='custom-table-wrap margin60'
                                            style={{ height: 285, overflow: 'auto' }} // 设置外部容器的高度和滚动条
                                            onScroll={handleScrollQualityPage} // 添加滚动监听器
                                            // ref={containerRef} // 添加参考
                                        >
                                            <BaseTable
                                                rowKey='account'
                                                className={styles.TableContainer}
                                                // scroll={{ y: 400 }}
                                                columns={listColumnQuality}
                                                dataSource={tableDataQualityPage || []}
                                                // onChange={(_: any, filters: any, sorter: any) => {
                                                //     console.log('sorter', sorter, sorter?.order);
                                                //     setSortBy(sorter?.order);
                                                // }}
                                                loading={traceFoodrank?.isLoading}
                                                pagination={false}
                                            />
                                            {/* <Table
                                      className={styles.TableContainer}
                                      rowKey='account'
                                      // scroll={{ y: 400 }}
                                      columns={listColumnQuality}
                                      dataSource={tableDataQualityPage || []}
                                      onChange={(_: any, filters: any, sorter: any) => {
                                          console.log('sorter', sorter, sorter?.order);
                                          setSortBy(sorter?.order);
                                      }}
                                      loading={traceFoodrank?.isLoading}
                                      pagination={false}
                                  /> */}
                                        </div>
                                    </div>

                                    {/* <BasePagination
                                  shouldShowTotal
                                  showQuickJumper
                                  showSizeChanger
                                  current={pageInfo.pageIndex}
                                  pageSize={pageInfo.pageSize}
                                  total={traceFoodrank?.data?.data?.total}
                                  onShowSizeChange={handlePaginationChange}
                                  onChange={handlePaginationChange}
                              /> */}
                                </div>

                                <div className={styles.product_right}>
                                    <div className={styles.overViewContainerTopRow}>
                                        <div className={styles.overViewContainerTopRight}>
                                            <div className={styles.columnTitleLeft}>质检总批次</div>
                                            <div className={styles.chainNum}>
                                                <div className={styles.chainNumSubscript}>
                                                    {qualityInfoData?.sum || 0}
                                                    <span style={{ fontSize: '14px', fontWeight: 600 }}>批次</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div className={styles.overViewContainerTopRight}>
                                            <div className={styles.columnTitleLeft}>
                                                当月增加
                                                <img src={Top} alt='' className={styles.increaseIcon} />
                                            </div>
                                            <div className={styles.chainNum}>
                                                <div className={styles.chainNumSubscript}>
                                                    {qualityInfoData?.addSumMonth || 0}
                                                    <span style={{ fontSize: '14px', fontWeight: 600 }}>批次</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className={styles.overViewContainerTopRow}>
                                        <div className={styles.overViewContainerTopRight}>
                                            <div className={styles.columnTitleLeft}>待质检批次数</div>
                                            <div className={styles.chainNum}>
                                                <div className={styles.chainNumSubscript}>
                                                    {qualityInfoData?.waitCount || 0}
                                                    <span style={{ fontSize: '14px', fontWeight: 600 }}>批次</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div className={styles.overViewContainerTopRight}>
                                            <div className={styles.columnTitleLeft}>
                                                近半年合格率
                                                <img src={Top} alt='' className={styles.increaseIcon} />
                                            </div>
                                            <div className={styles.chainNum}>
                                                <div className={styles.chainNumSubscript}>
                                                    {qualityInfoData?.ratioInSixMonths || 0}
                                                    <span style={{ fontSize: '18px', fontWeight: 800 }}>%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </BaseCard>

                        {/* 仓储 */}
                        {getHasPermissWarehouseData && (
                            <BaseCard
                                className={styles.overViewContainer}
                                style={{ marginTop: '30px' }}
                                title={<PageTitle title='仓储概览' bg='container title' />}
                            >
                                <div className={styles.Details} onClick={() => navigate('/warehouse/list')}>
                                    查看详情&gt;
                                </div>
                                <img src={require('../../assets/landicon/mai.png')} className={styles.mai} alt='' />
                                <div className={styles.overViewContainerTop}>
                                    <div className={styles.overViewContainerTopOverView}>
                                        <div className={styles.weather}>
                                            <div className={styles.columnTitle}>仓储数量曲线图</div>
                                            {/* <div>
            最近更新时间:
            {dayjs(LandWeatherData?.lastUpdateTime).format('YYYY-MM-DD HH:mm:ss')}
        </div> */}
                                        </div>
                                        <div>
                                            <ReactEcharts
                                                option={getOptionWar()}
                                                opts={{ renderer: 'svg' }}
                                                style={{ height: '440px' }}
                                                theme='clear'
                                            />
                                        </div>
                                    </div>

                                    <div className={styles.overViewContainerTopRightGoods}>
                                        <div>
                                            {
                                                // [traceabilityConfig, productConfig, sourceConfig, qcqaConfig, transformConfig]
                                                [warehouseConfig]?.map((config: any, index: number) => (
                                                    <CardTips key={index} {...config} />
                                                ))
                                            }
                                        </div>
                                    </div>
                                </div>
                            </BaseCard>
                        )}

                        {/* 溯源概览 */}
                        <BaseCard
                            className={styles.overViewContainer}
                            style={{ marginTop: '30px' }}
                            title={<PageTitle title='溯源概览' bg='container title' />}
                        >
                            <div className={styles.Details} onClick={() => navigate('/code-manage/code')}>
                                查看详情&gt;
                            </div>
                            <img src={require('../../assets/landicon/mai.png')} className={styles.mai} alt='' />
                            <div className={styles.overViewContainerTop}>
                                <div className={styles.overViewContainerTopOverView}>
                                    <div className={styles.weather}>
                                        <div className={styles.columnTitle}>溯源查询曲线图</div>
                                        {/* <div>
                                      最近更新时间:
                                      {dayjs(LandWeatherData?.lastUpdateTime).format('YYYY-MM-DD HH:mm:ss')}
                                  </div> */}
                                    </div>
                                    <div>
                                        <ReactEcharts
                                            option={getOptionSu()}
                                            opts={{ renderer: 'svg' }}
                                            style={{ height: '440px' }}
                                            theme='clear'
                                        />
                                    </div>
                                </div>

                                <div className={styles.overViewContainerTopRightGoods}>
                                    <div>
                                        {
                                            // [traceabilityConfig, productConfig, sourceConfig, qcqaConfig, transformConfig]
                                            [traceabilityConfig]?.map((config: any, index: number) => (
                                                <CardTips key={index} {...config} />
                                            ))
                                        }
                                    </div>
                                </div>
                            </div>
                        </BaseCard>

                        {/* 销售 */}

                        <BaseCard
                            className={styles.overViewContainer}
                            style={{ marginTop: '30px' }}
                            title={<PageTitle title='销售概览' bg='container title' />}
                        >
                            <div className={styles.Details} onClick={() => navigate('/market/list')}>
                                查看详情&gt;
                            </div>
                            <img src={require('../../assets/landicon/mai.png')} className={styles.mai} alt='' />
                            <div className={styles.MakerTop}>
                                <div className={styles.MakerTopLeft}>
                                    <div>
                                        <div className={styles.columnTitleMaker}>销售总量</div>
                                        <div className={styles.chainNumMaker}>
                                            <div className={styles.chainNumSubscript}>
                                                {getOverviewInfoData?.totalSalesVolume || '0'}
                                                <span style={{ fontSize: '14px' }}>单</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div className={styles.columnTitleMaker}>当月销售量</div>
                                        <div className={styles.chainNumMaker}>
                                            <div className={styles.chainNumSubscript}>
                                                {getOverviewInfoData?.monthSalesVolume || '0'}
                                                <span style={{ fontSize: '14px' }}>单</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div className={styles.columnTitleMaker}>当日销售量</div>
                                        <div className={styles.chainNumMaker}>
                                            <div className={styles.chainNumSubscript}>
                                                {getOverviewInfoData?.daySalesVolume || '0'}
                                                <span style={{ fontSize: '14px' }}>单</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className={styles.MakerTopRight}>
                                    <div>
                                        <div className={styles.columnTitleMaker}>销售总额</div>
                                        <div className={styles.chainNumMaker}>
                                            <div className={styles.chainNumSubscript}>
                                                {getOverviewInfoData?.totalSalesAmount || '0'}
                                                <span style={{ fontSize: '14px' }}>元</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div className={styles.columnTitleMaker}>当月销售额</div>
                                        <div className={styles.chainNumMaker}>
                                            <div className={styles.chainNumSubscript}>
                                                {getOverviewInfoData?.monthSalesRevenue || '0'}
                                                <span style={{ fontSize: '14px' }}>元</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div className={styles.columnTitleMaker}>当日销售额</div>
                                        <div className={styles.chainNumMaker}>
                                            <div className={styles.chainNumSubscript}>
                                                {getOverviewInfoData?.daySalesRevenue || '0'}
                                                <span style={{ fontSize: '14px' }}>元</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className={styles.overViewContainerTop}>
                                <div className={styles.overViewContainerTopGoodsMarket}>
                                    <div className={styles.weather}>
                                        <div className={styles.columnTitle}>销售量/额折线图</div>
                                    </div>
                                    <div>
                                        <div style={{ textAlign: 'right' }}>
                                            <Select
                                                defaultValue='销售量'
                                                onChange={handleChangeSale}
                                                options={[
                                                    { value: '销售量', label: '销售量' },
                                                    { value: '销售额', label: '销售额' }
                                                ]}
                                            />
                                        </div>
                                        <ReactEcharts
                                            option={getOptionSales()}
                                            opts={{ renderer: 'svg' }}
                                            style={{ height: '380px' }}
                                            theme='clear'
                                        />
                                    </div>
                                </div>

                                <div className={styles.overViewContainerTopRightGoodsPieMarket}>
                                    <div className={styles.weather}>
                                        <div className={styles.columnTitle}>销售区地域分布-环形图</div>
                                    </div>
                                    <div>
                                        {/* <div style={{ textAlign: 'right', width: '80%' }}> */}
                                        <div>
                                            <Select
                                                defaultValue='当日'
                                                onChange={handleChangeMarket}
                                                options={[
                                                    { value: '1', label: '当日' },
                                                    { value: '2', label: '当月' },
                                                    { value: '3', label: '当年' }
                                                    // { value: '总批次', label: '总批次' },
                                                    // { value: '当月新增', label: '当月新增' }
                                                ]}
                                            />
                                        </div>
                                        <ReactEcharts
                                            option={initMarket()}
                                            opts={{ renderer: 'svg' }}
                                            style={{ height: '380px' }}
                                            theme='clear'
                                        />
                                    </div>
                                </div>
                            </div>
                        </BaseCard>
                    </div>
                ) : (
                    <div className={styles.Column}>
                        {getHasPermissWarehouseData && (
                            <BaseCard
                                className={styles.overViewContainer}
                                style={{ marginTop: '30px' }}
                                title={<PageTitle title='仓储概览' bg='container title' />}
                            >
                                <div className={styles.Details} onClick={() => navigate('/warehouse/list')}>
                                    查看详情&gt;
                                </div>
                                <img src={require('../../assets/landicon/mai.png')} className={styles.mai} alt='' />
                                <div className={styles.overViewContainerTop}>
                                    <div className={styles.overViewContainerTopOverView}>
                                        <div className={styles.weather}>
                                            <div className={styles.columnTitle}>仓储数量曲线图</div>
                                            {/* <div>
                                  最近更新时间:
                                  {dayjs(LandWeatherData?.lastUpdateTime).format('YYYY-MM-DD HH:mm:ss')}
                              </div> */}
                                        </div>
                                        <div>
                                            <ReactEcharts
                                                option={getOptionWar()}
                                                opts={{ renderer: 'svg' }}
                                                style={{ height: '440px' }}
                                                theme='clear'
                                            />
                                        </div>
                                    </div>

                                    <div className={styles.overViewContainerTopRightGoods}>
                                        <div>
                                            {
                                                // [traceabilityConfig, productConfig, sourceConfig, qcqaConfig, transformConfig]
                                                [warehouseConfig]?.map((config: any, index: number) => (
                                                    <CardTips key={index} {...config} />
                                                ))
                                            }
                                        </div>
                                    </div>
                                </div>
                            </BaseCard>
                        )}

                        {/* 销售 */}

                        <BaseCard
                            className={styles.overViewContainer}
                            style={{ marginTop: '30px' }}
                            title={<PageTitle title='销售概览' bg='container title' />}
                        >
                            <div className={styles.Details} onClick={() => navigate('/market/list')}>
                                查看详情&gt;
                            </div>
                            <img src={require('../../assets/landicon/mai.png')} className={styles.mai} alt='' />
                            <div className={styles.MakerTop}>
                                <div className={styles.MakerTopLeft}>
                                    <div>
                                        <div className={styles.columnTitleMaker}>销售总量</div>
                                        <div className={styles.chainNumMaker}>
                                            <div className={styles.chainNumSubscript}>
                                                {getOverviewInfoData?.totalSalesVolume || '0'}
                                                {/* <span style={{ fontSize: '14px' }}>种</span> */}
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div className={styles.columnTitleMaker}>当月销售量</div>
                                        <div className={styles.chainNumMaker}>
                                            <div className={styles.chainNumSubscript}>
                                                {getOverviewInfoData?.monthSalesVolume || '0'}
                                                {/* <span style={{ fontSize: '14px' }}>种</span> */}
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div className={styles.columnTitleMaker}>当日销售量</div>
                                        <div className={styles.chainNumMaker}>
                                            <div className={styles.chainNumSubscript}>
                                                {getOverviewInfoData?.daySalesVolume || '0'}
                                                {/* <span style={{ fontSize: '14px' }}>种</span> */}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className={styles.MakerTopRight}>
                                    <div>
                                        <div className={styles.columnTitleMaker}>销售总额</div>
                                        <div className={styles.chainNumMaker}>
                                            <div className={styles.chainNumSubscript}>
                                                {getOverviewInfoData?.totalSalesAmount || '0'}
                                                {/* <span style={{ fontSize: '14px' }}>种</span> */}
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div className={styles.columnTitleMaker}>当月销售额</div>
                                        <div className={styles.chainNumMaker}>
                                            <div className={styles.chainNumSubscript}>
                                                {getOverviewInfoData?.monthSalesRevenue || '0'}
                                                {/* <span style={{ fontSize: '14px' }}>种</span> */}
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <div className={styles.columnTitleMaker}>当日销售额</div>
                                        <div className={styles.chainNumMaker}>
                                            <div className={styles.chainNumSubscript}>
                                                {getOverviewInfoData?.daySalesRevenue || '0'}
                                                {/* <span style={{ fontSize: '14px' }}>种</span> */}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className={styles.overViewContainerTop}>
                                <div className={styles.overViewContainerTopGoodsMarket}>
                                    <div className={styles.weather}>
                                        <div className={styles.columnTitle}>销售量/额折线图</div>
                                    </div>
                                    <div>
                                        <div style={{ textAlign: 'right' }}>
                                            <Select
                                                defaultValue='销售量'
                                                onChange={handleChangeSale}
                                                options={[
                                                    { value: '销售量', label: '销售量' },
                                                    { value: '销售额', label: '销售额' }
                                                ]}
                                            />
                                        </div>
                                        <ReactEcharts
                                            option={getOptionSales()}
                                            opts={{ renderer: 'svg' }}
                                            style={{ height: '380px' }}
                                            theme='clear'
                                        />
                                    </div>
                                </div>

                                <div className={styles.overViewContainerTopRightGoodsPieMarket}>
                                    <div className={styles.weather}>
                                        <div className={styles.columnTitle}>销售区地域分布-环形图</div>
                                    </div>
                                    <div>
                                        <div>
                                            <Select
                                                defaultValue='当日'
                                                onChange={handleChangeMarket}
                                                options={[
                                                    { value: '1', label: '当日' },
                                                    { value: '2', label: '当月' },
                                                    { value: '3', label: '当年' }
                                                    // { value: '总批次', label: '总批次' },
                                                    // { value: '当月新增', label: '当月新增' }
                                                ]}
                                            />
                                        </div>
                                        <ReactEcharts
                                            option={initMarket()}
                                            opts={{ renderer: 'svg' }}
                                            style={{ height: '380px' }}
                                            theme='clear'
                                        />
                                    </div>
                                </div>
                            </div>
                        </BaseCard>
                    </div>
                )}
            </div>
        </>
    );
};

export default WithPaginate(TraceabilityOverview);

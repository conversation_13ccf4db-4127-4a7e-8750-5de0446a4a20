# 仓储管理作废按钮实现

## 功能需求
在仓储管理页面的操作列中，根据用户角色显示作废按钮：
- **显示角色**: 平台方、运营方、销售企业
- **隐藏角色**: 生产加工企业及其他角色

## 实现的修改

### 1. 更新的文件
- `src/pages/warehouse-list/warehouse-list.tsx` ✅
- `src/pages/wars-list/wars-list.tsx` ✅

### 2. 添加的功能

#### 导入作废服务
```typescript
import { warehouseList, cancelWare } from '@services/ware';
```

#### 添加作废mutation
```typescript
//作废
const matermodiy = useMutation(cancelWare, {
    onSuccess(res) {
        message.success('作废成功');
        foodquery.refetch();
    },
    onError(err: any) {
        ReformChainError(err);
        foodquery.refetch();
    }
});
```

#### 操作列中的作废按钮
```typescript
{/* 作废按钮 - 仅平台方、运营方、销售企业显示 */}
{[RoleEnum.平台方, RoleEnum.运营方, RoleEnum.销售企业]?.includes(LocalLoginIdentity) && (
    <BaseButton
        type='dashed'
        className='primaryBtn'
        onClick={() => {
            matermodiy.mutate({
                id: record?.id
            });
        }}
    >
        作废
    </BaseButton>
)}
```

### 3. 角色权限控制

#### 显示作废按钮的角色
- **平台方** (RoleEnum.平台方 = 0)
- **运营方** (RoleEnum.运营方 = 6)  
- **销售企业** (RoleEnum.销售企业 = 7)

#### 不显示作废按钮的角色
- **生产加工企业** (RoleEnum.生产加工企业 = 1)
- **供应商** (RoleEnum.供应商 = 2)
- **质检机构** (RoleEnum.质检机构 = 3)
- **监管机构** (RoleEnum.监管机构 = 4)
- **物流企业** (RoleEnum.物流企业 = 5)

### 4. 功能特点
- ✅ 点击作废按钮后调用 `cancelWare` 服务
- ✅ 作废成功后显示成功提示
- ✅ 自动刷新列表数据
- ✅ 错误处理机制
- ✅ 按钮仅对有权限的角色显示

### 5. 操作列布局
```
[作废按钮] [查看详情按钮]
```
- 作废按钮：仅特定角色显示
- 查看详情按钮：所有角色都显示

## 测试要点
1. 使用平台方账号登录，验证作废按钮是否显示
2. 使用运营方账号登录，验证作废按钮是否显示  
3. 使用销售企业账号登录，验证作废按钮是否显示
4. 使用生产加工企业账号登录，验证作废按钮是否隐藏
5. 点击作废按钮，验证功能是否正常工作
6. 验证作废成功后的提示和数据刷新

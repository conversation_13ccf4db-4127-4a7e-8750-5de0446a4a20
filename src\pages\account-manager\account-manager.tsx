/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-11-01 17:31:05
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Button, Form, Input, message, Modal, Space } from 'antd';
import useUrlState from '@ahooksjs/use-url-state';
import { UserAdd, userPage, resetPassWord } from '@services/user';
import { useMutation, useQuery } from 'react-query';
import styles from './index.module.less';
import { addAccountConfig, editAccountConfig, searchConfig } from './config';
import SearchForm from '@components/search-form';
import { useRef, useState } from 'react';
import BaseModal from '@components/base-modal';
import FilterForm from '@components/filter-form';
import BaseButton from '@components/base-button';
import PageTitle from '@components/page-title';
import TableHead from '@components/table-head';
import { SearchOutlined, PlusOutlined, SyncOutlined } from '@ant-design/icons';
import { useAccountList } from '../../myhooks/useaccountmanage';
import copyToClipboard from 'copy-to-clipboard';
import { userModiy, uptdateUser } from '@services/user';
import WithPaginate from '../../hoc/withpaginate';
import { ReformChainError } from '@utils/errorCodeReform';
import { decryptStr, isArrayArr, randomPassword } from '@utils';
import dayjs from 'dayjs';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { CheckCircleFilled } from '@ant-design/icons';
import { ColumnsType } from 'antd/lib/table';
import BaseTooptip from '@components/base-tooltip';
import rsaEncrypt from '@utils/rsa';

interface IUrlState {
    pageIndex: number;
    pageSize: number;
    pageSearchFlag?: number;
}

const { confirm } = Modal;
const AccountManage = (props: any) => {
    const [addAccountVisible, setAddAccountModelVisible] = useState(false);
    const [editAccountVisible, seteditAccountVisible] = useState(false);
    const [resetPassword, setresetPassword] = useState('');
    const [createPassword, setCreatePassword] = useState('');
    const [resetPswVisible, setresetPswVisible] = useState(false);
    const [editUserId, setEditUserId] = useState();
    // const [addAccountSuccessVisible, setAddAccountSuccessVisible] = useState(false);

    const [tableData, setTableData] = useState<any>();
    const [nameArr, setNameArr] = useState<any>();
    const [phoneNumberArr, setPhoneNumberArr] = useState<any>();

    const [addAccountForm] = Form.useForm();
    //编辑form
    const [editCormForm] = Form.useForm();
    const [resetFrom] = Form.useForm();
    const pswRef: any = useRef();
    // const [resetPassword,setresetPassword] = useState()
    // const pswValue = Form.useWatch('password', resetFrom);
    const addSuccessInfo: any = useRef();
    const [search]: any = Form.useForm();
    const queryuser: any = useRef(null);
    const { pageInfo, handlePaginationChange } = props;
    const [successModalVisible, setsuccessModalVisible] = useState(false);
    const [creatEmployeesForm] = Form.useForm();

    const userAdd = useMutation(UserAdd, {
        onSuccess(res) {
            setAddAccountModelVisible(false);
            message.success('添加用户成功');
            setsuccessModalVisible(true);
            userquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            userquery.refetch();
        }
    });
    const usermodiy = useMutation(userModiy, {
        onSuccess(res) {
            message.success('修改状态成功');
            userquery.refetch();
        },
        onError(err) {
            message.error('修改状态失败');
            userquery.refetch();
        }
    });
    const updateuser = useMutation(uptdateUser, {
        onSuccess(res) {
            message.success('编辑用户成功');
            userquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            userquery.refetch();
        }
    });
    //重置密码接口
    const resetpassword = useMutation(resetPassWord, {
        onSuccess(res) {
            message.success('重置密码成功');
            userquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            userquery.refetch();
        }
    });
    const userquery = useQuery(
        ['userquery', pageInfo],
        () => {
            // if (!userInfo.user?.organization_id) {
            //     message.error('未获取到机构id');
            //     return Promise.reject('未获取到机构id');
            // }
            return userPage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                param: queryuser?.current?.name?.trim() || undefined,
                state: queryuser?.current?.data ? Number(queryuser?.current?.data) : undefined
            });
        },
        {
            async onSuccess(res) {
                //列表数据
                console.log('res?.data----解密', res?.data);
                const phoneNumberArr: any = await Promise.all(
                    isArrayArr(res?.data?.records)?.map((item: any, index: number) => {
                        return decryptStr(item.phoneNumber);
                    })
                );
                const nameArr = await Promise.all(
                    isArrayArr(res?.data?.records)?.map((item: any, index: number) => {
                        return decryptStr(item.userName);
                    })
                );
                const tableData = await Promise.all(
                    isArrayArr(res?.data?.records)?.map((item: any, index: number) => {
                        const value = {
                            account: item.userId,
                            phoneNumber: index,
                            time: dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss'),
                            status: item.state,
                            operation: index
                        };
                        return value;
                    })
                );
                console.log('tableData----', tableData);
                setTableData(tableData);
                console.log('nameArr', nameArr, phoneNumberArr);
                setNameArr(nameArr);
                setPhoneNumberArr(phoneNumberArr);
            },
            onError(err: any) {
                ReformChainError(err);
                message.error(err.data.message);
            }
        }
    );
    //列表数据
    // const tableData = userquery?.data?.data?.records?.map((item: any) => ({
    //     account: item.userId,
    //     phoneNumber: item.phoneNumber,
    //     time: dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss'),
    //     status: item.state,
    //     operation: item.userName
    // }));
    const listColumn: ColumnsType<any> = [
        {
            title: '账号',
            dataIndex: 'account',
            key: 'account',
            ellipsis: true
        },
        {
            title: '用户名',
            dataIndex: 'operation',
            key: 'operation',
            ellipsis: true,
            render: (data: any) => <span>{nameArr[data]}</span>
        },
        {
            title: '联系方式',
            dataIndex: 'phoneNumber',
            key: 'phoneNumber',
            ellipsis: true,
            render: (data: any) => <span>{phoneNumberArr[data]}</span>
        },
        {
            title: '创建时间',
            dataIndex: 'time',
            key: 'time',
            ellipsis: true
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            ellipsis: true,
            render: (data: any) => (
                <span style={{ color: data ? '#F64041' : '#666666' }}>
                    <Badge
                        status={data ? 'error' : 'success'}
                        color={data ? '#F64041' : 'rgb(36, 171, 59)'}
                        text={data ? '禁用' : '可用'}
                    />
                </span>
            )
        },
        {
            width: 280,
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            render: (data: any, record: any) => (
                <Space size='middle'>
                    <BaseButton
                        type='dashed'
                        disabled={record?.account === 1}
                        className={record.status ? 'primaryBtn' : 'warnBtn'}
                        onClick={
                            record.status
                                ? () => {
                                      usermodiy.mutate({
                                          userId: record?.account
                                      });
                                  }
                                : () => {
                                      showConfirm(record?.account);
                                  }
                        }
                    >
                        {record?.status ? '启用' : '禁用'}
                    </BaseButton>
                    <BaseButton
                        type='text'
                        className='editBtn'
                        // className={data.status ? 'warnBtn' : 'primaryBtn'}
                        onClick={() => {
                            editCormForm.setFieldsValue({
                                name: nameArr[record?.operation],
                                phone: phoneNumberArr[record?.phoneNumber],
                                userId: record?.account
                            });
                            setEditUserId(record?.account);
                            seteditAccountVisible(true);
                        }}
                    >
                        编辑
                    </BaseButton>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        // className={data.status ? 'warnBtn' : 'primaryBtn'}
                        onClick={() => {
                            pswRef.current = record?.account;
                            setresetPswVisible(true);
                        }}
                    >
                        重置密码
                    </BaseButton>
                </Space>
            )
        }
    ];

    //禁用
    const showConfirm = (id: any) => {
        confirm({
            title: '确定要禁用该用户吗？',
            okText: '停用',
            cancelText: '取消',
            icon: <ExclamationCircleFilled rev={undefined} />,
            content: `禁用后该用户无法使用${sessionStorage.systemTitle}`,
            onOk() {
                usermodiy.mutate({
                    state: 1,
                    userId: id
                });
            },
            onCancel() {}
        });
    };
    // const listData = queryList.data;

    const addAccountModalConfig = {
        okText: '确定',
        title: '新增用户账号',
        visible: addAccountVisible,
        setVisible: setAddAccountModelVisible,
        okHandle: async () => {
            try {
                const values = await addAccountForm.validateFields();
                const randomPd = randomPassword(8);
                setCreatePassword(randomPd);
                userAdd.mutate({
                    userName: values?.name,
                    phoneNumber: await rsaEncrypt(values?.phone),
                    password: await rsaEncrypt(randomPd)
                });
            } catch {}
        },
        onCancelHandle: () => {
            addAccountForm.resetFields();
            setAddAccountModelVisible(false);
        }
    };
    //创建数据
    const successConfigs = [
        {
            label: '用户名',
            value: 'name',
            placeholder: '请输入管理员名称',
            className: 'manaName',
            display: addAccountForm.getFieldValue('name')
        },
        {
            label: '联系方式',
            value: 'phone',
            placeholder: '请输入',
            className: 'phone',
            display: addAccountForm.getFieldValue('phone')
        },
        {
            label: '密码',
            value: 'password',
            placeholder: '请输入',
            className: 'phone',
            display: '. . . . . . . . . .'
        }
    ];
    const successConfig = {
        okText: '复制密码',
        title: '用户创建成功，请妥善保存账号和密码！',
        // hidden: true,
        visible: successModalVisible,
        setVisible: setsuccessModalVisible,
        okHandle: async () => {
            const values = await addAccountForm.getFieldsValue();
            setsuccessModalVisible(false);
            const username: any = {
                // account: userAdd?.data?.data?.account,
                password: createPassword
            };
            const copyRet = copyToClipboard(`${username.password}`);
            copyRet ? message.success('复制成功') : message.error('复制失败');
            addAccountForm.resetFields();
        },
        onCancelHandle: () => {
            setsuccessModalVisible(false);
            addAccountForm.resetFields();
        }
    };
    const editAccountSuccessConfig = {
        okText: '确定',
        title: '编辑用户账号',
        visible: editAccountVisible,
        setVisible: seteditAccountVisible,
        okHandle: async () => {
            try {
                const values = await editCormForm.validateFields();
                updateuser.mutate({
                    userId: editUserId,
                    userName: values?.name,
                    phoneNumber: await rsaEncrypt(values?.phone)
                });
                seteditAccountVisible(false);
            } catch (e) {}
        },
        onCancelHandle: () => {
            // setresetPass('')
            seteditAccountVisible(false);
        }
    };

    const resetPassWDConfig = {
        title: '重置密码',
        visible: resetPswVisible,
        setVisible: setresetPswVisible,
        okHandle: async () => {
            if (!resetPassword) {
                message.error('请生成登录密码!');
                return;
            }
            resetpassword.mutate({
                userId: pswRef.current,
                password: await rsaEncrypt(resetPassword)
            });
            setresetPassword('');
            setresetPswVisible(false);
            resetFrom.resetFields();
        },
        onCancelHandle: () => {
            setresetPassword('');
            setresetPswVisible(false);
            resetFrom.resetFields();
        }
    };

    // const resetPassword = {
    //     title: '重置密码'
    // }

    //查询
    const onFinish = (values: any) => {
        handlePaginationChange(1);
        queryuser.current = values;
        userquery.refetch();
    };
    return (
        <div>
            <BaseCard className='accountContainer' title={<PageTitle title='账号列表' bg='container yuan' />}>
                <BaseTable
                    rowKey='account'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return (
                            <TableHead
                                LeftDom={
                                    <div
                                        // className="searchContainer"
                                        className={styles.searchContainer}
                                    >
                                        <Form
                                            onFinish={onFinish}
                                            layout='inline'
                                            labelAlign='left'
                                            form={search}
                                            className='label-title'
                                            style={{ marginBottom: '21px' }}
                                        >
                                            <FilterForm itemConfig={searchConfig} size={230} labelCol={4} />
                                            <BaseButton
                                                type='primary'
                                                htmlType='submit'
                                                style={{ width: 100 }}
                                                // className='searchBtn'
                                                className={`${styles.searchBtn} ${styles.baseBtn}`}
                                                icon={<SearchOutlined rev={undefined} />}
                                            >
                                                查询
                                            </BaseButton>
                                        </Form>
                                        {/* <BaseButton
                            type="primary"
                            className={styles.baseBtn}
                            style={{ width: 120}}
                            onClick={() => {
                                queryuser.current = null
                                userquery.refetch()
                                search.resetFields()
                            }}>
                            重置
                        </BaseButton> */}
                                        <BaseButton
                                            type='dashed'
                                            className='primaryBtn'
                                            icon={<SyncOutlined rev={undefined} />}
                                            style={{ width: 100 }}
                                            onClick={() => {
                                                queryuser.current = null;
                                                userquery.refetch();
                                                search.resetFields();
                                            }}
                                        >
                                            重置
                                        </BaseButton>
                                    </div>
                                }
                                RightDom={
                                    <BaseButton
                                        type='dashed'
                                        icon={<PlusOutlined rev={undefined} />}
                                        className='bgBtn'
                                        onClick={() => {
                                            setAddAccountModelVisible(true);
                                        }}
                                    >
                                        添加账号
                                    </BaseButton>
                                }
                            />
                        );
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={userquery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={userquery.data?.data.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>

            <BaseModal {...addAccountModalConfig}>
                <Form name='addAccountForm' form={addAccountForm} className='edit-label-title account-manager-form'>
                    {<FilterForm itemConfig={addAccountConfig} />}
                </Form>
            </BaseModal>

            <BaseModal {...editAccountSuccessConfig}>
                <Form name='editCormForm' form={editCormForm} className='edit-label-title'>
                    {
                        // <FilterForm itemConfig={addSuccessInfo.current ? addSuccessInfo.current : []} />
                        <FilterForm itemConfig={editAccountConfig} />
                    }
                </Form>
            </BaseModal>
            {/* 重置密码 */}
            <BaseModal
                centered
                {...resetPassWDConfig}
                width={515}
                style={{ height: 411 }}
                cancelText='取消'
                okText='确认'
            >
                <Form
                    name='resetpassword'
                    labelCol={{
                        span: 5
                    }}
                    wrapperCol={{
                        span: 14
                    }}
                    autoComplete='off'
                    form={resetFrom}
                >
                    <Form.Item
                        label='重置密码'
                        name='password'
                        wrapperCol={{ span: 20 }}
                        initialValue={''}
                        rules={[
                            {
                                required: true,
                                message: '请生成登录密码!'
                            }
                        ]}
                    >
                        <div className={styles.resetContainer}>
                            <Input id='pswRef' value={resetPassword} readOnly className={styles.pswInput} />
                            <span
                                className={`${styles.btn} ${styles.newPswBtn} ${styles.active}`}
                                onClick={() => {
                                    setresetPassword(randomPassword(8));
                                }}
                            >
                                生成
                            </span>
                            <span
                                id='copyBtn'
                                className={
                                    resetPassword ? `${styles.btn} ${styles.active}` : `${styles.btn} ${styles.disable}`
                                }
                                data-clipboard-target='#pswRef'
                                onClick={() => {
                                    if (resetPassword) {
                                        const copyRet = copyToClipboard(resetPassword);
                                        copyRet ? message.success('复制成功') : message.error('复制失败');
                                    }
                                }}
                            >
                                复制
                            </span>
                        </div>
                    </Form.Item>
                </Form>
            </BaseModal>
            <BaseModal
                {...successConfig}
                className='employees_header'
                // closeIcon={<CheckCircleFilled className={styles.employess_icon} rev={undefined} />}
            >
                <Form
                    name='creatEmployeesForm'
                    form={addAccountForm}
                    className='employess-label-title'
                    labelAlign='left'
                >
                    {<FilterForm showMode itemConfig={successConfigs} />}
                </Form>
            </BaseModal>
        </div>
    );
};

export default WithPaginate(AccountManage);

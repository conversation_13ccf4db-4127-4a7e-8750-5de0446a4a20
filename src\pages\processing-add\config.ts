/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 18:07:30
 * @LastEditTime: 2022-11-01 18:15:21
 * @LastEditors: PhilRandWu
 */
export const productInfoConfigs = [
    {
        type: 'Input',
        label: '生产线',
        value: 'productionLine',
        placeholder: '请输入',
        span: 12,
        className: 'count'
    },
    {
        type: 'Input',
        label: '种植户',
        value: 'productionShift',
        placeholder: '请输入',
        span: 12,
        className: 'count'
    },
    {
        type: 'Input',
        label: '生产地点',
        value: 'productionPlace',
        placeholder: '请输入',
        span: 12,
        className: 'count'
    },
    {
        type: 'Input',
        label: '生产环境信息',
        value: 'environmentInfo',
        placeholder: '请输入',
        span: 12,
        className: 'count'
    },
    {
        type: 'Input',
        label: '责任人员',
        value: 'personLiable',
        placeholder: '请输入',
        span: 12,
        className: 'count'
    },
    {
        type: 'Input',
        label: '联系电话',
        value: 'contactNumber',
        placeholder: '请输入',
        span: 12,
        className: 'count'
    },
    {
        type: 'DatePicker',
        label: '抽检时间',
        value: 'checkTime',
        placeholder: '请输入',
        span: 12,
        wide: 280
    },
    {
        type: 'Input',
        label: '抽检记录',
        value: 'checkRecord',
        placeholder: '请输入',
        span: 12,
        className: 'count'
    },
    {
        type: 'DatePicker',
        label: '留样时间',
        value: 'sampleTime',
        placeholder: '请输入',
        span: 12,
        wide: 280
    },
    {
        type: 'Input',
        label: '留样记录',
        value: 'sampleRecord',
        placeholder: '请输入',
        span: 12,
        className: 'count'
    },
    {
        type: 'UploadFile',
        label: '附件',
        value: 'productionAccessory',
        placeholder: '最大可上传20M,不限制类型',
        span: 12,
    }
];

/*
 * @Description: 
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-07-21 09:51:12
 * @LastEditTime: 2022-08-13 17:08:42
 * @LastEditors: PhilRandWu
 */
import { createSlice } from "@reduxjs/toolkit";

interface IUserState {
    showPasswordInput?: boolean
}

const initialState: IUserState = {
    showPasswordInput: false
};

const app: any = createSlice({
    name: "app",
    initialState,
    reducers: {
        showPasswordInput: (store: IUserState,action) => {
            console.log("showPasswordInput",action)
            store.showPasswordInput = action.payload.showPasswordInput;
        },
    },
});

export const { showPasswordInput} = app.actions;
export default app.reducer;

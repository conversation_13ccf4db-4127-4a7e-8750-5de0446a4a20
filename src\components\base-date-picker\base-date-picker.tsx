import React from 'react';
import { DatePicker } from 'antd';
import type { DatePickerProps } from 'antd';

import './override.less';
const { RangePicker } = DatePicker;

const BaseInput = (props: any) => {
    return <RangePicker getPopupContainer={triggerNode => triggerNode.parentNode} {...props} />;
    // <RangePicker className='base-date-picker' {...props} />;
};

export default React.memo(BaseInput);

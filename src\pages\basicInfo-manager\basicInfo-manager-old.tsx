/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-11-02 14:35:27
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import styles from './index.module.less';
import PageTitle from '@components/page-title';
import { Form, Image, message, Space } from 'antd';
import { businessConfig, chainConfig, enterpriseConfig, headConfig } from './config';
import BaseButton from '@components/base-button';
import BaseFormItem from '@components/base-form-item';
import { selectParticipantDetail } from '@services/company';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQuery } from 'react-query';
import { ReformChainError } from '@utils/errorCodeReform';
import { useSelector, useDispatch } from 'react-redux';
import { useAppDispatch, useAppSelector } from '@store';
import FilterForm from '@components/filter-form';
import dayjs from 'dayjs';
import { getLocalPrivatekey } from '@utils/blockChainUtils';
import ImgCropUpload from '@components/img-upload';
import { LocalLoginIdentity, RoleEnum } from '@config';
import ChainDetailModal from '@components/chain_detail_modal';
import { useState } from 'react';
import { decryptedUrl, decryptedUrlfn, isArrayArr } from '@utils';

const CoreFlrm = () => {
    const navigate = useNavigate();
    const dispatch = useDispatch();
    const userInfo = useAppSelector((store) => store.user);
    const errImage =
        'data:image/png;base64,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';
    const [editEnterpriseForm] = Form.useForm();
    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    const [promotionPicData, setPromotionPicData] = useState<any>();
    const [promotionVideo, setPromotionVideo] = useState<any>();
    const [qualification, setQualification] = useState<any>();

    const selectParticipant = useQuery(
        [' selectParticipant111'],
        () => {
            return selectParticipantDetail({
                orgId: userInfo.userInfo.orgId
            });
        },
        {
            async onSuccess(res) {
                const qualification = await decryptedUrl(res?.data?.qualification);
                setQualification(qualification);
                const video = await decryptedUrl(res?.data?.video ? res?.data?.video : null);
                setPromotionVideo(video);
                // const picture = await decryptedUrl(res?.data?.picture)
                // const newPicture = Promise.all(res?.data?.picture.map((item: any, index: number) => {
                //     return {
                //         uid: index + 1,
                //         name: 'image.png',
                //         status: 'done',
                //         url: decryptedUrlfn(item)
                //     };
                // }))
                const newPicture = await Promise.all(
                    isArrayArr(res?.data?.picture).map((item: string) => decryptedUrl(item))
                );
                const arrayData = await Promise.all(
                    isArrayArr(res?.data?.picture)?.map((item: any) => {
                        decryptedUrl(item);
                    })
                );
                setPromotionPicData(arrayData);
                editEnterpriseForm.setFieldsValue({
                    companyName: res?.data?.companyName,
                    creditCode: res?.data?.creditCode,
                    address: res?.data?.address,
                    principal: res?.data?.principal,
                    email: res?.data?.email,
                    telephone: res?.data?.telephone,
                    introduce: res?.data?.introduce,
                    transactionId: res?.data?.transactionId,
                    transactionTime: res?.data?.transactionTime
                        ? dayjs(res?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
                        : '-',
                    // credentials: [
                    //     {
                    //         uid: 1,
                    //         name: 'image.png',
                    //         status: 'done',
                    //         url: qualification
                    //     }
                    // ],
                    credentials: qualification,
                    // promotionPic: res?.data?.picture?.map((item: any, index: number) => {
                    //     return {
                    //         uid: index + 1,
                    //         name: 'image.png',
                    //         status: 'done',
                    //         url: decryptedUrlfn(res?.data?.item)
                    //     };
                    // }),
                    promotionPic: newPicture,
                    promotionVideo: video
                });
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    console.log(selectParticipant);
    const enterpriseConfig = [
        {
            label: '企业名称',
            name: 'companyName',
            value: 'companyName',
            type: 'ShowText'
        },
        {
            label: '统一社会信用代码',
            name: 'creditCode',
            value: 'creditCode',
            type: 'ShowText'
        },
        {
            label: '地址',
            name: 'address',
            value: 'address',
            type: 'ShowText'
        },
        {
            label: '企业资质',
            name: 'credentials',
            value: 'credentials',
            type: 'Custom',
            children: (
                <Form.Item key='credentials' name='credentials'>
                    {selectParticipant?.data?.data?.qualification ? (
                        <Image
                            width={70}
                            src={editEnterpriseForm.getFieldValue('credentials')}
                            fallback={errImage}
                        ></Image>
                    ) : (
                        '-'
                    )}
                </Form.Item>
            )
        }
    ];
    const headConfig = [
        {
            label: '姓名',
            name: 'principal',
            value: 'principal',
            type: 'ShowText'
        },
        {
            label: '邮箱',
            name: 'email',
            value: 'email',
            type: 'ShowText'
        },
        {
            label: '手机号',
            name: 'telephone',
            value: 'telephone',
            type: 'ShowText'
        }
    ];

    const ChainDetailModalConfig = {
        transactionId: editEnterpriseForm.getFieldValue('transactionId'),
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };

    const businessConfig = [
        {
            label: '企业介绍',
            name: 'introduce',
            value: 'introduce',
            type: 'ShowScrollText'
        },
        {
            label: '宣传图片',
            name: 'promotionPic',
            value: 'promotionPic',
            type: 'Custom',
            children: (
                <Form.Item key='promotionPic' name='promotionPic'>
                    {selectParticipant?.data?.data?.picture ? (
                        <Image.PreviewGroup>
                            <Space>
                                {promotionPicData?.map((item: string) => {
                                    return <Image width={150} height={80} fallback={errImage} src={item} />;
                                })}
                            </Space>
                        </Image.PreviewGroup>
                    ) : (
                        '-'
                    )}
                </Form.Item>
            )
        },
        {
            label: '宣传视频',
            name: 'promotionVideo',
            value: 'promotionVideo',
            type: 'Custom',
            children: selectParticipant?.data?.data?.video ? (
                <video src={promotionVideo} width={200} controls></video>
            ) : (
                <div>-</div>
            )
        }
    ];
    const chainConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Link',
            onClick: () => {
                setChainDetailModalVisible(true);
            }
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];

    const onFinish = (values: any) => {
        console.log('Success:', values);
    };

    const onFinishFailed = (errorInfo: any) => {
        console.log('Failed:', errorInfo);
    };

    return (
        <>
            <BaseCard title={<PageTitle title='企业信息详情' />}>
                <Form
                    name='basic'
                    form={editEnterpriseForm}
                    onFinish={onFinish}
                    onFinishFailed={onFinishFailed}
                    autoComplete='off'
                    labelAlign='left'
                >
                    <PageTitle title='企业基础信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={enterpriseConfig} labelCol={false} />

                    <PageTitle title='负责人信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={headConfig} labelCol={false} />

                    {[RoleEnum.生产加工企业]?.includes(RoleEnum[userInfo.role]) && (
                        <>
                            <PageTitle title='企业简介' type='primaryIcon' bmagin={16} />
                            <FilterForm showMode itemConfig={businessConfig} labelCol={false} />
                        </>
                    )}

                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={chainConfig} labelCol={false} />

                    <BaseButton
                        type='primary'
                        htmlType='submit'
                        style={{ width: 120 }}
                        onClick={() => {
                            navigate('basicInfoEdit');
                        }}
                    >
                        编辑
                    </BaseButton>
                </Form>
            </BaseCard>
            <ChainDetailModal {...ChainDetailModalConfig} />
        </>
    );
};

export default CoreFlrm;

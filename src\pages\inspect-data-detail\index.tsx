import React, { useEffect, useState } from 'react';
import { Col, Image, Form, message, Row, Space, Descriptions } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';

import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';
import BaseInput from '@components/base-input/base-input';
import BaseSelect from '@components/base-select/base-select';
import BaseDatePicker from '@components/base-date-picker';

import styles from './index.module.less';
import FilterForm from '@components/filter-form';
import { useForm } from 'antd/lib/form/Form';
import SourceInspectService, { SourceInspectResultsEnum } from '@services/traceability_data/source_inspect';
import ChainDetailModal from '@components/chain_detail_modal';
import BaseDescriptions from '@components/base-descriptions';
import { decryptedUrl } from '@utils';

const IconStyle: React.CSSProperties = {
    display: 'inline-block',
    width: 16,
    height: 16,
    fontSize: 12,
    background: '#cecece',
    color: '#fff',
    borderRadius: 100,
    textAlign: 'center',
    margin: '0 6px 0 6px'
};

const FleeWarning = () => {
    const { id } = useParams();
    const navigate = useNavigate();
    const [ChainForm] = useForm();
    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    const [inspectionReport, setInspectionReport] = useState<any>();
    const [inspectionAccessory, setInspectionAccessory] = useState<any>();

    const InspectDetail = useQuery([], () => SourceInspectService.detail(Number(id)), {
        onSuccess: async (res) => {
            const inspectionReport = await decryptedUrl(res?.data?.inspectionReport);
            setInspectionReport(inspectionReport);
            const inspectionAccessory = await decryptedUrl(res?.data?.inspectionAccessory);
            setInspectionAccessory(inspectionAccessory);
        }
    });
    const InspectDetailData = InspectDetail?.data?.data;

    const chainConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Link',
            onClick: () => {
                setChainDetailModalVisible(true);
            }
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];

    useEffect(() => {
        ChainForm.setFieldsValue({
            transactionId: InspectDetailData?.transactionId || '-',
            transactionTime: dayjs(InspectDetailData?.transactionTime).format('YYYY-MM-DD HH:mm:ss') || '-'
        });
    }, [InspectDetailData]);

    const ChainDetailModalConfig = {
        transactionId: InspectDetailData?.transactionId,
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };

    return (
        <>
            <BaseCard
                className={styles.coreFIrmContainer}
                title={<PageTitle title='质检溯源数据详情' bg='container zhi' />}
            >
                <PageTitle title='质检信息' type='primaryIcon' bmagin={16} />
                <BaseDescriptions>
                    <Descriptions.Item label='产品名称'>{InspectDetailData?.productName || '-'}</Descriptions.Item>
                    <Descriptions.Item label='生产批次'>{InspectDetailData?.productionBatch || '-'}</Descriptions.Item>
                    <Descriptions.Item label='质检内容'>
                        {InspectDetailData?.inspectionContent || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label='质检机构'>
                        {InspectDetailData?.inspectionOrgName || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label='质检结果'>
                        <span
                            style={
                                InspectDetailData?.inspectionResults === SourceInspectResultsEnum.合格
                                    ? { color: '#29bd9c' }
                                    : { color: 'red' }
                            }
                        >
                            {InspectDetailData?.inspectionResults === SourceInspectResultsEnum.合格 ? '合格' : '不合格'}
                        </span>
                    </Descriptions.Item>
                    <Descriptions.Item label='质检报告' className='custom-descriptions-item'>
                        {InspectDetailData?.inspectionReport && inspectionReport ? (
                            <Image width={64} src={inspectionReport}></Image>
                        ) : (
                            '-'
                        )}
                    </Descriptions.Item>
                    <Descriptions.Item label='附件'>
                        {InspectDetailData?.inspectionAccessory && inspectionAccessory ? (
                            <a href={inspectionAccessory}>下载</a>
                        ) : (
                            '-'
                        )}
                    </Descriptions.Item>
                </BaseDescriptions>
                <Form form={ChainForm}>
                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={chainConfig} labelCol={false} />
                </Form>
            </BaseCard>
            <ChainDetailModal {...ChainDetailModalConfig} />
        </>
    );
};

export default FleeWarning;

import request from '../request';
//质检分页
export const qualityPage = (obj: any) => {
    return request({
        url: '/quality/page',
        method: 'post',
        data: obj
    });
};
//详情
export const qualityTestDetail = (obj: any) => {
    return request({
        url: `/quality/getDetail`,
        method: 'get',
        params: obj
    });
};
//新增
export const addQuality = (obj: any) => {
    return request({
        url: '/quality/add',
        method: 'post',
        data: obj
    });
};
//产品列表
export const selectProductList = (obj: any) => {
    return request({
        url: '/product/list',
        method: 'get',
        params: obj
    });
};
//生产列表
export const selectBatchList = (obj: any) => {
    return request({
        url: '/production/list',
        method: 'post',
        data: obj
    });
};

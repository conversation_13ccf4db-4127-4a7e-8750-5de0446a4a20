/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-11-01 18:29:18
 * @LastEditTime: 2022-11-01 18:29:18
 * @LastEditors: PhilRandWu
 */
import request from '../request';
//员工接口
export const LoGin = (obj: any) => {
    const { phoneNumber, password, imageCode } = obj;

    return request({
        url: '/login',
        method: 'post',
        data: {
            phoneNumber,
            password,
            imageCode
        },
        headers: {
            ['x-request-id']: obj['x-request-id']
        }
    });
};
//用户信息
export const getInfo = () => {
    return request({
        url: '/user/info',
        method: 'get'
    });
};

// tokenvalid
export const authTokenValid = (token: string) => {
    return request({
        method: 'get',
        url: '/cmBaasLogin',
        params: {
            token: token
        }
    });
};

// 初次修改密码
export const firstReset = (data: { userId: number; password: string }) => {
    return request({
        method: 'post',
        url: '/user/firstReset',
        data
    });
};
// 登出
export const logOut = () => {
    return request({
        method: 'post',
        url: '/logout',
        data: {}
    });
};

/**
 * ⽣成随机验证码图⽚
 */
export const getCode = () => {
    return request({
        url: '/user/getImageCode',
        method: 'get',
        responseType: 'blob'
    });
};

//应用标题信息信息
//标题所属页面(0:web端,1:h5端),不传则返回所有
export const getTitle = (data:{titleType?:number}) => {
    return request({
        url: '/getTitle',
        method: 'post',
        data,
    });
};

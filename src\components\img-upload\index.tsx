// import ImgCrop from 'antd-img-crop';
import React, { useState, useEffect } from 'react';
import { message, Modal, Upload } from 'antd';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';
// import { PublicServices } from '@services/modules/public';
import { temporaryUploadUrl } from '@services/food';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import axios from 'axios';
import { fileUpload, getFileUrlFormUploadedFile } from '@utils';
import { getPreviewUrl } from '@services';

interface IImageCropUpload {
    isCrop: boolean;
    beforeUpload?: (file: RcFile) => boolean;
    value?: any;
    onChange?: any;
    maxAmount?: number;
    tips?: any;
    isUpLoading?: any;
    setIsUpLoading?: any;
}

type allowFileType = {
    allowFileType: string[];
    messageText: string;
};

interface IAllowFileType {
    allowFileType: allowFileType;
    allowSize: number;
}

// 可快捷判断是否是指定类型与大小
export function isAllowFileType({
    allowFileType = { allowFileType: ['jpg', 'jpeg', 'png'], messageText: '图片格式支持:jpg/jpeg/png' },
    allowSize = 5
}: IAllowFileType) {
    // 判断图片类型
    console.log('isAllowFileType00000==00')
    return (file: any) => {
        console.log('isAllowFileType',file)
        const fileType = file?.name?.split('.').at(-1).toLowerCase();
        const isAllow = allowFileType?.allowFileType?.includes(fileType);
        if (!isAllow) {
            message.error(`${allowFileType?.messageText}`);
            return Upload.LIST_IGNORE;
        }

        // 判断图片大小
        if (file?.size / (1024 * 1024) > allowSize) {
            message.error(`文件大小超过 ${allowSize}M`);
            return Upload.LIST_IGNORE;
        }
        console.log('isAllowFileType===end')
        return true;
    };
}

const ImgCropUpload = (props: IImageCropUpload) => {
    // const propsValue: any[] = Array.isArray((props as any)?.value)
    //     ? (props as any)?.value
    //     : (props as any)?.value?.fileList;
    // useEffect(() => {
    //     // console.log((props as any)?.value);
    //     if (propsValue) {
    //         setFileList(propsValue);
    //     }
    // }, [propsValue]);

    const [fileList, setFileList] = useState<UploadFile[]>((props as any)?.value || []);
    const [previewOpen, setPreviewOpen] = useState(false);
    const [previewSrc, setPreviewSrc] = useState('');

    const beforeCrop = (e: any) => {
        console.log('beforeCrop',e)
        if (props?.beforeUpload) {
            const allowUpload = props?.beforeUpload(e);
            if (allowUpload !== true) {
                return false;
            }
            return allowUpload;
        }
        return true;
    };

    const cropConfig = {
        resize: false, //裁剪是否可以调整大小
        resizeAndDrag: true, //裁剪是否可以调整大小、可拖动
        modalTitle: '上传图片', //弹窗标题
        modalWidth: 600, //弹窗宽度
        modalOk: '确定',
        modalCancel: '取消',
        beforeCrop: beforeCrop
    };

    const onPreview = async (file: UploadFile) => {
        console.log('onPreview',file)
        let src = file?.response?.fileUrl as string;
        // const imgSrc = await getPreviewUrl(src);
        const imgSrc = file?.thumbUrl? file?.thumbUrl : src
        console.log(imgSrc);
        setPreviewSrc(imgSrc);
        setPreviewOpen(true);
    };

    const onRemove = (file: UploadFile) => {
        console.log(file);
        setFileList(fileList.filter((item) => item.uid !== file.uid));
    };

    const handleRequest = async (p: any) => {
        const formData = new FormData();
        formData.append(p.file?.name, p.file);
        const faceUrlResult: any = await temporaryUploadUrl({ fileName: encodeURIComponent(p?.file?.name) });
        console.log(faceUrlResult);
        const noAuthUrl = faceUrlResult?.data?.split('?')[0];
        const result: any = { method: 'put', url: faceUrlResult?.data, data: p?.file };
        console.log(result);
        axios(result).then((res) => {
            if (res?.status === 200) {
                console.log('res', res);
                const newFileLists: UploadFile[] = [
                    ...fileList,
                    {
                        uid: noAuthUrl,
                        name: noAuthUrl,
                        status: 'done',
                        url: noAuthUrl
                    }
                ];
                setFileList(newFileLists);
                props?.onChange && props?.onChange(newFileLists);
            }
        });
    };

    const UploadDom = (
        <div>
            <Upload
                // action='https://www.mocky.io/v2/5cc8019d300000980a055e76'
                listType='picture-card'
                fileList={props?.value}
                onPreview={onPreview}
                onRemove={onRemove}
                customRequest={({ file, onError, onProgress, onSuccess }) => {
                    // @ts-ignore
                    console.log('customRequest',props,file)
                    fileUpload({
                        ...{ file, onError, onProgress, onSuccess },
                        isUploading: props?.isUpLoading,
                        setIsUpLoading: props?.setIsUpLoading
                    });
                }}
                onChange={({ fileList: newFileList }) => {
                    console.log(newFileList, 'newFileListnewFileListnewFileList');
                    props?.onChange && props?.onChange(newFileList);
                }}
                accept='.jpg,.jpeg,.png'
                beforeUpload={isAllowFileType({
                    allowFileType: { allowFileType: ['jpg', 'jpeg', 'png'], messageText: '图片仅支持:jpg/jpeg/png' },
                    allowSize: 5
                })}
                // progress={{
                //     strokeColor: {
                //         '0%': '#108ee9',
                //         '100%': '#87d068'
                //     },
                //     strokeWidth: 3,
                //     format: (percent) => percent && `${parseFloat(percent.toFixed(2))}%`
                // }}
                // {...props}
            >
                {/* {'+ 选择图片'} */}
                {props?.maxAmount
                    ? (!props?.value || props?.value?.length < props?.maxAmount) && (
                          <div>
                              <PlusOutlined rev={undefined} />
                              <div style={{ marginTop: 8 }}>上传</div>
                          </div>
                      )
                    : ''}
            </Upload>
            <div>{props?.tips}</div>
        </div>
    );

    return (
        <>
            {/* {props.isCrop ? <ImgCrop {...cropConfig}>{UploadDom}</ImgCrop> : { UploadDom }} */}
            {UploadDom}
            <Modal
                maskClosable={false}
                open={previewOpen}
                title='预览'
                footer={null}
                onCancel={() => {
                    setPreviewOpen(false);
                }}
            >
                <img alt='example' style={{ width: '100%' }} src={previewSrc} />
            </Modal>
        </>
    );
};

export default ImgCropUpload;

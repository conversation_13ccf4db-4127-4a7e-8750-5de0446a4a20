/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-11 11:58:07
 * @LastEditTime: 2022-11-01 18:04:47
 * @LastEditors: PhilRandWu
 */
/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-11 11:04:25
 * @LastEditTime: 2022-10-11 11:56:30
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BaseCollapse from '@components/base-collapse';
import PageTitle from '@components/page-title';
import { Form, message, Modal, Tooltip, Typography, List, Divider } from 'antd';
import React from 'react';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import { codeDetail } from '@services/trace-source-code';
import { useLocation } from 'react-router-dom';
import { useMutation, useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { SourceInfoConfig, FoodInfoConfig, ProductInfoConfig, QsqaInfoConfig } from './config';
import BaseModal from '@components/base-modal';
import './index.less';
import { randomPassword } from '@utils';
function Back2SourceCodeDetail() {
    const navigate = useNavigate();
    const [SourceCodeForm] = Form.useForm();
    const [FoodForm] = Form.useForm();
    const [ProductForm] = Form.useForm();
    const [QsqaForm] = Form.useForm();
    const [materialInfoForm] = Form.useForm();
    const [addEmployeesForm] = Form.useForm();

    const [isModalOpen, setIsModalOpen] = useState(false);
    const { state } = useLocation();
    // console.log('state', state);
    const codedetail = useQuery(
        ['codedetail'],
        () => {
            return codeDetail({
                traceCodeId: state?.id
            });
        },
        {
            onError(err: any) {
                randomPassword(err);
            },
            retry: false
        }
    );
    const materialInfo = [
        ...(codedetail?.data?.data?.materialInfo || [])?.map((item: any, index: any) => {
            const materialdata = [
                {
                    label: '原料名称',
                    name: 'materialName',
                    value: '123',
                    type: 'Display',
                    span: 8,
                    displayDom: item.materialName
                },
                {
                    label: '产品合格证明材料',
                    name: 'certificateQualification',
                    value: '123',
                    type: 'Url',
                    span: 8,
                    tips: '点击下载',
                    display: <a href={item?.certificateQualification}>{'点击下载'}</a>
                },
                {
                    label: '企业/机构名称',
                    name: 'companyName',
                    value: '123',
                    type: 'Display',
                    span: 8,
                    displayDom: item.companyName
                },
                // {
                //     label: '区块号',
                //     name: 'blockNum',
                //     value: '123',
                //     type: 'Display',
                //     span: 8,
                //     displayDom: item.blockNum
                // },
                {
                    label: '链上哈希',
                    name: 'transactionId',
                    value: '123',
                    type: 'Display',
                    span: 16,
                    displayDom: item.transactionId
                }
            ];
            return materialdata;
        })
    ];
    console.log('materialInfo', materialInfo);
    // console.log('SourceInfoConfig', SourceInfoConfig);
    const source = codedetail?.data?.data?.codeInfo;
    const SourceInfoConfig = [
        {
            label: '溯源码',
            name: 'code',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: source?.code
        },
        {
            label: '所属码包',
            name: 'packNumber',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: source?.packNumber
        },
        {
            label: '生码时间',
            name: 'createTime',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: dayjs(source?.createTime).format('YYYY-MM-DD')
        },
        {
            label: '状态',
            name: 'state',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: !source?.state ? '可用' : '禁用'
        },
        {
            label: '所属产品',
            name: 'foodName',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: source?.foodName
        },
        {
            label: '所属生产批次',
            name: 'productionBatch',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: source?.productionBatch
        },
        {
            label: '查询次数',
            name: 'searchNumber',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: source?.searchNumber === 0 ? '0次' : source?.searchNumber + '次'
        },
        {
            label: '查询记录',
            name: 'record',
            value: '123',
            type: 'Url',
            span: 8,
            display: (
                <a
                    onClick={() => {
                        setIsModalOpen(true);
                    }}
                >
                    {'点击查看'}
                </a>
            )
        }
    ];
    const FoodInfoConfig = [
        {
            label: '食品名称',
            name: 'foodName',
            value: '123',
            type: 'Display',
            span: 8,
            show: codedetail?.data?.data?.foodInfo?.foodName ? null : '1',
            displayDom: codedetail?.data?.data?.foodInfo?.foodName
        },
        {
            label: '食品编码',
            name: 'foodCode',
            value: '123',
            type: 'Display',
            span: 8,
            show: codedetail?.data?.data?.foodInfo?.foodCode ? null : '1',
            displayDom: codedetail?.data?.data?.foodInfo?.foodCode
        },
        {
            label: '食品品类',
            name: 'foodCategory',
            value: '123',
            type: 'Display',
            span: 8,
            show: codedetail?.data?.data?.foodInfo?.foodCategory ? null : '1',
            displayDom: codedetail?.data?.data?.foodInfo?.foodCategory
        },
        {
            label: '食品单位',
            name: 'foodUnit',
            value: '123',
            type: 'Display',
            span: 8,
            show: codedetail?.data?.data?.foodInfo?.foodUnit ? null : '1',
            displayDom: codedetail?.data?.data?.foodInfo?.foodUnit
        },
        {
            label: '保质期',
            name: 'expirationDate',
            value: '123',
            type: 'Display',
            span: 8,
            show: codedetail?.data?.data?.foodInfo?.expirationDate ? null : '1',
            displayDom: codedetail?.data?.data?.foodInfo?.expirationDate
        },
        {
            label: '食品规格',
            name: 'specification',
            value: '123',
            type: 'Display',
            span: 8,
            show: codedetail?.data?.data?.foodInfo?.specification ? null : '1',
            displayDom: codedetail?.data?.data?.foodInfo?.specification
        },
        // {
        //     label: '区块号',
        //     name: 'blockNum',
        //     value: '123',
        //     type: 'Display',
        //     span: 8,
        //     show: codedetail?.data?.data?.foodInfo?.blockNum ? null : '1',
        //     displayDom: codedetail?.data?.data?.foodInfo?.blockNum
        // },
        {
            label: '链上哈希',
            name: 'transactionId',
            value: '123',
            type: 'Display',
            span: 8,
            show: codedetail?.data?.data?.foodInfo?.transactionId ? null : '1',
            displayDom: codedetail?.data?.data?.foodInfo?.transactionId
        }
    ];

    // FoodForm.setFieldsValue({
    //     foodName: codedetail?.data?.data?.foodInfo?.foodName,
    //     foodCode: codedetail?.data?.data?.foodInfo?.foodCode,
    //     foodCategory: codedetail?.data?.data?.foodInfo?.foodCategory,
    //     foodUnit: codedetail?.data?.data?.foodInfo?.foodUnit,
    //     expirationDate: codedetail?.data?.data?.foodInfo?.expirationDate,
    //     specification: codedetail?.data?.data?.foodInfo?.specification,
    //     blockNum: codedetail?.data?.data?.foodInfo?.blockNum,
    //     transactionId: codedetail?.data?.data?.foodInfo?.transactionId
    // });
    const Product = codedetail?.data?.data?.productionInfo;

    const ProductInfoConfig: any = [
        {
            type: 'Display',
            span: 8,
            name: 'productionDate',
            label: '生产日期',
            show: Product?.productionDate ? null : '1',
            displayDom: dayjs(Product?.productionDate).format('YYYY-MM-DD')
        },
        {
            span: 8,
            type: 'Display',
            name: 'productionBatch',
            show: Product?.productionBatch ? null : '1',
            displayDom: Product?.productionBatch,
            label: '生产批次'
        },
        {
            span: 8,
            type: 'Display',
            name: 'productionLine',
            show: Product?.productionLine ? null : '1',
            displayDom: Product?.productionLine,
            label: '生产线'
        },
        {
            span: 8,
            type: 'Display',
            name: 'productionShift',
            show: Product?.productionShift ? null : '1',
            displayDom: Product?.productionShift,
            label: '种植户'
        },
        {
            span: 8,
            type: 'Display',
            name: 'productionPlace',
            show: Product?.productionPlace ? null : '1',
            displayDom: Product?.productionPlace,
            label: '生产地点'
        },
        {
            span: 8,
            type: 'Display',
            name: 'environmentInfo',
            show: Product?.environmentInfo ? null : '1',
            displayDom: Product?.environmentInfo,
            label: '生产环境信息'
        },
        {
            span: 8,
            type: 'Display',
            name: 'personLiable',
            show: Product?.personLiable ? null : '1',
            displayDom: Product?.personLiable,
            label: '责任人员'
        },
        {
            span: 8,
            type: 'Display',
            name: 'contactNumber',
            show: Product?.contactNumber ? null : '1',
            displayDom: Product?.contactNumber,
            label: '联系电话'
        },
        {
            span: 8,
            type: 'Display',
            name: 'checkTime',
            show: Product?.checkTime ? null : '1',
            displayDom: dayjs(Product?.checkTime).format('YYYY-MM-DD'),
            label: '抽检时间'
        },
        {
            span: 8,
            type: 'Display',
            name: 'checkRecord',
            show: Product?.checkRecord ? null : '1',
            displayDom: Product?.checkRecord,
            label: '抽检记录'
        },
        {
            span: 8,
            type: 'Display',
            name: 'sampleTime',
            show: Product?.sampleTime ? null : '1',
            displayDom: dayjs(Product?.sampleTime).format('YYYY-MM-DD'),
            label: '留样时间'
        },
        {
            span: 8,
            type: 'Display',
            name: 'sampleRecord',
            show: Product?.sampleRecord ? null : '1',
            displayDom: Product?.sampleRecord,
            label: '留样记录'
        },
        {
            span: 8,
            type: 'Display',
            name: 'productionAccessory',
            label: '附件',
            show: Product?.productionAccessory ? null : '1',
            display: <a href={Product?.productionAccessory}>{'点击下载'}</a>
        },
        {
            label: '企业/机构名称',
            name: 'companyName',
            value: '123',
            type: 'Display',
            span: 8,
            show: Product?.companyName ? null : '1',
            displayDom: Product?.companyName
        },
        // {
        //     label: '区块号',
        //     name: 'blockNum',
        //     value: '123',
        //     type: 'Display',
        //     span: 8,
        //     show: Product?.blockNum ? null : '1',
        //     displayDom: Product?.blockNum
        // },
        {
            label: '链上哈希',
            name: 'transactionId',
            value: '123',
            type: 'Display',
            span: 8,
            show: Product?.transactionId ? null : '1',
            displayDom: Product?.transactionId
        }
    ];
    const test = codedetail?.data?.data?.testInfo;
    const QsqaInfoConfig: any = [
        {
            type: 'Display',
            name: 'foodNumber',
            span: 8,
            label: '食品批号',
            show: test?.foodNumber ? null : '1',
            displayDom: test?.foodNumber
        },
        {
            span: 8,
            type: 'Display',
            name: 'testRecord',
            label: '质检内容',
            show: test?.testRecord ? null : '1',
            displayDom: test?.testRecord
        },
        {
            span: 8,
            type: 'Display',
            name: 'testTime',
            label: '质检时间',
            show: test?.testTime ? null : '1',
            displayDom: dayjs(test?.testTime).format('YYYY-MM-DD')
        },
        {
            span: 8,
            type: 'Display',
            name: 'testResult',
            label: '质检结果',
            show: test?.testResult === 0 || test?.testResult === 1 ? null : '1',
            displayDom: test?.testResult ? '不合格' : '合格'
            // displayDom: (
            //     <h3 className={test?.testResult ? 'ErrorStatus' : 'SuccessStatus'}>
            //         {test?.testResult ? '不合格' : '合格'}
            //     </h3>
            // )
        },
        {
            span: 8,
            type: 'Display',
            name: 'testPersonnel',
            label: '质检人员',
            show: test?.testPersonnel ? null : '1',
            displayDom: test?.testPersonnel
        },
        {
            span: 8,
            type: 'Display',
            name: 'testReport',
            label: '质检报告',
            show: test?.testReport ? null : '1',
            display: <a href={test?.testReport}>{'点击下载'}</a>
        },
        {
            span: 8,
            type: 'Display',
            name: 'phone',
            label: '联系电话',
            show: test?.phone ? null : '1',
            displayDom: test?.phone
        },
        {
            span: 8,
            type: 'Display',
            name: 'testAccessory',
            label: '附件',
            show: test?.testAccessory ? null : '1',
            display: <a href={test?.testAccessory}>{'点击下载'}</a>
        },
        {
            label: '企业/机构名称',
            name: 'companyName',
            value: '123',
            type: 'Display',
            span: 8,
            show: test?.companyName ? null : '1',
            displayDom: test?.companyName
        },
        // {
        //     label: '区块号',
        //     name: 'blockNum',
        //     value: '123',
        //     type: 'Display',
        //     span: 8,
        //     show: test?.blockNum ? null : '1',
        //     displayDom: test?.blockNum
        // },
        {
            label: '链上哈希',
            name: 'transactionId',
            value: '123',
            type: 'Display',
            span: 8,
            show: test?.transactionId ? null : '1',
            displayDom: test?.transactionId
        }
    ];

    const valuesForm = SourceCodeForm.getFieldsValue();
    console.log('SourceCodeForm', SourceCodeForm, valuesForm);

    const showModal = () => {
        setIsModalOpen(true);
    };

    const handleOk = () => {
        setIsModalOpen(false);
    };

    const handleCancel = () => {
        setIsModalOpen(false);
    };
    const data = [
        ...(source?.records || [])?.map((item: any) => {
            // return '查询时间:' + dayjs(item).format('YYYY-MM-DD HH:mm')
            return (
                <>
                    <p>查询时间 : </p>
                    <span className='time'> {dayjs(item).format('YYYY-MM-DD HH:mm')} </span>
                </>
            );
        })
    ];
    // const data = [
    //     'Racing car sprays burning fuel into crowd.',
    //     'Japanese princess to wed commoner.',
    //     'Australian walks 100km after outback crash.',
    //     'Man charged over missing wedding girl.',
    //     'Los Angeles battles huge wildfires.',
    // ];
    console.log('data998768', data);
    return (
        <>
            <BaseCard title={<PageTitle title='溯源码详情' bg='container su' />}>
                <Form
                    // name="sourceCode"
                    form={SourceCodeForm}
                    // initialValues={{
                    //     code: 123,
                    //     package: '123'
                    // }}
                >
                    <BaseCollapse
                        Itemkey={1}
                        headTitle={<PageTitle title='溯源码详情' type='primaryIcon' />}
                        itemConfig={SourceInfoConfig}
                    />
                </Form>
                <Form form={FoodForm}>
                    <BaseCollapse
                        Itemkey={2}
                        headTitle={<PageTitle title='食品信息' type='primaryIcon' />}
                        itemConfig={FoodInfoConfig}
                    />
                </Form>
                {codedetail?.data?.data?.materialInfo && codedetail?.data?.data?.materialInfo.length > 0 && (
                    <Form form={materialInfoForm}>
                        <BaseCollapse
                            Itemkey={3}
                            headTitle={<PageTitle title='原料信息' type='primaryIcon' />}
                            itemConfig={materialInfo.flat()}
                        />
                    </Form>
                )}
                <Form form={ProductForm}>
                    <BaseCollapse
                        Itemkey={3}
                        headTitle={<PageTitle title='生产信息' type='primaryIcon' />}
                        itemConfig={ProductInfoConfig}
                    />
                </Form>
                <Form form={QsqaForm}>
                    <BaseCollapse
                        Itemkey={4}
                        headTitle={<PageTitle title='质检信息' type='primaryIcon' />}
                        itemConfig={QsqaInfoConfig}
                    />
                </Form>
            </BaseCard>
            <Modal
                open={isModalOpen}
                onOk={handleOk}
                onCancel={handleCancel}
                wrapClassName='open'
                cancelButtonProps={{ style: { display: 'none' } }}
            >
                <>
                    {source?.searchNumber === 0 ? (
                        <p>暂无数据</p>
                    ) : (
                        <List
                            size='small'
                            bordered
                            className='bodydata'
                            dataSource={data}
                            pagination={{
                                onChange: (page) => {
                                    console.log(page);
                                },
                                pageSize: 5
                            }}
                            renderItem={(item: any) => <List.Item>{item}</List.Item>}
                        />
                    )}
                </>
            </Modal>
        </>
    );
}

export default Back2SourceCodeDetail;

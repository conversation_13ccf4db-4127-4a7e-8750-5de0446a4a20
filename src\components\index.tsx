import { Form, message, Image, Space } from 'antd';

export const FormItemImages = ({ value, ...rest }: any) => {
    const imgs = Array.isArray(value) ? value : [value];
    const styles = {
        width:rest?.width
    }
    return value ? (
        <Image.PreviewGroup>
            <Space style={{width:rest?.width}}>
                {imgs.map((url) => {
                    return <Image src={url} {...rest}></Image>;
                })}
            </Space>
        </Image.PreviewGroup>
    ) : (
        <span>-</span>
    );
};

export const FormItemVideo = ({ value, ...rest }: any) => {
    return value ? <video src={value} {...rest}></video> : <span>-</span>;
};

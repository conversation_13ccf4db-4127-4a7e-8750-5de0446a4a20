.singleSearchContainer {
    display: flex;
    align-items: center;

    .singleSearchInput {
        margin-left: 5px;
        margin-right: 8px;
        background: transparent;
        .ant-input-affix-wrapper {
            background: transparent;
            // border-right: none;
            input {
                background: transparent;
            }
        }
        .ant-input-group-addon {
            background-color: transparent !important;
            button {
                // margin-left: -30px;
                // border: none !important;
                background: transparent !important;
                // border-left: none;
            }
        }

        // .ant-input-suffix {
        //     display: none;
        // }
    }
}
.searchConfig-input {
    width: 280px !important;
}
.ant-btn:hover,
.ant-btn:focus {
    color: #76ae55;
    border-color: #76ae55;
}
.ant-input-search > .ant-input-group > .ant-input-group-addon:last-child .ant-input-search-button {
    border-color: #76ae55;
}

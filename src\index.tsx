/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-19 14:56:33
 * @LastEditTime: 2022-10-14 11:29:54
 * @LastEditors: PhilRandWu
 */
import React from 'react';
import ReactDOM from 'react-dom/client';
import store from '@store';
import './styles/index.less';
import App from './app';

import { QueryClient, QueryClientProvider } from 'react-query';
import zhCN from 'antd/es/locale/zh_CN';
import moment from 'moment';
import 'moment/locale/zh-cn';
import { ConfigProvider, DatePicker, message } from 'antd';
import { Provider } from 'react-redux';

const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            refetchOnWindowFocus: false
        }
    }
});

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
    <QueryClientProvider client={queryClient}>
        <ConfigProvider locale={zhCN}>
            <Provider store={store}>
                <App />
            </Provider>
        </ConfigProvider>
    </QueryClientProvider>
);

import { message } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';
const ReformChainError = (error: any) => {
    console.log('error88', error);
    if (error?.data?.code === 500) {
        message.error(error.data.message);
    } else if (error?.data?.code === '5002005') {
        // navigate('/login');
        message.error(error.data.message);
    }else if (error?.response?.data?.status === 400) {
        message.error(error.response.data.message);
    } else {
        message.error(error?.data?.message || "操作失败")
    }
};
// const reformChainError = (error: any, name?: string) => {
//     message.destroy();
//     let errorObject = error;
//     let str = '';
//     if (typeof errorObject === 'string') {
//         errorObject = JSON.parse(errorObject);
//     }

//     if (errorObject && errorObject.error) {
//         const error = errorObject.error;

//         if (
//             error.name === 'expired_tx_exception' &&
//             error.what === 'Expired Transaction'
//         ) {
//             // 交易超时
//             message.error('请求超时,请重试');
//         } else if (error.details && error.details.length > 0) {
//             const details = error.details;

//             if (
//                 details[0].message ===
//                 'assertion failure with message: symbol already exist'
//             ) {
//                 message.error('符号重复,请重新填写');
//             }
//         }
//     } else if (
//         errorObject.toString() ===
//         `Error: unknown key (boost::tuples::tuple<bool, eosio::chain::name, boost::tuples::null_type, boost::tuples::null_type, boost::tuples::null_type, boost::tuples::null_type, boost::tuples::null_type, boost::tuples::null_type, boost::tuples::null_type, boost::tuples::null_type>): (0 ${name})`
//     ) {
//         str = 'account not existed';
//     } else if (errorObject.toString() === `Error: Invalid symbol`) {
//         str = '类别符号不合法';
//         message.error(str);
//     } else if (
//         errorObject.toString() ===
//         `Error: assertion failure with message: symbol already exist`
//     ) {
//         str = '类别符号已存在,不可重复';
//         message.error(str);
//     } else if (
//         errorObject
//             .toString()
//             .indexOf(`Error: account ${name} has insufficient ram;`) === 0
//     ) {
//         message.error('账户内存不足,请联系管理员');
//     } else if (
//         errorObject.toString().indexOf(`Error: missing authority of `) === 0
//     ) {
//         message.error('该账户没有操作权限');
//     } else if (
//         errorObject.toString() ===
//         `Error: assertion failure with message: already in same approve state`
//     ) {
//         message.error('该链账户已授权');
//     } else if (
//         errorObject.toString() ===
//         `assertion failure with message: account not exists`
//     ) {
//         message.error('账户不存在');
//     } else if (
//         errorObject.toString() ===
//         `Error: Name should be less than 13 characters, or less than 14 if last character is between 1-5 or a-j, and only contain the following symbols .12345abcdefghijklmnopqrstuvwxyz`
//     ) {
//         message.error('链账户名称为12个字符，只能包括【12345和小写字母】');
//     } else if (
//         errorObject.toString() ===
//         `Error: assertion failure with message: not active sender`
//     ) {
//         message.error('账户已被冻结');
//     } else if (
//         errorObject.toString() ===
//         `Error: assertion failure with message: not active to user`
//     ) {
//         message.error('接收账户已被冻结');
//     } else if (
//         errorObject.toString() ===
//         `Error: expected string containing public key`
//     ) {
//         message.error('公钥格式不正确,请重试或联系管理员');
//     } else {
//         try {
//             message.error((error || '').toString());
//         } catch (error) {
//             message.error('未知错误, 请联系管理员');
//         }
//     }
//     return str;
// };

export { ReformChainError };

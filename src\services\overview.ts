import request from "@services/request";

export default class OverViewService {
    public static async getTraceCount() {
        return request({
            url: '/traceOverview/getTraceCount',
            method: 'post',
            data: {}
        })
    }

    public static async getTraceProductRank(data: {
        coreId: number,
        pageIndex: number,
        pageSize: number,
        sortBy: number
    }) {
        return request({
            url: '/traceOverview/getTraceProductRank',
            method: 'post',
            data
        })
    }

    public static async Query() {
        return request({
            url: '/traceOverview/traceOverView',
            method: 'post',
            data: {}
        })
    }
    public static async BarChart(data:any) {
      return request({
          url: '/LandPlantBatch/getLandPlantBatchBarChart',
          method: 'post',
          data,
      })
  }
  public static async LandState(data:any) {
    return request({
        url: '/landState/getLandState',
        method: 'get',
    })
}

// 气象
public static async LandWeather(data:any) {
  return request({
      url: '/LandWeather/getLandWeather',
      method: 'get',
  })
}

public static async ChainProductCount(data:any) {
  return request({
      url: '/traceOverview/getChainProductCount',
      method: 'get',
  })
}
public static async ProductionCountGroupMonth(data:any) {
  return request({
      url: '/traceOverview/getProductionCountGroupMonth',
      method: 'get',
  })
}

public static async ProductionStatistical(data:any) {
  return request({
      url: '/traceOverview/selectProductionStatistical',
      method: 'post',
      data,
  })
}
public static async getTraceCountDay(data:any) {
  return request({
      url: '/traceOverview/getTraceCountDay',
      method: 'get',
  })
}
// 商品轮播图
public static async getProductImageList() {
  return request({
      url: '/traceOverview/getProductImageList',
      method: 'get',
  })
}
// 商品生产饼图
public static async getProductionRatioGroupProduct(data:any) {
  return request({
      url: '/traceOverview/getProductionRatioGroupProduct',
      method: 'get',
      params:data
  })
}

// 获取实时种植作物表
public static async getLandGrowingPage(data:any) {
  return request({
      url: '/traceOverview/getLandGrowingPage',
      method: 'post',
      data,
  })
}
// 最近质检批次列表
public static async getLatestQualityPage(data:any) {
  return request({
      url: '/traceOverview/getLatestQualityPage',
      method: 'post',
      data,
  })
}

// 获取质检图表信息
public static async getQualityInfo() {
  return request({
      url: '/traceOverview/getQualityInfo',
      method: 'get',
  })
}
// 收购列表
public static async getMaterialPurchaseList(data:any) {
  return request({
      url: '/materialPurchase/getMaterialPurchaseList',
      method: 'post',
      data,
  })
}
// 收购列表右侧信息
public static async getMaterialPurchaseSummary() {
  return request({
      url: '/traceOverview/getMaterialPurchaseSummary',
      method: 'get',
  })
}
// 销售额信息
public static async getOverviewInfo() {
  return request({
      url: '/traceOverview/sales-overview-data',
      method: 'get',
  })
}

// 销售折线图数据
public static async getSalesChart() {
  return request({
      url: '/traceOverview/sales-trend-chart',
      method: 'get',
  })
}

// 销售饼图
public static async getsalesDonutChart(data:any) {
  return request({
      url: 'traceOverview/sales-donut-chart',
      method: 'get',
      params:data
  })
}

// 仓储管理
public static async getTraceCountDayWar() {
  return request({
      url: '/warehouse/getWarehouseSummary',
      method: 'get',
  })
}
}

import request from '../request';

export interface ISourceLogisticsQuery {
    coreId: number,
    endTime: string,
    orgId: number,
    pageIndex: number,
    pageSize: number,
    startTime: string,
    state: number,
    transNumber: string,
    type: number
}

export enum SourceLogisticsStateEnum {
    "运输中",
    "已完成",
    "已作废"
}
export enum SourceLogisticsTypeEnum {
    _,
    "自行运输",
    "委托运输"
}


export default class SourceLogisticsService {
    public static async Query(data: ISourceLogisticsQuery) {
        return request({
            url: '/traceData/logisticsPage',
            method: 'post',
            data
        });
    };

    public static async detail(id: number) {
        return request({
            url: '/traceData/logisticsDetail',
            method: 'get',
            params: {id}
        });
    }
}
/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 11:16:50
 * @LastEditTime: 2022-11-01 18:38:47
 * @LastEditors: PhilRandWu
 */
import { DatePicker, Form, Input, Select } from 'antd';
import './index.less';

const { Option } = Select;
const { RangePicker } = DatePicker;
const SearchForm = () => {
    const item = [
        {
            title: '禁用',
            value: '1'
        },
        {
            title: '可用',
            value: '0'
        }
    ];

    return (
        <>
            <Form.Item label='搜索' name='search'>
                <Input placeholder='请输入' className='searchInput' />
            </Form.Item>
            <Form.Item label='状态' name='type'>
                <Select placeholder='全部' allowClear className='searchSelect' style={{ width: 200 }}>
                    {item?.map((option: any) => {
                        return (
                            option && (
                                <Option key={option?.value} value={option?.value}>
                                    {option?.title}
                                </Option>
                            )
                        );
                    })}
                </Select>
            </Form.Item>
        </>
    );
};

export default SearchForm;

/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:27:42
 * @LastEditTime: 2022-11-01 18:23:14
 * @LastEditors: PhilRandWu
 */
export const addPartiesConfigs = [
    {
        label: '参与方名称',
        type: 'Input',
        value: 'name',
        placeholder: '请输入参与方名称',
        rules: [{ required: true, message: '请输入参与方名称!' }]
    },
    {
        label: '参与方类型',
        type: 'Radio',
        value: 'type',
        placeholder: '请选择',
        rules: [{ required: true, message: '请输入联系方式' }],
        fields: [
            {
                value: '12发给3',
                label: '供应商'
            },
            {
                value: '12dfvdfdf3',
                label: '质检机构'
            },
            {
                value: '12ddvsdfsffgnf3',
                label: '监管机构'
            }
        ]
    },
    {
        label: '备注',
        showCount: true,
        type: 'TextArea',
        value: 'permission',
        placeholder: '请选择',
        rules: [{ required: true, message: '请填写备注!' }]
    }
];

export const editEmployeesConfigs = [
    {
        label: '员工名称',
        type: 'Input',
        value: 'name',
        placeholder: 'erferferf',
        rules: [{ required: true, message: '请输入员工名称!' }]
    },
    {
        label: '联系方式',
        type: 'Input',
        value: 'name',
        placeholder: 'erferferf',
        rules: [{ required: true, message: '请输入联系方式!' }]
    },
    {
        label: '管理员账号',
        type: 'TagsSelect',
        value: 'account',
        placeholder: 'fefer',
        rules: [{ required: true, message: '请输入管理员账号!' }],
        fields: [
            {
                value: '12发给3',
                label: '123vfv123'
            },
            {
                value: '12dfvdfdf3',
                label: '1231vdfvdfvdd23'
            },
            {
                value: '12ddvsdfsffgnf3',
                label: '123gfbgfbfgb123'
            }
        ]
    }
];

export const searchConfig = [
    {
        label: '批次号',
        type: 'Input',
        value: 'name',
        placeholder: '输入批次号',
        span: 11
    },
    {
        label: '选择日期',
        type: 'RangePicker',
        value: 'Time',
        placeholder: ['请选择'],
        span: 13,
        wide:260
    }
];

/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 10:01:45
 * @LastEditTime: 2022-10-10 11:43:22
 * @LastEditors: PhilRandWu
 */
import BaseButton from '@components/base-button';
import BaseCard from '@components/base-card';
import FilterForm from '@components/filter-form';
import PageTitle from '@components/page-title';
import { addFood, foodCategory } from '@services/food';
import { Form, message, Upload, Button, Select, InputNumber, Input } from 'antd';
import { useMutation, useQuery } from 'react-query';
import React, { Component, useEffect, useState } from 'react';
import type { FormInstance } from 'antd/es/form';
import { addConfigs } from './config';
import styles from './index.module.less';
import { ReformChainError } from '@utils/errorCodeReform';
import { useNavigate } from 'react-router-dom';
import { signData } from '../../utils/blockChainUtils';
import { useDispatch } from 'react-redux';
import { getLocalPrivatekey } from '@utils/blockChainUtils';
import ImgCropUpload from '@components/img-upload';

import { fileUpload, getFileUrlFormUploadedFile } from '@utils';
import { UploadOutlined } from '@ant-design/icons';
import BaseInput from '@components/base-input/base-input';

const { Option } = Select;
const FoodAdd = (props: any) => {
    const [options, setOptions]: any = useState([]);

    const [uploadImageUrl, setUploadImageUrl]: any = useState('');
    const [url, setUrl]: any = useState('');
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [isUpLoading, setIsUpLoading]: any = useState();
    const [fooddata, setfooddata]: any = useState('');
    const [n, setn] = useState(1);
    // 单位
    const [unit, setUnit]: any = useState('1');
    //产品品类
    const foodcategory = useMutation(foodCategory, {
        onSuccess(res) {
            // getLocalPrivatekey(dispatch);
            // @ts-ignore
            const mapOptions = (items: any[]) => {
                return items.map((item) => {
                    if (item.childrenCategory) {
                        return {
                            label: item.categoryName,
                            // value: item.id,
                            value: item.categoryName,
                            children: mapOptions(item.childrenCategory)
                        };
                    } else {
                        return {
                            label: item.categoryName,
                            value: item.id
                        };
                    }
                });
            };
            setOptions(mapOptions(res?.data || []));
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    useEffect(() => {
        foodcategory.mutate({
            n: n,
            name: fooddata
        });
    }, [fooddata]);

    const addfood = useMutation(addFood, {
        onSuccess(res) {
            message.success('新增产品成功');
            navigate('/product-manage/food');
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    const suffixSelector = (
        <Form.Item name='suffix' noStyle>
            <Select
                defaultValue={unit}
                style={{ width: 100 }}
                onChange={(e) => {
                    console.log(e);
                    setUnit(e);
                }}
            >
                <Option value='1'>克（g）</Option>
                <Option value='2'>千克（kg）</Option>
            </Select>
        </Form.Item>
    );
    const addBasicInfoConfigs = [
        {
            type: 'Input',
            label: '产品名称',
            value: 'productName',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,30}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入产品名称!');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            if (value.length > 30) {
                                callback('请保持字符在30字符以内!');
                            } else {
                                callback('请输入产品名称，支持中文、字母或数字!');
                            }
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入产品名称'
        },
        {
            type: 'Cascader',
            label: '产品品类',
            value: 'productCategory',
            rules: [{ required: true, message: '请选择产品品类!' }],
            placeholder: '请选择产品品类',
            fields: options
        },
        {
            type: 'Input',
            label: '保质期',
            value: 'expirationDate',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback('请输入保质期！');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入保质期'
        },
        {
            type: 'Input',
            label: '食品生产许可证编号',
            value: 'productionLicense',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (value === undefined) {
                            callback('请输入食品生产许可证编号!');
                        }
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,500}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入食品生产许可证编号!');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            if (value.length >= 500) {
                                callback('请保持字符在500字符以内!');
                            } else {
                                callback('请输入食品生产许可证编号，支持中文、字母或数字!');
                            }
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入食品生产许可证编号'
        },
        // {
        //     type: 'Input',
        //     label: '产品编码',
        //     value: 'productCode',
        //     placeholder: '请输入产品编码',
        //     rules: [
        //         () => ({
        //             validator: (_: any, value: any, callback: any) => {
        //                 // console.log('valuessss', value === '' || value === undefined);
        //                 const data = value === '' || value === undefined;
        //                 if (data === false) {
        //                     const regExp = new RegExp(/^[0-9]{13}$/);
        //                     const verify = regExp.test(value);
        //                     if (verify === false) {
        //                         callback('请输入长度为13的数字!');
        //                     } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
        //                         callback('字段前后不能输入空格！');
        //                     } else {
        //                         callback();
        //                     }
        //                 } else {
        //                     console.log('sjioooqweppp');
        //                     callback();
        //                 }
        //             }
        //         })
        //     ]
        // },
        {
            type: 'Input',
            label: '产品执行标准',
            value: 'executiveStandard',
            placeholder: '请输入产品执行标准',
            rules: [
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback();
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        // {
        //     type: 'Input',
        //     label: '产品规格',
        //     value: 'specification',
        //     placeholder: '请输入产品规格',
        //     rules: [
        //         { required: true, message: '' },
        //         () => ({
        //             validator: (_: any, value: any, callback: any) => {
        //                 if (!value) {
        //                     callback();
        //                 } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
        //                     callback('字段前后不能输入空格！');
        //                 } else if (value.length > 50) {
        //                     callback('请保持字符在50字符以内!');
        //                 } else {
        //                     callback();
        //                 }
        //             }
        //         })
        //     ]
        // },
        // {
        //     type: 'Custom',
        //     label: '产品规格',
        //     value: 'specification',
        //     rules: [{ required: true, message: '请输入产品规格' }],
        //     children: (
        //         // <Form.Item>
        //         <InputNumber
        //             maxLength={50}
        //             addonAfter={suffixSelector}
        //             placeholder='请输入产品规格'
        //             min={0}
        //             // max={100}
        //             // step={0.01} // 设置步长为0.01确保每次改变都是0.01的倍数
        //             // precision={2} // 限制输入数值保留两位小数
        //             style={{ width: '100%', height: '34px' }}
        //             onChange={(value) => {
        //                 // 可以在此处添加额外的验证逻辑，例如处理非数字输入或超过预期范围的值
        //             }}
        //         />
        //         // <InputNumber addonAfter={suffixSelector} style={{ width: '100%' }} />
        //         // </Form.Item>
        //     )
        // },
        {
            type: 'Input',
            label: '产品规格',
            value: 'specification',
            placeholder: '请输入产品规格',
            addonAfter: suffixSelector,
            maxLength: 50,
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        // const regExp = new RegExp(/^\d+(\.\d+)?$/);
                        const regExp = /^\d+$/;
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入产品规格!');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false || parseInt(value) <= 0) {
                            callback('请输入大于0整数!');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            type: 'Input',
            label: '配料',
            value: 'ingredient',
            rules: [
                { required: false, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback();
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 500) {
                            callback('请保持字符在500字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入配料'
        }
        // {
        //     type: 'Custom',
        //     label: '产品合格证明',
        //     value: 'productAptitude',
        //     children: (
        //         <Form.Item noStyle>
        //             <div className={styles.prodPic}>
        //                 <Form.Item
        //                     name='productAptitude'
        //                     rules={[
        //                         {
        //                             required: false,
        //                             message: '请上传'
        //                         }
        //                     ]}
        //                 >
        //                     <ImgCropUpload
        //                         setIsUpLoading={setIsUpLoading}
        //                         isUpLoading={isUpLoading}
        //                         isCrop={false}
        //                         maxAmount={1}
        //                         tips={'(图片格式支持:jpg/jpeg/png,最大大小为5M)'}
        //                     ></ImgCropUpload>
        //                 </Form.Item>
        //             </div>
        //         </Form.Item>
        //     )
        // }
    ];

    const productVideoAcceptTypes = ['.mp4', '.webm', '.mov', '.m4v'];
    const handleBeforeProductVideoUpload = (file: any) => {
        const fileType = file?.name?.split('.').at(-1).toLowerCase();
        console.log(fileType, file, 'fffffff');
        if (!productVideoAcceptTypes.includes('.' + fileType)) {
            message.error('文件格式不正确');
            return Upload.LIST_IGNORE;
        }
        if (file.size / 1024 / 1024 > 50) {
            message.error('文件过大');
            return Upload.LIST_IGNORE;
        }
        return true;
    };

    const addIntroductionConfigs = [
        {
            type: 'TextArea',
            label: '产品介绍',
            value: 'productIntro',
            placeholder: '请输入产品介绍',
            maxLength: 200,
            showCount: true
        },
        {
            type: 'Custom',
            label: '宣传图片',
            value: 'productImg',
            required: 'requireds',
            placeholder: '(图片格式支持:jpg/jpeg/png,最大大小为20M)',
            children: (
                <Form.Item noStyle>
                    <div className={styles.prodPic}>
                        <Form.Item
                            name='productImg'
                            rules={[
                                {
                                    required: true,
                                    message: '请上传'
                                }
                            ]}
                        >
                            <ImgCropUpload
                                setIsUpLoading={setIsUpLoading}
                                isUpLoading={isUpLoading}
                                isCrop={false}
                                maxAmount={3}
                                tips={'最多支持上传3张图片，大小不超过5MB，推荐图片尺寸比例4:3'}
                            ></ImgCropUpload>
                        </Form.Item>
                    </div>
                </Form.Item>
            )
            //             callback:{(res: any) => {
            //                         console.log('res12: ', res);
            // }}
        },
        {
            type: 'Custom',
            label: '宣传视频',
            value: 'productVideo',
            placeholder: '大小不超过50MB，支持mp4、webm、mov、m4v格式',
            children: (
                <Form.Item noStyle>
                    <div className={styles.prodPic}>
                        <Form.Item
                            name='productVideo'
                            rules={[
                                {
                                    required: false,
                                    message: '请上传'
                                }
                            ]}
                            extra={<div style={{ color: '#333' }}>大小不超过50MB，支持mp4、webm、mov、m4v格式</div>}
                            valuePropName='fileList'
                            getValueFromEvent={(e: any) => {
                                console.log(e);
                                if (e.fileList[0]?.response?.status == 200) {
                                    setUrl(e.fileList[0]?.response.fileUrl);
                                }
                                if (e.file?.status == 'removed') {
                                    setUrl(null);
                                }
                                if (Array.isArray(e)) {
                                    return e;
                                }
                                return e && e.fileList;
                            }}
                        >
                            <Upload
                                accept={productVideoAcceptTypes.join(',')}
                                customRequest={({ file, onError, onProgress, onSuccess }) => {
                                    // @ts-ignore
                                    fileUpload({
                                        ...{ file, onError, onProgress, onSuccess },
                                        isUploading: isUpLoading,
                                        setIsUpLoading: setIsUpLoading
                                    });
                                }}
                                beforeUpload={handleBeforeProductVideoUpload}
                                maxCount={1}
                            >
                                <Button icon={<UploadOutlined rev={undefined} />}>上传文件</Button>
                            </Upload>
                        </Form.Item>
                    </div>
                </Form.Item>
            )
        }
    ];

    const onFinish = (values: any) => {
        console.log('values123:', values);
        if (isUpLoading) {
            message.warning('正在上传文件请稍等～');
            return;
        }
        const params: any = {
            productName: values.productName,
            productCategory: values.productCategory.join('>'),
            expirationDate: values.expirationDate,
            productCode: values.productCode,
            executiveStandard: values.executiveStandard,
            specification: values.specification,
            specificationUnit: Number(unit),
            productionLicense: values.productionLicense,
            ingredient: values.ingredient,
            productAptitude: getFileUrlFormUploadedFile(values?.productAptitude)?.[0],
            productIntro: values.productIntro,
            productImg: getFileUrlFormUploadedFile(values?.productImg),
            productVideo: url ? url : null
        };

        const paramStr = JSON.stringify(params);
        signData(dispatch, JSON.stringify(params), (error, result: any) => {
            if (!error && result) {
                addfood.mutate({
                    addProductVo: params,
                    paramStr: paramStr,
                    signature: result
                });
            } else if (error !== 'misprivatekey') {
                message.info('签名异常，请重试或联系管理员');
            }
        });
    };
    return (
        <BaseCard title={<PageTitle title='新建产品' bg='container chan' />}>
            <PageTitle title='产品基础信息' type='primaryIcon' bmagin={16} />
            <Form
                onFinish={onFinish}
                className='edit-label-title'
                // form={search}
            >
                <FilterForm itemConfig={addBasicInfoConfigs} labelCol={3} wrapperCol={9} />

                <PageTitle title='产品简介' type='primaryIcon' bmagin={16} />

                <FilterForm itemConfig={addIntroductionConfigs} labelCol={3} wrapperCol={9} />
                <div className={styles.addBtnContainer}>
                    <Form.Item className={styles.saveBtn}>
                        <BaseButton type='primary' htmlType='submit' className={styles.submitBtn}>
                            保存
                        </BaseButton>
                    </Form.Item>
                    <Form.Item>
                        <BaseButton
                            htmlType='button'
                            type='dashed'
                            className={styles.primaryBtn}
                            onClick={() => {
                                navigate('/product-manage/food');
                            }}
                        >
                            取消
                        </BaseButton>
                    </Form.Item>
                </div>
            </Form>
        </BaseCard>
    );
};

export default FoodAdd;

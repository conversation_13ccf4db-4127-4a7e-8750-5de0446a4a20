export const menuIdMap: any = {
    101: '平台方账号管理',
    100: '溯源总览',
    2: '账号管理',
    102: '企业信息管理',
    103: '员工管理',
    104: '企业管理',
    105: '参与方管理',
    106: '产品管理',
    107: '产地管理',
    108: '原料管理',
    109: '原料采购管理',
    7: '生产管理',
    110: '生产过程管理',
    111: '生产加工管理',
    112: '装箱管理',
    9: '出入库管理',
    113: '入库管理',
    114: '出库管理',
    115: '质检管理',
    116: '物流管理',
    117: '溯源码包列表',
    118: '溯源码列表',
    13: '溯源数据管理',
    119: '原料溯源数据',
    120: '生产溯源数据',
    121: '质检溯源数据',
    122: '物流溯源数据',
    123: '系统日志',
    130: '种植管理',
    131: '种植溯源数据',
    142: '收购管理',
    143: '销售管理',
    144: '收购溯源数据',
    145: '销售溯源数据',
    147: '用户反馈',
    149: '仓储管理',
    150: '仓储溯源数据',



};

export const menuIdToUrl: any = {
    100: '/traceability-overview',
    101: '/coreflrmaccount',
    102: '/account/basicInfo',
    103: '/account/employees',
    105: '/parties/manage',
    106: '/product-manage/food',
    107: '/product-manage/origin',
    108: '/product-manage/source',
    109: '/raw/rawlist',
    110: '/product/product',
    111: '/product/process',
    112: '/box-code/rawlist',
    113: '/cpsjsr/putIn',
    114: '/cpsjsr/putOut',
    115: '/qcqa/list',
    130: '/qcqa/list',
    116: 'logistics-manage/list',
    117: '/code-manage/code-package',
    118: 'code-manage/code',
    119: '/source-data-manage/raw-material',
    120: '/source-data-manage/production',
    121: '/source-data-manage/inspect',
    122: '/source-data-manage/logistics',
    123: '/system-log',

};

export enum RoleEnum {
    '平台方',
    '生产加工企业',
    '供应商',
    '质检机构',
    '监管机构',
    '物流企业',
    '运营方',
    '农户',
    '销售企业',
}

export const LocalLoginIdentity = RoleEnum.平台方;

// 先初步通过 role 来确定，后续通过接口对接
export function getMenusByRole(role: number): number[] {
    const menus: number[] = [];
    switch (role) {
        case RoleEnum.平台方:
            menus.push(1, 9, 10, 20, 21, 16, 146);
            break;
        case RoleEnum.生产加工企业:
            menus.push(2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20,146);
            break;
        case RoleEnum.供应商:
            menus.push(3, 4, 11, 20);
            break;
        case RoleEnum.质检机构:
            menus.push(3, 4, 15, 20);
            break;
        case RoleEnum.监管机构:
            menus.push(3, 4, 9, 10, 15, 20);
            break;
        case RoleEnum.物流企业:
            menus.push(3, 4, 18, 20);
            break;
            case RoleEnum.销售企业:
              menus.push(8);
              break;
        default:
            break;
    }
    return menus;
}

export const roleIdentity = {
    0: '平台方',
    1: '生产加工企业',
    2: '供应商',
    3: '质检机构',
    4: '监管机构',
    5: '物流企业',
    6: '运营方',
    8: '销售企业',
}
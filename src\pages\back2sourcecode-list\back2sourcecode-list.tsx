/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-11-01 18:05:21
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';

import { useMutation, useQuery } from 'react-query';
import dayjs from 'dayjs';

import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space } from 'antd';
import useUrlState from '@ahooksjs/use-url-state';
import SingleSearch from '@components/single-search';
import styles from './index.module.less';
import { addTraceabilityConfigs, editEmployeesConfigs } from './config';
import SearchForm from '@components/search-form';
import { useRef, useState } from 'react';
import BaseModal from '@components/base-modal';
import FilterForm from '@components/filter-form';
import PageTitle from '@components/page-title';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { useAccountList } from '../../myhooks/useaccountlist';
import { useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import { traceSourceCodePage } from '@services/trace-source-code';
import { useLocation } from 'react-router-dom';
import { ReformChainError } from '@utils/errorCodeReform';
import { ColumnsType } from 'antd/lib/table';
const Back2SourceCode = (props: any) => {
    const { state } = useLocation();

    const { pageInfo, handlePaginationChange } = props;
    const navigate = useNavigate();
    const querylist: any = useRef(state?.name ? state?.name : null);
    const traceSourceCodepage = useQuery(
        ['userquery', pageInfo],
        () => {
            // if (!userInfo.user?.organization_id) {
            //     message.error('未获取到机构id');
            //     return Promise.reject('未获取到机构id');
            // }
            return traceSourceCodePage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                param: querylist?.current?.trim() || undefined
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    //列表数据
    const tableData = traceSourceCodepage?.data?.data?.records?.map((item: any) => ({
        code: item.code,
        packNumber: item.packNumber,
        createTime: dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss'),
        foodName: item.foodName,
        productionBatch: item.productionBatch,
        searchNumber: item.searchNumber,
        status: item.state,
        traceCodeId: item.traceCodeId
    }));
    const listColumn: ColumnsType<any> = [
        {
            title: '溯源码',
            dataIndex: 'code',
            key: 'code',
            ellipsis: true
        },
        {
            title: '所属码包',
            dataIndex: 'packNumber',
            key: 'packNumber',
            ellipsis: true
        },
        {
            title: '生码时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true
        },
        {
            title: '所属产品',
            dataIndex: 'foodName',
            key: 'foodName',
            ellipsis: true
        },
        {
            title: '生产批次',
            dataIndex: 'productionBatch',
            key: 'productionBatch',
            ellipsis: true
        },
        {
            title: '查询次数',
            dataIndex: 'searchNumber',
            key: 'searchNumber',
            ellipsis: true
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            ellipsis: true,
            render: (data: any) => (
                <span style={{ color: data ? '#F64041' : '#666666' }}>
                    <Badge status={data ? 'error' : 'success'} color={data ? '#F64041' : 'rgb(36, 171, 59)'} />
                    {data ? '禁用' : '可用'}
                </span>
            )
        },
        {
            width: 130,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='small'>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            navigate('code-detail', {
                                state: {
                                    id: record.traceCodeId
                                }
                            });
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    const searchConfig = {
        label: '',
        handleSearch: () => {
            handlePaginationChange(1);
            traceSourceCodepage.refetch();
        },
        placeholder: '输入溯源码搜索',
        setSearchValue: (values: any) => {
            // console.log('values', values);
            querylist.current = values;
        }
    };

    return (
        <>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
                title={<PageTitle title='溯源码列表' bg='container su' />}
            >
                <BaseTable
                    rowKey='account'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return <TableHead LeftDom={<div></div>} RightDom={<SingleSearch {...searchConfig} />} />;
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={traceSourceCodepage?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={traceSourceCodepage?.data?.data?.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>
        </>
    );
};

export default WithPaginate(Back2SourceCode);

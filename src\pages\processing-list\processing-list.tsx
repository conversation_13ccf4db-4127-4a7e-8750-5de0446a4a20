/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-09 16:17:21
 * @LastEditTime: 2024-10-10 15:21:45
 * @LastEditors: 吴山仁
 */
/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-10-09 16:16:29
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space, Card, Col, Row, DatePicker, Table } from 'antd';
import useUrlState from '@ahooksjs/use-url-state';
import dayjs from 'dayjs';
import { alidFoodList } from '@services/material';
import { productionPage, cancelProduction, cancelPurchase } from '@services/production';
import { useMutation, useQuery } from 'react-query';
import styles from './index.module.less';
import { useRef, useState } from 'react';
import PageTitle from '@components/page-title';
import { SearchOutlined, PlusOutlined, DownOutlined, UpOutlined, SyncOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { useAccountList } from '../../myhooks/useaccountlist';
import SingleSearch from '@components/single-search';
import { useNavigate } from 'react-router-dom';
import BaseModal from '@components/base-modal';
import FilterForm from '@components/filter-form';
import { ReformChainError } from '@utils/errorCodeReform';
import { addProductConfig } from './config';
import WithPaginate from '../../hoc/withpaginate';
import { ColumnsType } from 'antd/lib/table';
import BaseInput from '@components/base-input';
import BaseDatePicker from '@components/base-date-picker';
import BaseSelect from '@components/base-select';
import utc from 'dayjs/plugin/utc';
import { getProductSelectList } from '@services/food';

dayjs.extend(utc);

const { RangePicker } = DatePicker;

interface IUrlState {
    pageIndex: number;
    pageSize: number;
}

const ProductList = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;
    const navigate = useNavigate();
    const [modalVisible, setmodalVisible] = useState(false);
    const [addProductForm] = Form.useForm();
    const [productForm] = Form.useForm();
    const querylist = useRef<any>('');
    const [isSimpleSearch, setIsSimpleSearch] = useState(true);
    const [search]: any = Form.useForm();
    const [pageIndex, setPageIndex] = useState(1);
    const [pageSize, setPageSize] = useState(5);
    const [landSelect, setLandSelect] = useState<any>();
    //作废
    const matermodiy = useMutation(cancelProduction, {
        onSuccess(res) {
            message.success('作废成功');
            productionquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            productionquery.refetch();
        }
    });
    //分页数据
    const productionquery = useQuery(
        ['purchsequery', pageInfo],
        () => {
            return productionPage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                state: querylist?.current?.state,
                productId: querylist?.current?.productId,
                productionBatch: querylist?.current?.productionBatch?.trim() || undefined,
                startTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[0]).startOf('day')).format()
                    : undefined,
                endTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[1]).endOf('day')).format()
                    : undefined
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    //食品列表
    const alidfoodList = useQuery(
        ['alidFoodList', pageInfo],
        () => {
            return alidFoodList({
                valid: true
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            },
            enabled: !!modalVisible
        }
    );

    //产品数据
    const selectProductsListQuery = useQuery(
        ['selectProductsListQuery1'],
        () => {
            return getProductSelectList({
                valid: false
            });
        },
        {
            onSuccess(data) {
                console.log(data);
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );

    console.log('staffquery', productionquery);
    const tableData = productionquery?.data?.data?.records?.map((item: any) => ({
        productionBatch: item.productionBatch,
        productName: item.productName,
        createTime: dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss'),
        productionId: item.id,
        status: item.state,
        optName: item.optName
    }));
    const listColumn: ColumnsType<any> = [
        {
            title: '生产批次',
            dataIndex: 'productionBatch',
            key: 'productionBatch',
            ellipsis: true
        },
        {
            title: '产品名',
            dataIndex: 'productName',
            key: 'productName',
            ellipsis: true
        },

        {
            title: '操作人',
            dataIndex: 'optName',
            key: 'optName',
            ellipsis: true
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            width: 180,
            render: (data: any) => {
                const getStatusConfig = (status: number) => {
                    switch (status) {
                        case 0:
                            return {
                                status: 'success' as const,
                                color: 'rgb(36, 171, 59)',
                                text: '可用',
                                textColor: '#666666'
                            };
                        case 1:
                            return {
                                status: 'error' as const,
                                color: 'rgb(161, 158, 162)',
                                text: '已作废',
                                textColor: 'rgb(161, 158, 162)'
                            };
                        case 2:
                            return {
                                status: 'success' as const,
                                color: 'rgb(36, 171, 59)',
                                text: '已入库（本来生活）',
                                textColor: 'rgb(36, 171, 59)'
                            };
                        default:
                            return {
                                status: 'default' as const,
                                color: '#d9d9d9',
                                text: '-',
                                textColor: '#999999'
                            };
                    }
                };

                const config = getStatusConfig(data);
                return (
                    <span style={{ color: config.textColor }}>
                        <Badge status={config.status} color={config.color} text={config.text} />
                    </span>
                );
            }
        },
        {
            width: 200,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle'>
                    <BaseButton
                        danger
                        type='dashed'
                        className='warnBtn'
                        disabled={record.status}
                        onClick={() => {
                            console.log('record', record);
                            matermodiy.mutate({
                                id: record?.productionId
                            });
                        }}
                    >
                        作废
                    </BaseButton>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            navigate('/product/process/detail', {
                                state: {
                                    data: record
                                }
                            });
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    const mapToEnum: any = {};
    const mapToNumber: any = {};
    (alidfoodList?.data?.data || [])?.forEach((item: any, index: any) => {
        mapToEnum[item.id] = item.food_name;
        mapToNumber[item.id] = item.food_number;
    });
    const [addModalVisibleland, setAddModelVisibleland] = useState(false);
    const addProductConfigs = {
        title: '新建批次',
        visible: modalVisible,
        width: 1000,

        setVisible: setmodalVisible,
        okHandle: async () => {
            try {
                const values = await addProductForm.validateFields();
                const productValues = await productForm.validateFields();
                console.log(productValues);
                if (landSelect.length === 0) {
                    message.warning('请选择收购过程');
                } else {
                    navigate('/product/process/add', {
                        state: {
                            id: productValues.name,
                            food_name: mapToEnum[productValues.name],
                            food_number: mapToNumber[productValues.name],
                            purchase_data: landSelect
                        }
                    });
                    setmodalVisible(false);
                }
            } catch (e) {}
        },
        onCancelHandle: () => {
            productForm.resetFields();
            addProductForm.resetFields();
            setmodalVisible(false);
        }
    };
    //选择收购过程列表
    const purchaseQuery = useQuery(
        ['purchaseQuery', pageSize, pageIndex],
        async () => {
            const queryFormData = await addProductForm.validateFields();
            return cancelPurchase({
                pageIndex: pageIndex,
                pageSize: pageSize,
                purchaseBatch: queryFormData.purchaseBatch,
                startTime: queryFormData.createTime
                    ? dayjs.utc(dayjs(queryFormData.createTime[0]).startOf('day')).format()
                    : undefined,
                endTime: queryFormData.createTime
                    ? dayjs.utc(dayjs(queryFormData.createTime[1]).endOf('day')).format()
                    : undefined
            });
        },
        {
            onSuccess(res) {
                console.log('res: ', res);
            },
            onError(err: any) {
                ReformChainError(err);
            },
            enabled: !!addModalVisibleland
        }
    );
    const listColumnPurchase: ColumnsType<any> = [
        {
            title: '收购批次',
            dataIndex: 'purchaseBatch',
            key: 'purchaseBatch',
            ellipsis: true
        },

        {
            title: '收购时间',
            dataIndex: 'purchaseTime',
            key: 'purchaseTime',
            ellipsis: true,
            render: (time: any) => {
                return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
            }
        },
        {
            title: '农作物类型',
            dataIndex: 'plantName',
            key: 'plantName',
            ellipsis: true
        },
        {
            title: '收割时间',
            dataIndex: 'harvestTime',
            key: 'harvestTime',
            ellipsis: true,
            render: (time: any) => {
                return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
            }
        },

        {
            title: '加工数量(吨)',
            dataIndex: 'availablePurchaseWeight',
            key: 'availablePurchaseWeight',
            ellipsis: true
        }
    ];
    const addProductConfig = [
        {
            label: '产品名称',
            type: 'Select',
            value: 'name',
            placeholder: '请选择',
            name: 'name',
            rules: [{ required: true, message: '请选择产品名称!' }],
            fields: [
                ...(alidfoodList?.data?.data || [])?.map((item: any, index: any) => {
                    const materialdata = {
                        value: item.id,
                        label: item.productName
                    };
                    return materialdata;
                })
            ]
        }
    ];
    const searchFormItems = [
        <Form.Item label='批次号' name='productionBatch'>
            <BaseInput placeholder='请输入批次号'></BaseInput>
        </Form.Item>,
        <Form.Item label='产品名' name='productId'>
            <BaseSelect
                placeholder='请选择'
                options={selectProductsListQuery?.data?.data?.map((item: any) => {
                    return {
                        label: item?.productName,
                        value: item?.id
                    };
                })}
            ></BaseSelect>
        </Form.Item>,
        <Form.Item label='状态' name='state'>
            <BaseSelect
                placeholder='请选择'
                options={[
                    {
                        label: '可用',
                        value: 0
                    },
                    {
                        label: '已作废',
                        value: 1
                    },
                    {
                        label: '已入库（本来生活）',
                        value: 2
                    }
                ]}
            ></BaseSelect>
        </Form.Item>,
        <Form.Item label='创建时间' name='createTime'>
            <BaseDatePicker style={{ width: '100%' }}></BaseDatePicker>
        </Form.Item>
    ];

    const handleQueryModalTable = () => {
        purchaseQuery.refetch();
    };

    return (
        <>
            <Card title={<PageTitle title='生产批次列表' bg='container sheng' />} style={{ marginBottom: 10 }}>
                <Form
                    labelAlign='left'
                    form={search}
                    onFinish={(values) => {
                        console.log(values, 'values');
                        handlePaginationChange(1);
                        console.log('values', values);
                        querylist.current = values;
                        productionquery.refetch();
                    }}
                    className='label-title'
                >
                    <Row gutter={[36, 12]}>
                        {searchFormItems.slice(0, isSimpleSearch ? 2 : searchFormItems.length).map((searchFormItem) => (
                            <Col key={searchFormItem.key} span={8}>
                                {searchFormItem}
                            </Col>
                        ))}
                        <Col
                            span={
                                isSimpleSearch
                                    ? 8
                                    : searchFormItems.length % 3
                                    ? searchFormItems.length % 3 === 1
                                        ? 16
                                        : 8
                                    : 24
                            }
                        >
                            <div style={{ display: 'flex', justifyContent: 'end' }}>
                                <Space>
                                    <BaseButton
                                        htmlType='submit'
                                        type='primary'
                                        // className='searchBtn'
                                        style={{ width: 100 }}
                                        className={`${styles.searchBtn} ${styles.baseBtn}`}
                                        icon={<SearchOutlined rev={undefined} />}
                                    >
                                        查询
                                    </BaseButton>
                                    <BaseButton
                                        type='dashed'
                                        className='primaryBtn'
                                        style={{ width: 100 }}
                                        icon={<SyncOutlined rev={undefined} />}
                                        onClick={() => {
                                            querylist.current = null;
                                            productionquery.refetch();
                                            search.resetFields();
                                        }}
                                    >
                                        重置
                                    </BaseButton>
                                    <BaseButton
                                        type='link'
                                        style={{ color: '#80a932' }}
                                        onClick={() => {
                                            setIsSimpleSearch(!isSimpleSearch);
                                        }}
                                    >
                                        {isSimpleSearch ? '展开' : '收起'}
                                        {isSimpleSearch ? (
                                            <DownOutlined rev={undefined} />
                                        ) : (
                                            <UpOutlined rev={undefined} />
                                        )}
                                    </BaseButton>
                                </Space>
                            </div>
                        </Col>
                    </Row>
                </Form>
                {/*</div>*/}
            </Card>
            <BaseCard
                // className="coreFIrmContainer"
                className={styles.coreFIrmContainer}
                // mt24
            >
                <BaseTable
                    rowKey='account'
                    btnDisplay={(checkData: any, resetSelect: any) => {
                        return (
                            <TableHead
                                LeftDom={<div></div>}
                                RightDom={
                                    <div
                                        style={{
                                            display: 'flex',
                                            marginBottom: '21px'
                                        }}
                                    >
                                        <BaseButton
                                            type='dashed'
                                            icon={<PlusOutlined rev={undefined} />}
                                            className='bgBtn'
                                            onClick={() => {
                                                setmodalVisible(true);
                                                setAddModelVisibleland(true);
                                                setLandSelect([]);
                                                setPageIndex(1);
                                                purchaseQuery.refetch();
                                            }}
                                        >
                                            新建批次
                                        </BaseButton>
                                    </div>
                                }
                            />
                        );
                    }}
                    columns={listColumn}
                    dataSource={tableData}
                    loading={productionquery?.isLoading}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={productionquery?.data?.data.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>

            <BaseModal {...addProductConfigs}>
                <Form form={productForm}>
                    <Row gutter={[36, 15]}>
                        <Col span={8}>{<FilterForm itemConfig={addProductConfig} />}</Col>
                    </Row>
                </Form>
                <div className={styles.titleP}>收购过程</div>
                <Form
                    name='addProductForm'
                    form={addProductForm}
                    onFinish={handleQueryModalTable}
                    className='label-title'
                >
                    <Row gutter={[36, 12]}>
                        <Col span={8}>
                            {/* <Form.Item label='地块名称' name='landName'>
                                        <BaseInput placeholder='请输入地块名称'></BaseInput>
                                    </Form.Item> */}
                            <Form.Item label='收购批次' name='purchaseBatch'>
                                <BaseInput placeholder='请输入收购批次'></BaseInput>
                                {/* <BaseSelect
                                            placeholder='请选择收购批次'
                                            options={landProductListData?.map((item) => ({
                                                label: item?.landName,
                                                value: item?.landId
                                            }))}
                                        ></BaseSelect> */}
                            </Form.Item>
                        </Col>

                        <Col span={8}>
                            <Form.Item label='收购时间' name='createTime'>
                                {/* <BaseSelect
                                            placeholder='请选择农作物类型'
                                            options={typeProductListData?.map((item) => ({
                                                label: item?.plantName,
                                                value: item?.plantName
                                            }))}
                                        ></BaseSelect> */}
                                <RangePicker
                                    style={{ width: '100%' }}
                                    getPopupContainer={(trigger: any) => trigger.parentNode}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={8}>
                            <BaseButton
                                type='primary'
                                htmlType='submit'
                                style={{ width: 100 }}
                                // className='searchBtn'
                                className={`${styles.searchBtn} ${styles.baseBtn}`}
                                icon={<SearchOutlined rev={undefined} />}
                            >
                                查询
                            </BaseButton>
                        </Col>
                    </Row>
                </Form>
                <Table
                    rowKey='id'
                    rowSelection={{
                        type: 'radio',
                        onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
                            const selectedRow = selectedRows.find((row) => row.id === selectedRowKeys[0]);
                            console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
                            setLandSelect(selectedRows);
                            // text === setLandSelect(selectedRows);
                            // text === '选择生产过程' ? setAvailableNums('') : setAvailableNums(selectedRow.availableNum);
                        },
                        getCheckboxProps: (record) => ({
                            // disabled: record.availableNum == '0' || record.state == '1', // Column configuration not to be checked
                            // availableNum: record.availableNum

                            disabled: record.availablePurchaseWeight == '0' || record.state == '1', // Column configuration not to be checked
                            availablePurchaseWeight: record.availablePurchaseWeight
                        })
                    }}
                    columns={listColumnPurchase}
                    dataSource={purchaseQuery?.data?.data?.records}
                    loading={purchaseQuery?.isLoading}
                    pagination={false}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    // showSizeChanger
                    current={pageIndex}
                    pageSize={pageSize}
                    total={purchaseQuery?.data?.data?.total}
                    // onShowSizeChange={handlePaginationChange}
                    onChange={(index, pagesize) => {
                        console.log(index, pagesize);
                        setPageIndex(index);
                        setPageSize(pagesize);
                    }}
                />
            </BaseModal>
        </>
    );
};

export default WithPaginate(ProductList);

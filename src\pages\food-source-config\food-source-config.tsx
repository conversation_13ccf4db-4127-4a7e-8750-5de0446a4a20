/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 14:00:32
 * @LastEditTime: 2022-10-10 17:09:06
 * @LastEditors: PhilRandWu
 */
import './index.less';
import BaseCard from '@components/base-card';
import PageTitle from '@components/page-title';
import { useEffect, useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import BaseButton from '@components/base-button';
import { Form, message, Button, Card, Switch, Row, Col, Collapse, Spin } from 'antd';
import { useLocation } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';
import {
    sourceConfig,
    materialList,
    foodDetail,
    getProductConfigOptions,
    getProductConfigs,
    configSourceCode
} from '@services/food';
import { ReformChainError } from '@utils/errorCodeReform';
import { selectParticipantDetail } from '@services/company';
import { useAppDispatch, useAppSelector } from '@store';

export default function FoodSourceConfig() {
    const navigate = useNavigate();
    const { state } = useLocation();
    const userInfo = useAppSelector((store) => store.user);

    const [editEmployeesForm] = Form.useForm();
    const [productForm] = Form.useForm();
    const [placeForm] = Form.useForm();
    const [productionForm] = Form.useForm();
    const [inspectionForm] = Form.useForm();
    const [logisticsForm] = Form.useForm();
    const [materialForm] = Form.useForm();
    const [orgForm] = Form.useForm();
    const [landForm] = Form.useForm();
    const [purchaseForm] = Form.useForm();
    const [warehouseForm] = Form.useForm();
    const [activeModel, setActiveModel] = useState<any>([]);
    const [loading, setLoading] = useState<any>(false);

    useEffect(() => {
        setLoading(true);
    }, []);

    useEffect(() => {
        console.log(activeModel);
    }, [loading]);

    //产品可配置项
    const productConfigOptionsQuery: any = useQuery(
        [' productConfigOptionsQuery'],
        () => {
            return getProductConfigOptions();
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    console.log(productConfigOptionsQuery);
    //初始展开项
    const initExpandModel = (obj: any) => {
        let array: any = [];
        for (let key in obj) {
            const value = obj[key];
            console.log(value);
            const flagItem = value?.filter((item: any) => {
                return item?.name?.toLowerCase() === key?.toLowerCase();
            })[0];
            const isShow = flagItem?.isShow;
            if (isShow) {
                array.push(flagItem?.name);
            }
        }
        return array;
    };

    //产品当前配置项
    const productConfigsQuery: any = useQuery(
        [' productConfigsQuery'],
        () => {
            return getProductConfigs({
                productId: state?.id
            });
        },
        {
            onSuccess(res: any) {
                console.log(res);
                setLoading(false);
                const activeTab = initExpandModel(res.data);
                console.log(activeTab);
                setActiveModel([...activeTab, 'PRODUCT']);
                productForm.setFieldsValue(
                    res?.data?.product?.reduce((obj: any, item: any) => {
                        obj[item.name] = item?.isShow;
                        return obj;
                    }, {})
                );
                inspectionForm.setFieldsValue(
                    res?.data?.inspection?.reduce((obj: any, item: any) => {
                        obj[item.name] = item?.isShow;
                        return obj;
                    }, {})
                );
                logisticsForm.setFieldsValue(
                    res?.data?.logistics?.reduce((obj: any, item: any) => {
                        obj[item.name] = item?.isShow;
                        return obj;
                    }, {})
                );
                materialForm.setFieldsValue(
                    res?.data?.material?.reduce((obj: any, item: any) => {
                        obj[item.name] = item?.isShow;
                        return obj;
                    }, {})
                );
                orgForm.setFieldsValue(
                    res?.data?.org?.reduce((obj: any, item: any) => {
                        obj[item.name] = item?.isShow;
                        return obj;
                    }, {})
                );
                placeForm.setFieldsValue(
                    res?.data?.place?.reduce((obj: any, item: any) => {
                        obj[item.name] = item?.isShow;
                        return obj;
                    }, {})
                );
                productionForm.setFieldsValue(
                    res?.data?.production?.reduce((obj: any, item: any) => {
                        obj[item.name] = item?.isShow;
                        return obj;
                    }, {})
                );
                landForm.setFieldsValue(
                    res?.data?.plant?.reduce((obj: any, item: any) => {
                        obj[item.name] = item?.isShow;
                        return obj;
                    }, {})
                );
                purchaseForm.setFieldsValue(
                    res?.data?.purchase?.reduce((obj: any, item: any) => {
                        obj[item.name] = item?.isShow;
                        return obj;
                    }, {})
                );
                warehouseForm.setFieldsValue(
                    res?.data?.warehouse?.reduce((obj: any, item: any) => {
                        obj[item.name] = item?.isShow;
                        return obj;
                    }, {})
                );
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );

    const foodDetaiQuery = useQuery(
        ['foodDetaiQuery'],
        () => {
            return foodDetail({
                productId: state?.id
            });
        },
        {
            onSuccess(res) {},
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );

    const companyInfoQuery = useQuery(
        [' companyInfoQuery'],
        () => {
            return selectParticipantDetail({
                orgId: userInfo.userInfo.orgId
            });
        },
        {
            onSuccess(res) {},
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );

    const configCode = useMutation(configSourceCode, {
        onSuccess(res) {
            message.success('配置溯源码成功');
            navigate('/product-manage/food');
            // setLoading(true);
            // productConfigOptionsQuery.refetch();
            // productConfigsQuery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });

    return (
        <div className='sourceConfigContainer'>
            {loading ? (
                <Spin spinning={loading}></Spin>
            ) : (
                <BaseCard
                    title={<PageTitle title='配置溯源码信息' bg='container chan' />}
                    style={{ maxHeight: 700, overflow: 'auto' }}
                >
                    <Form
                        onFinish={(values) => {
                            console.log(values);
                        }}
                        form={editEmployeesForm}
                        // onFieldsChange={onFieldsChange}
                    >
                        <Collapse activeKey={activeModel}>
                            {productConfigOptionsQuery?.data?.data?.product && (
                                <Collapse.Panel
                                    collapsible='header'
                                    header={<div style={{ fontSize: 16, marginLeft: 10, width: 100 }}>产品信息</div>}
                                    showArrow={false}
                                    key='PRODUCT'
                                    style={{ background: '#f2f8f2' }}
                                    extra={<Switch defaultChecked disabled />}
                                >
                                    <Form form={productForm} labelCol={{ span: 7 }} className='label-title'>
                                        <Row>
                                            <Col span={8}>
                                                <Form.Item label='产品名称' name='PRODUCT_NAME' valuePropName='checked'>
                                                    <Switch defaultChecked checked={true} disabled />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='产品品类'
                                                    name='PRODUCT_CATEGORY'
                                                    valuePropName='checked'
                                                >
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !foodDetaiQuery?.data?.data?.productCategory) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='保质期'
                                                    name='EXPIRATION_DATE'
                                                    valuePropName='checked'
                                                >
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !foodDetaiQuery?.data?.data?.expirationDate) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            {/* <Col span={8}>
                                                <Form.Item label='产品编码' name='PRODUCT_CODE' valuePropName='checked'>
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !foodDetaiQuery?.data?.data?.productCode) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col> */}
                                            <Col span={8}>
                                                <Form.Item
                                                    label='产品执行标准'
                                                    name='EXECUTIVE_STANDARD'
                                                    valuePropName='checked'
                                                >
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (
                                                                value &&
                                                                !foodDetaiQuery?.data?.data?.executiveStandard
                                                            ) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='食品生产许可证编号'
                                                    name='PRODUCTION_LICENSE'
                                                    valuePropName='checked'
                                                >
                                                    <Switch defaultChecked checked={true} disabled />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='配料' name='INGREDIENT' valuePropName='checked'>
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !foodDetaiQuery?.data?.data?.ingredient) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='产品规格'
                                                    name='PRODUCT_SPECIFICATION'
                                                    valuePropName='checked'
                                                >
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !foodDetaiQuery?.data?.data?.specification) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            {/* <Col span={8}>
                                                <Form.Item
                                                    label='产品合格证明'
                                                    name='PRODUCT_APTITUDE'
                                                    valuePropName='checked'
                                                >
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !foodDetaiQuery?.data?.data?.productAptitude) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col> */}
                                            <Col span={8}>
                                                <Form.Item
                                                    label='产品介绍'
                                                    name='PRODUCT_INTRO'
                                                    valuePropName='checked'
                                                >
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !foodDetaiQuery?.data?.data?.productIntro) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='宣传图片' name='PRODUCT_IMG' valuePropName='checked'>
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !foodDetaiQuery?.data?.data?.productImg) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='宣传视频'
                                                    name='PRODUCT_VIDEO'
                                                    valuePropName='checked'
                                                >
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !foodDetaiQuery?.data?.data?.productVideo) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                        </Row>
                                    </Form>
                                </Collapse.Panel>
                            )}
                            {productConfigOptionsQuery?.data?.data?.place && (
                                <Collapse.Panel
                                    collapsible='header'
                                    header={<div style={{ fontSize: 16, marginLeft: 10, width: 100 }}>产地信息</div>}
                                    showArrow={false}
                                    key='PLACE'
                                    style={{ background: '#f2f8f2' }}
                                    extra={
                                        <Switch
                                            // checked={Object.values(placeForm.getFieldsValue()).includes(true)}
                                            defaultChecked={
                                                !!productConfigsQuery?.data?.data?.place?.filter(
                                                    (item: any) => item.name === 'PLACE'
                                                )[0]?.isShow
                                            }
                                            onChange={(value) => {
                                                if (!value) {
                                                    setActiveModel(activeModel.filter((item: any) => item !== 'PLACE'));
                                                    placeForm.resetFields();
                                                } else {
                                                    setActiveModel([...activeModel, 'PLACE']);
                                                }
                                            }}
                                        />
                                    }
                                >
                                    <Form className='label-title' form={placeForm} labelCol={{ span: 7 }}>
                                        <Row>
                                            <Col span={8}>
                                                <Form.Item label='产地名称' name='PLACE_NAME' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='产地地址'
                                                    name='PLACE_ADDRESS'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='产地位置图'
                                                    name='PLACE_APTITUDE'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='产地介绍' name='PLACE_INTRO' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='产地图片' name='PLACE_IMG' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='产地视频' name='PLACE_VIDEO' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                        </Row>
                                    </Form>
                                </Collapse.Panel>
                            )}
                            {productConfigOptionsQuery?.data?.data?.material && (
                                <Collapse.Panel
                                    collapsible='header'
                                    header={<div style={{ fontSize: 16, marginLeft: 10, width: 100 }}>原料详情</div>}
                                    showArrow={false}
                                    key='MATERIAL'
                                    style={{ background: '#f2f8f2' }}
                                    extra={
                                        <Switch
                                            defaultChecked={
                                                productConfigsQuery?.data?.data?.material?.filter(
                                                    (item: any) => item.name === 'MATERIAL'
                                                )[0]?.isShow
                                            }
                                            onChange={(value) => {
                                                if (!value) {
                                                    setActiveModel(
                                                        activeModel.filter((item: any) => item !== 'MATERIAL')
                                                    );
                                                    materialForm.resetFields();
                                                } else {
                                                    setActiveModel([...activeModel, 'MATERIAL']);
                                                }
                                            }}
                                        />
                                    }
                                >
                                    <Form className='label-title' form={materialForm} labelCol={{ span: 7 }}>
                                        <Row>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='原料名称'
                                                    name='MATERIAL_NAME'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='原料采购批次'
                                                    name='PURCHASE_BATCH'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='生产日期'
                                                    name='PRODUCTION_DATE'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='保质期' name='EXPIRATION' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='数量' name='COUNT' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='规格' name='SPECIFICATION' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='原料合格证明'
                                                    name='CERTIFICATE'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='原料图片' name='MATERIAL_IMG' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='供应商' name='SUPPLIER' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                        </Row>
                                    </Form>
                                </Collapse.Panel>
                            )}
                            {productConfigOptionsQuery?.data?.data?.production && (
                                <Collapse.Panel
                                    collapsible='header'
                                    header={
                                        <div style={{ fontSize: 16, marginLeft: 10, width: 100 }}>生产加工详情</div>
                                    }
                                    showArrow={false}
                                    key='PRODUCTION'
                                    style={{ background: '#f2f8f2' }}
                                    extra={
                                        <Switch
                                            defaultChecked={
                                                productConfigsQuery?.data?.data?.production?.filter(
                                                    (item: any) => item.name === 'PRODUCTION'
                                                )[0]?.isShow
                                            }
                                            onChange={(value) => {
                                                if (!value) {
                                                    setActiveModel(
                                                        activeModel.filter((item: any) => item !== 'PRODUCTION')
                                                    );
                                                    productionForm.resetFields();
                                                } else {
                                                    setActiveModel([...activeModel, 'PRODUCTION']);
                                                }
                                            }}
                                        />
                                    }
                                >
                                    <Form className='label-title' form={productionForm} labelCol={{ span: 7 }}>
                                        <Row>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='批次号'
                                                    name='PRODUCTION_BATCH'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='数量' name='AMOUNT' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='生产线' name='LINE' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='种植户' name='GROWER' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='生产过程名称'
                                                    name='PROCESS_NAME'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='生产过程说明'
                                                    name='PROCESS_INSTRUCTIONS'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='生产过程图片'
                                                    name='PROCESS_IMG'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='生产过程视频'
                                                    name='PROCESS_VIDEO'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                        </Row>
                                    </Form>
                                </Collapse.Panel>
                            )}
                            {productConfigOptionsQuery?.data?.data?.logistics && (
                                <Collapse.Panel
                                    collapsible='header'
                                    header={<div style={{ fontSize: 16, marginLeft: 10, width: 100 }}>物流详情</div>}
                                    showArrow={false}
                                    key='LOGISTICS'
                                    style={{ background: '#d7edf7' }}
                                    extra={
                                        <Switch
                                            defaultChecked={
                                                productConfigsQuery?.data?.data?.logistics?.filter(
                                                    (item: any) => item.name === 'LOGISTICS'
                                                )[0]?.isShow
                                            }
                                            onChange={(value) => {
                                                if (!value) {
                                                    setActiveModel(
                                                        activeModel.filter((item: any) => item !== 'LOGISTICS')
                                                    );
                                                    logisticsForm.resetFields();
                                                } else {
                                                    setActiveModel([...activeModel, 'LOGISTICS']);
                                                }
                                            }}
                                        />
                                    }
                                >
                                    <Form className='label-title' form={logisticsForm} labelCol={{ span: 7 }}>
                                        <Row>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='装货地点'
                                                    name='LOADING_LOCATION'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='卸货地点'
                                                    name='UNLOADING_LOCATION'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='运输方式' name='TYPE' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='物流企业' name='ENTERPRISES' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='物流单号' name='NUMBER' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                        </Row>
                                    </Form>
                                </Collapse.Panel>
                            )}
                            {productConfigOptionsQuery?.data?.data?.inspection && (
                                <Collapse.Panel
                                    collapsible='header'
                                    header={<div style={{ fontSize: 16, marginLeft: 10, width: 100 }}>质检详情</div>}
                                    showArrow={false}
                                    key='INSPECTION'
                                    style={{ background: '#f2f8f2' }}
                                    extra={
                                        <Switch
                                            defaultChecked={
                                                productConfigsQuery?.data?.data?.inspection?.filter(
                                                    (item: any) => item.name === 'INSPECTION'
                                                )[0]?.isShow
                                            }
                                            onChange={(value) => {
                                                console.log(value);
                                                if (!value) {
                                                    setActiveModel(
                                                        activeModel.filter((item: any) => item !== 'INSPECTION')
                                                    );
                                                    inspectionForm.resetFields();
                                                } else {
                                                    setActiveModel([...activeModel, 'INSPECTION']);
                                                }
                                            }}
                                        />
                                    }
                                >
                                    <Form className='label-title' form={inspectionForm} labelCol={{ span: 7 }}>
                                        <Row>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='质检内容'
                                                    name='INSPECTION_CONTENT'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='质检结果'
                                                    name='INSPECTION_RESULTS'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='质检报告'
                                                    name='INSPECTION_REPORT'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='质检机构'
                                                    name='INSPECTION_INSTITUTION'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                        </Row>
                                    </Form>
                                </Collapse.Panel>
                            )}
                            {productConfigOptionsQuery?.data?.data?.org && (
                                <Collapse.Panel
                                    collapsible='header'
                                    header={
                                        <div style={{ fontSize: 16, marginLeft: 10, width: 100 }}>企业基础信息</div>
                                    }
                                    showArrow={false}
                                    key='ORG'
                                    style={{ background: '#f2f8f2' }}
                                    extra={
                                        <Switch
                                            defaultChecked={
                                                productConfigsQuery?.data?.data?.org?.filter(
                                                    (item: any) => item.name === 'ORG'
                                                )[0]?.isShow
                                            }
                                            onChange={(value) => {
                                                if (!value) {
                                                    setActiveModel(activeModel.filter((item: any) => item !== 'ORG'));
                                                    orgForm.resetFields();
                                                } else {
                                                    setActiveModel([...activeModel, 'ORG']);
                                                }
                                            }}
                                        />
                                    }
                                >
                                    <Form className='label-title' form={orgForm} labelCol={{ span: 8 }}>
                                        <Row>
                                            <Col span={8}>
                                                <Form.Item label='企业名称' name='COMPANY_NAME' valuePropName='checked'>
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !companyInfoQuery?.data?.data?.companyName) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='企业地址' name='ADDRESS' valuePropName='checked'>
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !companyInfoQuery?.data?.data?.address) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='统一社会信用代码'
                                                    name='CREDIT_CODE'
                                                    valuePropName='checked'
                                                >
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !companyInfoQuery?.data?.data?.creditCode) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='企业资质'
                                                    name='QUALIFICATION'
                                                    valuePropName='checked'
                                                >
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !companyInfoQuery?.data?.data?.qualification) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='企业介绍' name='INTRODUCE' valuePropName='checked'>
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !companyInfoQuery?.data?.data?.introduce) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='企业图片' name='PICTURE' valuePropName='checked'>
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !companyInfoQuery?.data?.data?.picture) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='企业视频' name='VIDEO' valuePropName='checked'>
                                                    <Switch
                                                        onChange={(value) => {
                                                            if (value && !companyInfoQuery?.data?.data?.video) {
                                                                message.warning('该信息尚未填写，请及时填写');
                                                            }
                                                        }}
                                                    />
                                                </Form.Item>
                                            </Col>
                                        </Row>
                                    </Form>
                                </Collapse.Panel>
                            )}

                            {productConfigOptionsQuery?.data?.data?.plant && (
                                <Collapse.Panel
                                    collapsible='header'
                                    header={<div style={{ fontSize: 16, marginLeft: 10, width: 100 }}>种植信息</div>}
                                    showArrow={false}
                                    key='PLANT'
                                    style={{ background: '#f2f8f2' }}
                                    extra={
                                        <Switch
                                            defaultChecked={
                                                productConfigsQuery?.data?.data?.plant?.filter(
                                                    (item: any) => item.name === 'PLANT'
                                                )[0]?.isShow
                                            }
                                            onChange={(value) => {
                                                console.log(value);
                                                if (!value) {
                                                    setActiveModel(activeModel.filter((item: any) => item !== 'PLANT'));
                                                    landForm.resetFields();
                                                } else {
                                                    setActiveModel([...activeModel, 'PLANT']);
                                                }
                                            }}
                                        />
                                    }
                                >
                                    <Form className='label-title' form={landForm} labelCol={{ span: 7 }}>
                                        <Row>
                                            <Col span={8}>
                                                <Form.Item label='农作物类型' name='PLANT_NAME' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='播种时间' name='SOW_TIME' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='收割时间' name='HARVEST_TIME' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='种植地块' name='LAND_NAME' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='地块产量' name='HARVEST_NUM' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                        </Row>
                                    </Form>
                                </Collapse.Panel>
                            )}

                            {/* 收购信息 */}
                            {productConfigOptionsQuery?.data?.data?.plant && (
                                <Collapse.Panel
                                    collapsible='header'
                                    header={<div style={{ fontSize: 16, marginLeft: 10, width: 100 }}>收购信息</div>}
                                    showArrow={false}
                                    key='PURCHASE'
                                    style={{ background: '#f2f8f2' }}
                                    extra={
                                        <Switch
                                            defaultChecked={
                                                productConfigsQuery?.data?.data?.purchase?.filter(
                                                    (item: any) => item.name === 'PURCHASE'
                                                )[0]?.isShow
                                            }
                                            onChange={(value) => {
                                                console.log(value);
                                                if (!value) {
                                                    setActiveModel(
                                                        activeModel.filter((item: any) => item !== 'PURCHASE')
                                                    );
                                                    purchaseForm.resetFields();
                                                } else {
                                                    setActiveModel([...activeModel, 'PURCHASE']);
                                                }
                                            }}
                                        />
                                    }
                                >
                                    <Form className='label-title' form={purchaseForm} labelCol={{ span: 7 }}>
                                        <Row>
                                            <Col span={8}>
                                                <Form.Item label='农户姓名' name='FARMER_NAME' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item label='收购方' name='ACQUIRING_FIRM' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='收购商品'
                                                    name='PURCHASE_GOODS'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='收购重量'
                                                    name='PURCHASE_WEIGHT'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='收购时间'
                                                    name='PURCHASE_TIME'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                        </Row>
                                    </Form>
                                </Collapse.Panel>
                            )}

                            {/* 仓储信息 */}
                            {productConfigOptionsQuery?.data?.data?.warehouse && (
                                <Collapse.Panel
                                    collapsible='header'
                                    header={<div style={{ fontSize: 16, marginLeft: 10, width: 100 }}>仓储信息</div>}
                                    showArrow={false}
                                    key='WAREHOUSE'
                                    style={{ background: '#f2f8f2' }}
                                    extra={
                                        <Switch
                                            defaultChecked={
                                                productConfigsQuery?.data?.data?.warehouse?.filter(
                                                    (item: any) => item.name === 'WAREHOUSE'
                                                )[0]?.isShow
                                            }
                                            onChange={(value) => {
                                                console.log(value);
                                                if (!value) {
                                                    setActiveModel(
                                                        activeModel.filter((item: any) => item !== 'WAREHOUSE')
                                                    );
                                                    warehouseForm.resetFields();
                                                } else {
                                                    setActiveModel([...activeModel, 'WAREHOUSE']);
                                                }
                                            }}
                                        />
                                    }
                                >
                                    <Form className='label-title' form={warehouseForm} labelCol={{ span: 7 }}>
                                        <Row>
                                            <Col span={8}>
                                                <Form.Item label='入库时间' name='INBOUND_TIME' valuePropName='checked'>
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                            <Col span={8}>
                                                <Form.Item
                                                    label='仓库名称'
                                                    name='WAREHOUSE_NAME'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>

                                            <Col span={8}>
                                                <Form.Item
                                                    label='仓库地址'
                                                    name='WAREHOUSE_ADDRESS'
                                                    valuePropName='checked'
                                                >
                                                    <Switch />
                                                </Form.Item>
                                            </Col>
                                        </Row>
                                    </Form>
                                </Collapse.Panel>
                            )}
                        </Collapse>
                    </Form>
                </BaseCard>
            )}
            {/* <Card style={{ position: 'sticky', bottom: 0, background: '#fff' }}> */}
            <Card>
                <div className='addBtnContainer'>
                    <Form.Item className='saveBtn'>
                        <BaseButton
                            type='primary'
                            htmlType='submit'
                            className='submitBtn'
                            onClick={() => {
                                configCode.mutate({
                                    productId: state?.id,
                                    inspection: [
                                        ...Object.entries(inspectionForm.getFieldsValue()).map(([key, value]) => {
                                            return {
                                                name: key,
                                                isShow: value === true ? true : false
                                            };
                                        }),
                                        {
                                            isShow: Object.values(inspectionForm.getFieldsValue()).includes(true)
                                                ? true
                                                : false,
                                            name: 'INSPECTION'
                                        }
                                    ],
                                    logistics: [
                                        ...Object.entries(logisticsForm.getFieldsValue()).map(([key, value]) => {
                                            return {
                                                name: key,
                                                isShow: value === true ? true : false
                                            };
                                        }),
                                        {
                                            isShow: Object.values(logisticsForm.getFieldsValue()).includes(true)
                                                ? true
                                                : false,
                                            name: 'LOGISTICS'
                                        }
                                    ],
                                    material: [
                                        ...Object.entries(materialForm.getFieldsValue()).map(([key, value]) => {
                                            return {
                                                name: key,
                                                isShow: value === true ? true : false
                                            };
                                        }),
                                        {
                                            isShow: Object.values(materialForm.getFieldsValue()).includes(true)
                                                ? true
                                                : false,
                                            name: 'MATERIAL'
                                        }
                                    ],
                                    org: [
                                        ...Object.entries(orgForm.getFieldsValue()).map(([key, value]) => {
                                            return {
                                                name: key,
                                                isShow: value === true ? true : false
                                            };
                                        }),
                                        {
                                            isShow: Object.values(orgForm.getFieldsValue()).includes(true)
                                                ? true
                                                : false,
                                            name: 'ORG'
                                        }
                                    ],
                                    place: [
                                        ...Object.entries(placeForm.getFieldsValue()).map(([key, value]) => {
                                            return {
                                                name: key,
                                                isShow: value === true ? true : false
                                            };
                                        }),
                                        {
                                            isShow: Object.values(placeForm.getFieldsValue()).includes(true)
                                                ? true
                                                : false,
                                            name: 'PLACE'
                                        }
                                    ],
                                    product: [
                                        ...Object.entries(productForm.getFieldsValue()).map(([key, value]) => {
                                            if (key === 'PRODUCT_NAME') {
                                                return {
                                                    isShow: true,
                                                    name: 'PRODUCT_NAME'
                                                };
                                            }
                                            if (key === 'PRODUCTION_LICENSE') {
                                                return {
                                                    isShow: true,
                                                    name: 'PRODUCTION_LICENSE'
                                                };
                                            }
                                            return {
                                                name: key,
                                                isShow: value === true ? true : false
                                            };
                                        }),
                                        {
                                            isShow: true,
                                            name: 'PRODUCT'
                                        },
                                        {
                                            isShow: true,
                                            name: 'PRODUCTION_LICENSE'
                                        }
                                    ],
                                    production: [
                                        ...Object.entries(productionForm.getFieldsValue()).map(([key, value]) => {
                                            return {
                                                name: key,
                                                isShow: value === true ? true : false
                                            };
                                        }),
                                        {
                                            isShow: Object.values(productionForm.getFieldsValue()).includes(true)
                                                ? true
                                                : false,
                                            name: 'PRODUCTION'
                                        }
                                    ],
                                    plant: [
                                        ...Object.entries(landForm.getFieldsValue()).map(([key, value]) => {
                                            return {
                                                name: key,
                                                isShow: value === true ? true : false
                                            };
                                        }),
                                        {
                                            isShow: Object.values(landForm.getFieldsValue()).includes(true)
                                                ? true
                                                : false,
                                            name: 'PLANT'
                                        }
                                    ],
                                    purchase: [
                                        ...Object.entries(purchaseForm.getFieldsValue()).map(([key, value]) => {
                                            return {
                                                name: key,
                                                isShow: value === true ? true : false
                                            };
                                        }),
                                        {
                                            isShow: Object.values(purchaseForm.getFieldsValue()).includes(true)
                                                ? true
                                                : false,
                                            name: 'PURCHASE'
                                        }
                                    ],
                                    warehouse: [
                                        ...Object.entries(warehouseForm.getFieldsValue()).map(([key, value]) => {
                                            return {
                                                name: key,
                                                isShow: value === true ? true : false
                                            };
                                        }),
                                        {
                                            isShow: Object.values(warehouseForm.getFieldsValue()).includes(true)
                                                ? true
                                                : false,
                                            name: 'WAREHOUSE'
                                        }
                                    ]
                                });
                            }}
                        >
                            保存
                        </BaseButton>
                    </Form.Item>
                    <Form.Item>
                        <BaseButton
                            htmlType='button'
                            type='dashed'
                            className='primaryBtn'
                            onClick={() => {
                                navigate('/product-manage/food');
                            }}
                        >
                            取消
                        </BaseButton>
                    </Form.Item>
                </div>
            </Card>
        </div>
    );
}

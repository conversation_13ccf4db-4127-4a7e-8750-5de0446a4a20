/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-08 09:47:50
 * @LastEditTime: 2022-10-14 11:09:44
 * @LastEditors: PhilRandWu
 */
import { useEffect, useState } from 'react';
// import { requestCoreFlrmList } from '../mock/core-firm';

interface searchConditionInterFace {
    pageIndex?: number;
    pageSize?: number;
}

export const useCormFlrmList = function ({ pageIndex, pageSize }: searchConditionInterFace) {
    const [coreFlrmList, setCoreFlrmList] = useState([]);
    const [loading, setloading] = useState(true);
    useEffect(() => {
        (async () => {
            // const data: any = await requestCoreFlrmList();
            // const formatData = data?.map((item: any) => ({
            //     ...item
            // }));
            // setCoreFlrmList(formatData);
            setloading(false);
        })();
    }, [pageIndex, pageSize]);
    return {
        isLoading: loading,
        data: coreFlrmList
    };
};

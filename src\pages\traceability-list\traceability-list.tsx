/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-11-01 18:24:27
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space, Spin } from 'antd';
import useUrlState from '@ahooksjs/use-url-state';

import {
    sourcePackPage,
    modifySourcePackState,
    canInFoodList,
    productionBatchList,
    addSourcePack,
    download
} from '@services/trace-source-pack';
import { useMutation, useQuery } from 'react-query';

import styles from './index.module.less';
import { addTraceabilityConfigs, editEmployeesConfigs, searchConfig } from './config';
import SearchForm from '@components/search-form';
import { useRef, useState } from 'react';
import BaseModal from '@components/base-modal';
import FilterForm from '@components/filter-form';
import PageTitle from '@components/page-title';
import { SearchOutlined, PlusOutlined, SyncOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { useAccountList } from '../../myhooks/useaccountlist';
import { useNavigate } from 'react-router-dom';
import WithPaginate from '../../hoc/withpaginate';
import dayjs from 'dayjs';
import { ReformChainError } from '@utils/errorCodeReform';
import { ColumnsType } from 'antd/lib/table';

interface IUrlState {
    pageIndex: number;
    pageSize: number;
}

const TracebilityList = (props: any) => {
    const [search]: any = Form.useForm();
    const { pageInfo, handlePaginationChange } = props;
    const navigate = useNavigate();
    const [addModalVisible, setAddModelVisible] = useState(false);
    const [addTraceabilityForm] = Form.useForm();
    const queryuser: any = useRef(null);
    const [food_id, setfood_id] = useState('');
    const queryList: any = useAccountList({
        pageIndex: pageInfo.pageIndex,
        pageSize: pageInfo.pageSize
    });
    const [loading, setLoading] = useState(false);
    //下载码包
    const downLoad = useMutation(download, {
        onSuccess(res: any) {
            console.log('res7788', res);
            window.location.href = res.data;
            setLoading(false);
            return;
        },
        onError(err: any) {
            console.log('err000000', err);
            ReformChainError(err);
            setLoading(false);
            // const blob = new Blob([err.data], {
            //     type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            // });
            // console.log('err000000', blob);
            // const objectUrl = URL.createObjectURL(err.data);
            // const fileName = decodeURI(err?.headers['content-disposition'].split('filename=')[1]).replace(/\"/g, '');
            // console.log('fileName', fileName);
            // const a = document.createElement('a');
            // a.href = objectUrl;
            // a.download = fileName;
            // document.body.appendChild(a);
            // // window.location.href = objectUrl;
            // a.click();
            // a.remove();
            // setLoading(false);
            // return;
        }
    });
    const sourcemodiy = useMutation(modifySourcePackState, {
        onSuccess(res) {
            message.success('修改状态成功');
            sourcequery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            sourcequery.refetch();
        }
    });
    //新增码包
    const addSourcepack = useMutation(addSourcePack, {
        onSuccess(res) {
            sourcequery.refetch();
            message.success('新增成功');
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    //生产批次
    const productionList = useMutation(productionBatchList, {
        onSuccess(res) {
            console.log('res', res);
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    const canInFoodlist = useQuery(['canInfoodlist'], () => {
        return canInFoodList({});
    });
    const sourcequery = useQuery(
        ['sourcequery', pageInfo],
        () => {
            return sourcePackPage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                param: queryuser?.current?.name?.trim() || undefined,
                startTime: queryuser?.current?.Time ? queryuser?.current?.Time[0] : '',
                endTime: queryuser?.current?.Time ? queryuser?.current?.Time[1] : ''
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    console.log('userquery', sourcequery);
    //列表数据
    const tableData = sourcequery?.data?.data?.records?.map((item: any) => ({
        packNumber: item.packNumber,
        foodName: item.foodName,
        createTime: dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss'),
        productionBatch: item.productionBatch,
        codeCount: item.codeCount,
        status: item.state,
        tracePackId: item.tracePackId
    }));
    const listColumn: ColumnsType<any> = [
        {
            title: '码包ID',
            dataIndex: 'packNumber',
            key: 'packNumber',
            ellipsis: true
        },
        {
            title: '所属产品',
            dataIndex: 'foodName',
            key: 'foodName',
            ellipsis: true
        },
        {
            title: '生产批次',
            dataIndex: 'productionBatch',
            key: 'productionBatch',
            ellipsis: true
        },
        {
            title: '溯源码数量',
            dataIndex: 'codeCount',
            key: 'codeCount',
            ellipsis: true
        },
        {
            title: '生码时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            ellipsis: true,
            render: (data: any) => (
                <span style={{ color: data ? '#F64041' : '#666666' }}>
                    <Badge status={data ? 'error' : 'success'} color={data ? '#F64041' : 'rgb(36, 171, 59)'} />
                    {data ? '禁用' : '可用'}
                </span>
            )
        },
        {
            width: 200,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='small'>
                    <BaseButton
                        type='dashed'
                        className={record.status ? 'primaryBtn' : 'warnBtn'}
                        onClick={() => {
                            console.log('record', record);
                            sourcemodiy.mutate({
                                state: record.status == 0 ? 1 : 0,
                                tracePackId: record.tracePackId
                            });
                        }}
                    >
                        {record.status ? '启用' : '禁用'}
                    </BaseButton>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            // console.log('record.status：'+record.status)
                            downLoad.mutate({
                                status: record?.status,
                                tracePackId: record.tracePackId
                            });
                            setLoading(true);
                        }}
                    >
                        下载码包
                    </BaseButton>
                </Space>
            )
        }
    ];
    const onFieldsChange = (values: any, errorFields: any) => {
        console.log('production', values);
        if (values[0].name[0] === 'foodname') {
            console.log('production', '判断成功');
            addTraceabilityForm.setFieldsValue({ production: null }); //清空生产批次输入框
        } else {
        }
    };

    const mapToEnum: any = {};
    (canInFoodlist?.data?.data || [])?.forEach((item: any, index: any) => {
        mapToEnum[item.food_name] = item.food_id;
    });
    const mapToEnum_2: any = {};
    (productionList?.data?.data || [])?.forEach((item: any, index: any) => {
        mapToEnum_2[item.production_batch] = item.id;
    });
    const addTraceabilityConfigs = [
        {
            label: '食品',
            type: 'Select',
            value: 'foodname',
            placeholder: '请选择',
            showSearch: 'showSearch',
            rules: [{ required: true, message: '请选择!' }],
            onChange: (option: any, input: any) => {
                setfood_id(option);
                productionList.mutate({
                    foodId: mapToEnum[option]
                });
                console.log('option', option, input);
            },
            fields: [
                ...(canInFoodlist?.data?.data || [])?.map((item: any, index: any) => {
                    const materialdata = {
                        value: item.food_name,
                        label: item.food_name
                    };
                    return materialdata;
                })
            ]
        },
        {
            label: '生产批次',
            type: 'Select',
            value: 'production',
            placeholder: '请选择',
            name: 'production',
            rules: [{ required: true, message: '请选择!' }],
            fields: [
                ...(food_id
                    ? (productionList?.data?.data || []).map((item: any, index: any) => {
                          const materialdata = {
                              value: item.production_batch,
                              label: item.production_batch
                          };
                          return materialdata;
                      })
                    : [])
            ]
        },
        {
            label: '溯源码数量',
            type: 'InputNumber',
            value: 'count',
            placeholder: '请输入溯源码数量',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[1-9]\d{0,5}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入溯源码数量！');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            callback('请输入正整数，并保持在6字符内');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            wide: 292
        }
    ];
    const addTraceabilityConfig = {
        title: '生成码包',
        visible: addModalVisible,
        setVisible: setAddModelVisible,
        okHandle: async () => {
            const data = await addTraceabilityForm.getFieldsValue();
            console.log('addCoreForm', data);
            addSourcepack.mutate({
                foodId: mapToEnum[data.foodname],
                foodName: data.foodname,
                productionId: mapToEnum_2[data.production],
                productionBatch: data.production,
                count: data.count
            });
            // sourcequery.refetch();
            setAddModelVisible(false);
            addTraceabilityForm.resetFields();
        },
        onCancelHandle: () => {
            setAddModelVisible(false);
            addTraceabilityForm.resetFields();
        }
    };
    //查询
    const onFinish = (values: any) => {
        console.log('values', values);
        handlePaginationChange(1);
        values.Time = Array.isArray(values.Time)
            ? [dayjs(values.Time[0]).startOf('date').toISOString(), dayjs(values.Time[1]).endOf('date').toISOString()]
            : values.Time;

        queryuser.current = values;
        console.log('queryuser', queryuser);
        sourcequery.refetch();
    };
    return (
        <>
            <Spin spinning={loading}>
                <BaseCard
                    // className="coreFIrmContainer"
                    className={styles.coreFIrmContainer}
                    // mt24
                    title={<PageTitle title='溯源码包列表' bg='container su' />}
                >
                    <BaseTable
                        rowKey='account'
                        btnDisplay={(checkData: any, resetSelect: any) => {
                            return (
                                <TableHead
                                    LeftDom={
                                        <div
                                            // className="searchContainer"
                                            className={styles.searchContainer}
                                        >
                                            <Form
                                                layout='inline'
                                                labelAlign='left'
                                                onFinish={onFinish}
                                                form={search}
                                                className='label-title'
                                            >
                                                <FilterForm itemConfig={searchConfig} size={100} labelCol={6} />
                                                <BaseButton
                                                    type='primary'
                                                    // className='searchBtn'
                                                    style={{ width: 100 }}
                                                    // className={styles.submitBtn}
                                                    className={`${styles.searchBtn} ${styles.baseBtn}`}
                                                    icon={<SearchOutlined rev={undefined} />}
                                                    htmlType='submit'
                                                >
                                                    查询
                                                </BaseButton>
                                                <BaseButton
                                                    type='dashed'
                                                    className='primaryBtn'
                                                    // className={styles.primaryBtn}
                                                    icon={<SyncOutlined rev={undefined} />}
                                                    style={{ width: 100 }}
                                                    onClick={() => {
                                                        queryuser.current = '';
                                                        sourcequery.refetch();
                                                        search.resetFields();
                                                    }}
                                                >
                                                    重置
                                                </BaseButton>
                                            </Form>
                                        </div>
                                    }
                                    RightDom={
                                        <BaseButton
                                            type='dashed'
                                            // icon={<PlusOutlined rev={undefined} />}
                                            className='bgBtn'
                                            onClick={() => {
                                                setAddModelVisible(true);
                                            }}
                                        >
                                            新建溯源码包
                                        </BaseButton>
                                    }
                                />
                            );
                        }}
                        columns={listColumn}
                        dataSource={tableData}
                        loading={sourcequery?.isLoading}
                    />
                    <BasePagination
                        shouldShowTotal
                        showQuickJumper
                        showSizeChanger
                        current={pageInfo.pageIndex}
                        pageSize={pageInfo.pageSize}
                        total={sourcequery?.data?.data.total}
                        onShowSizeChange={handlePaginationChange}
                        onChange={handlePaginationChange}
                    />
                </BaseCard>

                <BaseModal {...addTraceabilityConfig}>
                    <Form
                        name='addTraceabilityForm'
                        form={addTraceabilityForm}
                        onFieldsChange={onFieldsChange}
                        className='edit-label-title'
                    >
                        {<FilterForm itemConfig={addTraceabilityConfigs} />}
                    </Form>
                </BaseModal>
            </Spin>
        </>
    );
};

export default WithPaginate(TracebilityList);

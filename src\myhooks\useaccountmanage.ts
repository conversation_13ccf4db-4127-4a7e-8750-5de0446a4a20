/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-08 09:47:50
 * @LastEditTime: 2022-11-01 18:26:27
 * @LastEditors: PhilRandWu
 */
import { useEffect, useState } from 'react';
// import { requestaccountMock } from '../mock/account-manage';

interface searchConditionInterFace {
    pageIndex?: number;
    pageSize?: number;
}

export const useAccountList = function ({ pageIndex, pageSize }: searchConditionInterFace) {
    const [accountList, setAccountList] = useState([]);
    const [loading, setloading] = useState(true);
    useEffect(() => {
        (async () => {
            // const data: any = await requestaccountMock();
            // const formatData = data?.map((item: any) => ({
            //     ...item
            // }));
            // setAccountList(formatData);
            setloading(false);
        })();
    }, [pageIndex, pageSize]);
    return {
        isLoading: loading,
        data: accountList
    };
};

import BaseInput from '@components/base-input';
import BaseSelect from '@components/base-select';
import BaseTooptip from '@components/base-tooltip';
import type { RangePickerProps } from 'antd/es/date-picker';
import axios from 'axios';
import img from '@assets/icon/bg.jpg';
import {
    Form,
    Radio,
    Select,
    Input,
    Upload,
    message,
    Cascader,
    InputNumber,
    Row,
    Col,
    DatePicker,
    UploadFile,
    Button,
    Modal,
    Tooltip
} from 'antd';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { temporaryUploadUrl } from '@services/food';
import type { UploadChangeParam } from 'antd/es/upload';
import { UploadOutlined } from '@ant-design/icons';
import type { RcFile, UploadProps } from 'antd/es/upload/interface';
import React, { useEffect, useState } from 'react';
import './index.less';
import dayjs from 'dayjs';
import moment from 'moment';

import { useMutation, useQuery } from 'react-query';
import BaseDatePicker from '@components/base-date-picker';
import BaseUploadFile from '@components/base-upload-file';
import { resolve } from 'path';
import { config } from 'process';
import BaseTooltip from '@components/base-tooltip';

import { fileUpload } from '@utils';

const { Option } = Select;
const { TextArea } = Input;

interface IFilterFormItem {
    type:
        | 'Input'
        | 'Select'
        | 'TagsSelect'
        | 'Button'
        | 'Radio'
        | 'TextArea'
        | 'Upload'
        | 'Cascader'
        | 'DatePicker'
        | 'InputNumber'
        | 'ShowText'
        | 'ShowScrollText'
        | 'RangePicker'
        | 'UploadFile'
        | 'UploadImg'
        | 'Custom'
        | 'Link';
    label: string;
    value: string;
    className: any;
    rules?: any;
    placeholder: string;
    fields: any;
    disabled?: boolean;
    defaultValue?: any;
    onOpenChange?: () => {};
    format?: any;
    picker?: any;
    span?: number;
    display?: any;
    onChange?: any;
    color?: any;
    size?: any;
    showSearch?: any;
    displayDom?: any;
    mobile?: any;
    wide?: any;
    upload?: any;
    loadData?: any;
    getUrlCallback?: any;
    edit?: any;
    disable?: any;
    required?: any;
    mode?: any;
    title?: any;
    options?: any;
    onSearch?: any;
    onRemove?: any;
    disabledTime?: any;
    disabledSecondsTime?: any;
    getFieldValue?: any;
    maxCount?: number;
    children?: any;
    hidden?: any;
    labelCol?: any;
    initialValue?: any;
    wrapperCol?: any;
    name: any;
    showTime: boolean;
    maxWidth?: number;
}

const ShowTimeText = ({ value, maxWidth }: any) => {
    console.log(value);
    return (
        <div className='showtext-item'>
            <BaseTooltip
                preStyle={{ lineHeight: 'inherit', whiteSpace: 'nowrap' }}
                data={
                    value instanceof dayjs
                        ? dayjs(value as any).format('YYYY-MM-DD HH:mm:ss')
                        : value && value !== 'Invalid Date'
                        ? value
                        : '-'
                }
                maxWidth={maxWidth || 100}
            ></BaseTooltip>
        </div>
    );
};

const ShowScrollText = ({ value }: any) => {
    return value ? (
        <BaseTooltip preStyle={{ lineHeight: 'inherit', whiteSpace: 'nowrap' }} data={value}></BaseTooltip>
    ) : (
        // <div className='ShowScrollText-container'>
        //     <pre className='showtext-item'>{value}</pre>
        // </div>
        <div className='showtext-item'>-</div>
    );
};

const LinkText = ({ value, onClick }: any) => {
    return value && value !== '-' ? (
        <div>
            <a onClick={() => onClick()} style={{ color: '#80a932' }}>
                {value}
            </a>
        </div>
    ) : (
        <div>-</div>
    );
};

const getBase64 = (img: RcFile, callback: (url: string) => void) => {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result as string));
    reader.readAsDataURL(img);
};
const getBase645 = (file: RcFile): Promise<string> =>
    new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = (error) => reject(error);
    });

/**
 * showMode @type {boolean} 将formItem 的label和value顶部对齐
 */
const FilterForm = (props: any) => {
    const [previewOpen, setPreviewOpen] = useState(false);
    const [previewImage, setPreviewImage] = useState('');
    const [previewTitle, setPreviewTitle] = useState('');
    const [loading, setLoading] = useState(false); //上传状态
    const [imageUrl, setImageUrl] = useState<string>();
    const [fileList, setFileList] = useState<UploadFile[]>([]);
    const [isUpLoading, setIsUpLoading]: any = useState();
    const currenttime = dayjs().format('YYYY-MM-DD');
    const range = (start: number, end: number) => {
        const result = [];
        for (let i = start; i < end; i++) {
            result.push(i);
        }
        return result;
    };

    //当前时间限制
    const disabledDate = (current: any) => {
        return current > moment().subtract(0, 'days');
    };
    const disabledTime = (dates: any) => {
        const range = (start: any) => {
            const result = [];
            for (let i = 0; i < start; i++) {
                result.push(i);
            }
            return result;
        };
        const selDate = moment(dates).date();
        const selHours = moment(dates).hours();
        const selMinutes = moment(dates).minutes();

        const currentDate = moment().date();
        const hours = moment().hours();
        const minutes = moment().minutes();
        const seconds = moment().seconds();
        let res: any = {};
        if (selDate === currentDate) {
            res.disabledHours = () => range(hours);
            if (selHours === hours) {
                res.disabledMinutes = () => range(minutes);
                if (selMinutes === minutes) {
                    res.disabledSeconds = () => range(seconds);
                }
            }
            return res;
        }
    };
    useEffect(() => {
        if (props?.itemConfig[7]?.edit) {
            setFileList([
                {
                    uid: '1', //初始化图片文件列表
                    name: 'image.png',
                    status: 'done',
                    url: props?.itemConfig[7]?.edit //图片路径
                }
            ]);
        } else {
        }
    }, [props?.itemConfig[7]?.edit]);
    const onRemove = () => {
        props.itemConfig[7].edit = null;
        setFileList([]);
    };
    const foodimg: any = useMutation(temporaryUploadUrl, {});
    const beforeUpload = (file: RcFile) => {
        const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
        if (!isJpgOrPng) {
            message.error('请上传jpg/jpeg/png格式的图片!');
            return Upload.LIST_IGNORE;
        }
        const isLt2M = file.size / 1024 / 1024 < 20;
        if (!isLt2M && isJpgOrPng) {
            message.error('请上传20MB以内大小的图片!');
            return Upload.LIST_IGNORE;
        }

        console.log('true-texst', isJpgOrPng && isLt2M);
        return isJpgOrPng && isLt2M;
    };
    const beforeUploadFile = (file: RcFile) => {
        const isLt2M = file.size / 1024 / 1024 < 20;
        if (!isLt2M) {
            message.error('请上传20MB以内大小的附件!');
            return Upload.LIST_IGNORE;
        }
        return isLt2M;
    };
    const handleChange: UploadProps['onChange'] = (info: UploadChangeParam<UploadFile>) => {
        console.log('addd', info);
        setFileList(info?.fileList);
        console.log('addd', info?.fileList);
        if (info.file.status === 'uploading') {
            setLoading(true);
            return;
        }
        if (info.file.status === 'done') {
            // Get this url from response in real world.
            getBase64(info.file.originFileObj as RcFile, (url: any) => {
                //上传格式
                console.log('imager999299', imageUrl);
                setLoading(false);
                setImageUrl(url);
                console.log('img123121111111', imageUrl);
            });
        }
    };
    // const [label,setlabel] = useState('')
    const uploadButton = (
        <div>
            {loading ? <LoadingOutlined rev={undefined} /> : <PlusOutlined rev={undefined} />}
            <div style={{ marginTop: 8 }}>上传</div>
        </div>
    );
    const handleCancel = () => setPreviewOpen(false);
    const handlePreview = async (file: UploadFile) => {
        //图片放大
        if (!file.url && !file.preview) {
            file.preview = await getBase645(file.originFileObj as RcFile);
        }

        setPreviewImage(file.url || (file.preview as string));
        setPreviewOpen(true);
        setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
    };
    const onChange = (info: any) => {
        if (info.file.status !== 'uploading') {
            console.log(info.file, info.fileList);
        }
        if (info.file.status === 'done') {
            message.success(`${info.file.name} 文件上传成功`);
        } else if (info.file.status === 'error') {
            message.error(`${info.file.name} 文件上传失败`);
        }
    };
    const normFile = (e: any) => {
        console.log('Upload event:', e);
        if (Array.isArray(e)) {
            return e;
        }
        console.log('imageurl0123333333', imageUrl);
        return e?.fileList;
    };
    const formItemBuilder = (config: IFilterFormItem) => {
        switch (config.type) {
            case 'Input':
                return (
                    <BaseInput {...config} style={{ width: config?.wide }} disabled={config?.disable ? true : false} />
                );

            case 'Select':
                return (
                    <BaseSelect
                        {...config}
                        allowClear
                        getPopupContainer={(trigger: any) => trigger.parentNode}
                        mode={config?.mode ? 'multiple' : undefined}
                        style={{ width: props?.size }}
                        // onDropdownVisibleChange={item?.dropDownHandle}
                    >
                        {config?.fields?.map((option: any) => {
                            console.log('configconfig', option);
                            return (
                                option && (
                                    <Option key={option?.index} value={option?.value}>
                                        {option?.label}
                                    </Option>
                                )
                            );
                        })}
                    </BaseSelect>
                );

            case 'TagsSelect':
                return (
                    <Select
                        {...config}
                        mode='tags'
                        // size={size}
                        // placeholder="Please select"
                        // defaultValue={['a10', 'c12']}
                        // onChange={handleChange}
                        style={{ width: '100%' }}
                    >
                        {config?.fields?.map((option: any) => {
                            return (
                                option && (
                                    <Option key={option?.value} value={option?.value}>
                                        {option?.label}
                                    </Option>
                                )
                            );
                        })}
                    </Select>
                );

            case 'Radio':
                return (
                    <Radio.Group
                    // onChange={onChange} value={value}
                    >
                        {config?.fields?.map((option: any) => {
                            return (
                                option && (
                                    <Radio
                                        key={option?.value}
                                        value={option?.value}
                                        disabled={config?.disable ? true : false}
                                    >
                                        {option?.label}
                                    </Radio>
                                )
                            );
                        })}
                    </Radio.Group>
                );

            case 'TextArea':
                return (
                    <TextArea
                        {...config}
                        style={{ width: config?.wide, height: 178 }}
                        className='summarytextinput'
                        maxLength={200}
                    />
                );

            case 'ShowText':
                return (
                    <ShowTimeText
                        value={props.form?.getFieldValue(config?.value)}
                        maxWidth={config?.maxWidth}
                    ></ShowTimeText>
                );
            case 'ShowScrollText':
                return <ShowScrollText></ShowScrollText>;
            case 'Link':
                return <LinkText onClick={() => (config as any).onClick()} {...config}></LinkText>;
            case 'Upload':
                return (
                    <div className='uploadContainer'>
                        <Upload
                            name='avatar'
                            listType='picture-card'
                            className='avatar-uploader'
                            showUploadList={true}
                            fileList={fileList}
                            onPreview={handlePreview}
                            maxCount={1}
                            onRemove={config?.onRemove ? config?.onRemove : onRemove} //删除回调
                            // customRequest={async ({ file, onError, onProgress, onSuccess }) => {
                            //     //通过覆盖默认的上传行为，可以自定义自己的上传实现
                            //     console.log('file', file);
                            //     try {
                            //         foodimg.mutate(
                            //             {
                            //                 // @ts-ignore
                            //                 fileName: file.name
                            //             },
                            //             {
                            //                 onSuccess: (result: any) => {
                            //                     console.log('onSuccess: ', result);
                            //                     const index = result?.data?.indexOf('?');
                            //                     const url = result?.data?.substring(0, index);
                            //                     setImageUrl(url);
                            //                     try {
                            //                         const uploadRet = axios({
                            //                             method: 'put',
                            //                             url: url,
                            //                             onUploadProgress: onProgress,
                            //                             data: file
                            //                         }).then((res: any) => {
                            //                             if (res.status === 200) {
                            //                                 onSuccess?.({
                            //                                     ...uploadRet
                            //                                     // url: url
                            //                                 });
                            //                                 if (config.getUrlCallback) {
                            //                                     config.getUrlCallback(res.config.url);
                            //                                 }
                            //                             }
                            //                         });
                            //                     } catch (err: any) {
                            //                         message.error('文件上传失败');
                            //                         onError?.(err);
                            //                     }
                            //                 }
                            //             },
                            //             {
                            //                 onError: () => {
                            //                     console.log('onSuccess12312: ');
                            //                 }
                            //             }
                            //         );
                            //     } catch (err: any) {
                            //         message.error('获取文件上传路径失败');
                            //         onError?.(err);
                            //     }
                            // }}
                            customRequest={({ file, onError, onProgress, onSuccess }) => {
                                // @ts-ignore
                                fileUpload({
                                    ...{ file, onError, onProgress, onSuccess },
                                    isUploading: isUpLoading,
                                    setIsUpLoading: setIsUpLoading
                                });
                            }}
                            beforeUpload={beforeUpload}
                            onChange={handleChange}
                        >
                            {fileList.length > 0 //逻辑无误断点
                                ? null
                                : // <img src={imageUrl || config?.edit} alt='avatar' style={{ width: 104, height: 104 }} />
                                  uploadButton}
                        </Upload>
                        <Modal open={previewOpen} footer={null} onCancel={handleCancel} className='modal-span'>
                            <img alt='example' style={{ width: '100%' }} src={previewImage} />
                        </Modal>
                        <span>{config?.placeholder}</span>
                    </div>
                );

            case 'Cascader':
                return (
                    <Cascader
                        defaultValue={config?.defaultValue}
                        options={config?.fields}
                        loadData={config?.loadData}
                        placeholder={config?.placeholder}
                        getPopupContainer={(trigger: any) => trigger.parentNode}
                        // onChange={onChange}
                    />
                );

            case 'RangePicker':
                config.onOpenChange && config.onOpenChange();
                return (
                    <BaseDatePicker
                        allowClear
                        picker={config?.picker}
                        // format={config?.format ? config?.format : ['YYYY-MM-DD', 'YYYY-MM-DD']}
                        placeholder={config?.placeholder}
                        defaultValue={config?.defaultValue}
                        onOpenChange={config?.onOpenChange && config?.onOpenChange}
                        style={{ width: config?.wide }}
                    />
                );

            case 'DatePicker':
                return (
                    <DatePicker
                        // disabledDate={
                        //     config?.getFieldValue
                        //         ? (current: any) => {
                        //               return current < config.getFieldValue();
                        //           }
                        //         : config?.disabledTime
                        //         ? config?.disabledTime
                        //         : null
                        // }
                        // disabledTime={config?.disabledTime?disabledTime:config?.disabledTime}
                        placeholder={config?.placeholder}
                        showTime={config?.showTime || false}
                        getPopupContainer={(trigger: any) => trigger.parentNode} //悬浮问题
                        disabledDate={config?.disabledTime ? config?.disabledTime : null}
                        disabledTime={config?.disabledSecondsTime ? config?.disabledSecondsTime : null}
                        style={{ width: config?.wide }}
                    />
                );
            case 'Button':
                return (
                    <Button {...config} type={config?.color}>
                        {config.placeholder}
                    </Button>
                );

            case 'InputNumber':
                return (
                    <InputNumber
                        min={1}
                        placeholder={config?.placeholder}
                        defaultValue={config?.defaultValue}
                        style={{ width: config?.wide }}
                    />
                );

            case 'UploadFile':
                return (
                    <div className='UploadFileContent' style={{ width: config?.wide }}>
                        <div>
                            <Upload
                                name='avatar'
                                showUploadList={true}
                                maxCount={1}
                                onRemove={onRemove}
                                customRequest={async ({ file, onError, onProgress, onSuccess }) => {
                                    console.log('file', file);
                                    try {
                                        foodimg.mutate(
                                            {
                                                // @ts-ignore
                                                fileName: file.name
                                            },
                                            {
                                                onSuccess: (result: any) => {
                                                    console.log('onSuccess: ', result);
                                                    const index = result?.data?.indexOf('?');
                                                    const url = result?.data?.substring(0, index);
                                                    // setImageUrl(url);
                                                    try {
                                                        const uploadRet = axios({
                                                            method: 'put',
                                                            url: url,
                                                            onUploadProgress: onProgress,
                                                            data: file
                                                        }).then((res: any) => {
                                                            if (res.status === 200) {
                                                                onSuccess?.({
                                                                    ...uploadRet
                                                                    // url: url
                                                                });
                                                                if (config.getUrlCallback) {
                                                                    config.getUrlCallback(res.config.url);
                                                                }
                                                            }
                                                        });
                                                    } catch (err: any) {
                                                        message.error('文件上传失败');
                                                        onError?.(err);
                                                    }
                                                }
                                            },
                                            {
                                                onError: () => {
                                                    console.log('onSuccess12312: ');
                                                }
                                            }
                                        );
                                    } catch (err: any) {
                                        message.error('获取文件上传路径失败');
                                        onError?.(err);
                                    }
                                }}
                                onChange={onChange}
                                beforeUpload={beforeUploadFile}
                                className='uploadFileContainer'
                            >
                                <Button icon={<UploadOutlined rev={undefined} />} className='butcolor'>
                                    上传文件
                                </Button>
                            </Upload>
                        </div>
                        <span>{config?.placeholder}</span>
                    </div>
                );

            case 'UploadImg':
                return (
                    <div className='uploadContainer'>
                        <Upload
                            name='avatar'
                            listType='picture-card'
                            className='avatar-uploader'
                            showUploadList={true}
                            fileList={fileList}
                            onPreview={handlePreview}
                            maxCount={config?.maxCount}
                            customRequest={async ({ file, onError, onProgress, onSuccess }) => {
                                //通过覆盖默认的上传行为，可以自定义自己的上传实现
                                console.log('file', file);
                                try {
                                    foodimg.mutate(
                                        {
                                            // @ts-ignore
                                            fileName: file.name
                                        },
                                        {
                                            onSuccess: (result: any) => {
                                                console.log('onSuccess: ', result);
                                                const index = result?.data?.indexOf('?');
                                                const url = result?.data?.substring(0, index);
                                                setImageUrl(url);
                                                try {
                                                    const uploadRet = axios({
                                                        method: 'put',
                                                        url: url,
                                                        onUploadProgress: onProgress,
                                                        data: file
                                                    }).then((res: any) => {
                                                        if (res.status === 200) {
                                                            onSuccess?.({
                                                                ...uploadRet
                                                                // url: url
                                                            });
                                                            if (config.getUrlCallback) {
                                                                config.getUrlCallback(res.config.url);
                                                            }
                                                        }
                                                    });
                                                } catch (err: any) {
                                                    message.error('文件上传失败');
                                                    onError?.(err);
                                                }
                                            }
                                        },
                                        {
                                            onError: () => {
                                                console.log('onSuccess12312: ');
                                            }
                                        }
                                    );
                                } catch (err: any) {
                                    message.error('获取文件上传路径失败');
                                    onError?.(err);
                                }
                            }}
                            beforeUpload={beforeUpload}
                            onChange={handleChange}
                        >
                            {config?.maxCount && fileList.length >= config?.maxCount //逻辑无误断点
                                ? null
                                : // <img src={imageUrl || config?.edit} alt='avatar' style={{ width: 104, height: 104 }} />
                                  uploadButton}
                        </Upload>
                        <Modal open={previewOpen} footer={null} onCancel={handleCancel} className='modal-span'>
                            <img alt='example' style={{ width: '100%' }} src={previewImage} />
                        </Modal>
                        <span>{config?.placeholder}</span>
                    </div>
                );

            case 'Custom':
                console.log('config?.children', config, config?.children);
                console.log('config?.children', config?.children.length);

                return config?.children ? config?.children : '-';
        }
    };
    return (
        <Row className={props?.showMode ? 'filterFormOnlyShow' : ''}>
            {props?.itemConfig?.map((config: IFilterFormItem) =>
                // console.log(config)
                {
                    console.log(config);
                    return config?.required === 'requireds' ? (
                        <Col span={config?.span ? config?.span : 24}>
                            <Form.Item
                                className={props?.className}
                                style={{ marginLeft: config?.size }}
                                // labelCol={{ flex: 'auto' }}
                                labelCol={
                                    props?.labelCol !== false
                                        ? { span: props?.labelCol !== undefined ? props?.labelCol : 6 }
                                        : {}
                                }
                                wrapperCol={{ span: props?.wrapperCol !== undefined ? props?.wrapperCol : 15 }}
                                key={config?.value}
                                label={config?.label}
                                name={config?.value}
                                rules={config?.rules}
                                tooltip={config?.title}
                                required={config?.required}
                                // valuePropName={config?.upload ? 'fileList' : undefined}
                                // getValueFromEvent={config?.upload ? normFile : undefined}
                            >
                                {/* <div style={{ display: 'flex', alignItems: 'center' }}> */}
                                {config?.display ? config?.display : formItemBuilder(config)}
                                {/* </div> */}
                                {/* <h1>{config?.display ? config?.displayDom : ''}</h1> */}
                                {/* {formItemBuilder(config)} */}
                            </Form.Item>
                        </Col>
                    ) : (
                        <Col span={config?.span ? config?.span : 24}>
                            <Form.Item
                                className={props?.className}
                                style={{ marginLeft: config?.size }}
                                // labelCol={{ flex: 'auto' }}
                                labelCol={
                                    props?.labelCol !== false
                                        ? { span: props?.labelCol !== undefined ? props?.labelCol : 6 }
                                        : {}
                                }
                                wrapperCol={{ span: props?.wrapperCol !== undefined ? props?.wrapperCol : 15 }}
                                key={config?.value}
                                label={config?.label}
                                name={config?.value}
                                rules={config?.rules}
                                tooltip={config?.title}
                                // required={config?.required}
                                // valuePropName={config?.upload ? 'fileList' : undefined}
                                // getValueFromEvent={config?.upload ? normFile : undefined}
                            >
                                {/* <div style={{ display: 'flex', alignItems: 'center' }}> */}
                                {config?.display ? config?.display : formItemBuilder(config)}
                                {/* </div> */}
                                {/* <h1>{config?.display ? config?.displayDom : ''}</h1> */}
                                {/* {formItemBuilder(config)} */}
                            </Form.Item>
                        </Col>
                    );
                }
            )}
            {/* <pre>{JSON.stringify(props?.itemConfig,null,4)}</pre> */}
        </Row>
    );
};

export default FilterForm;

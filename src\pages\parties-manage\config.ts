/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:27:42
 * @LastEditTime: 2022-10-12 17:55:46
 * @LastEditors: PhilRandWu
 */
export const addPartiesConfigs = [
    {
        label: '参与方名称',
        type: 'Input',
        value: 'name',
        placeholder: '请输入参与方名称',
        rules: [{ required: true, message: '' },
        () => ({
            validator: (_: any, value: any, callback: any) => {
                const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,40}$/);
                const verify = regExp.test(value);
                if (!value) {
                    callback('请输入参与方名称!');
                } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                    callback('字段前后不能输入空格！');
                }
                else if (verify === false) {
                    if (value.length > 40) {
                        callback('请保持字符在40字符以内!');
                    } else {
                        callback('请输入参与方名称，支持中文、字母或数字!');
                    }
                }
                else {
                    callback();
                }
            }
        })
        ]
    },
    {
        label: '参与方类型',
        type: 'Radio',
        value: 'type',
        placeholder: '请选择',
        rules: [{ required: true, message: '请选择参与方类型' }],
        fields: [
            {
                value: 2,
                label: '供应商'
            },
            {
                value: 3,
                label: '质检机构'
            },
            {
                value: 4,
                label: '监管机构'
            }
        ]
    },
    {
        label: '备注',
        showCount: true,
        type: 'TextArea',
        value: 'permission',
        // placeholder: '请选择',
        // rules: [
        // () => ({
        //             validator: (_: any, value: any, callback: any) => {
        //                 const regExp = new RegExp(/^[\x21-\x2F\x3A-\x40\x5B-\x60\x7B-\x7E\u4e00-\u9fa5_a-zA-Z0-9_]{0,200}$/);
        //                 const verify = regExp.test(value);
        //                   if (verify === false) {
        //                     callback('请保持字符在200字符以内!');
        //                 } else {
        //                     callback();
        //                 }
        //             }
        //         })
        // ]
    }
];

export const editEmployeesConfigs = [
    {
        label: '员工名称',
        type: 'Input',
        value: 'name',
        placeholder: 'erferferf',
        rules: [{ required: true, message: 'Please input your username!' }]
    },
    {
        label: '联系方式',
        type: 'Input',
        value: 'name',
        placeholder: 'erferferf',
        rules: [{ required: true, message: 'Please input your username!' }]
    },
    {
        label: '管理员账号',
        type: 'TagsSelect',
        value: 'account',
        placeholder: 'fefer',
        rules: [{ required: true, message: 'Please input your username!' }],
        fields: [
            {
                value: '12发给3',
                label: '123vfv123'
            },
            {
                value: '12dfvdfdf3',
                label: '1231vdfvdfvdd23'
            },
            {
                value: '12ddvsdfsffgnf3',
                label: '123gfbgfbfgb123'
            }
        ]
    }
];

export const searchConfig = [
    {
        label: '企业名称',
        type: 'Input',
        value: 'name',
        placeholder: '输入企业名称',
        span: 8,
        // wide:130
    },
    {
        label: '企业类型',
        type: 'Select',
        value: 'type',
        placeholder: '请选择',
        span: 8,
        fields: [
            {
                value: 2,
                label: '供应商'
            },
            {
                value: 3,
                label: '质检机构'
            },
            {
                value: 4,
                label: '监管机构'
            },
            {
                value: 5,
                label: ' 物流机构'
            },
        ]
    },
    {
        label: '参与方状态',
        type: 'Select',
        value: 'status',
        placeholder: '请选择',
        span: 8,
        fields: [
            {
                value: '1',
                label: '禁用'
            },
            {
                value: '0',
                label: '可用'
            }
        ]
    }
];

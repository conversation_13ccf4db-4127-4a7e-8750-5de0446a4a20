import { useState } from 'react';
import { Col, Form, message, Row, Space, Badge, Card } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';

import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';
import BaseInput from '@components/base-input/base-input';
import BaseSelect from '@components/base-select/base-select';
import BaseDatePicker from '@components/base-date-picker';

import styles from './index.module.less';
import { RoleEnum } from '@config';

import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { Enum2Object } from '@utils/enum';
import SourceDataService, { SourceStateEnum } from '@services/traceability_data/source_data';
import { userInfo } from 'os';
import { useAppSelector } from '@store';
import PublicSourceService from '@services/traceability_data/public_source';
import { QueryTime } from '@utils';

interface IUrlState {
    pageIndex: number;
    pageSize: number;
    // ...
}

const Inspect = () => {
    const userInfo = useAppSelector((store) => store.user);
    const LocalLoginIdentity = Number(userInfo?.userInfo?.identity);
    const [queryParams, setQueryParams] = useState<any>({ pageSize: 10, pageIndex: 1 });

    const [form] = Form.useForm();
    const navigate = useNavigate();

    const [isSimpleSearch, setIsSimpleSearch] = useState(true);

    const queryCoreOrgList = useQuery(['queryCoreOrgList'], () => PublicSourceService.getCoreList(), {
        enabled: [RoleEnum.平台方]?.includes(LocalLoginIdentity)
    });
    const queryCoreOrgListData: any[] = queryCoreOrgList?.data?.data;

    const queryProductList = useQuery(['queryProductList'], () => PublicSourceService.getProductList());
    const queryProductListData: any[] = queryProductList?.data?.data;

    const queryProduction = useQuery(['queryInspect', queryParams], () => SourceDataService.landList(queryParams), {
        onSuccess() {},
        onError() {}
    });

    console.log('queryProduction', queryProduction);
    const queryProductionData: any = queryProduction?.data?.data;
    const coreEnterpriseColumns = () => {
        return [RoleEnum.平台方].includes(LocalLoginIdentity)
            ? [
                  {
                      title: '生产加工企业',
                      dataIndex: 'coreName',
                      key: 'coreName',
                      ellipsis: true
                  }
              ]
            : [];
    };

    const columns: ColumnsType<any> = [
        {
            title: '生产批次',
            dataIndex: 'productionBatch',
            key: 'productionBatch',
            ellipsis: true
        },
        {
            title: '产品名称',
            dataIndex: 'productName',
            key: 'productName',
            ellipsis: true
        },

        {
            title: '种植批次',
            dataIndex: 'plantBatch',
            key: 'plantBatch',
            ellipsis: true
        },
        {
            title: '地块信息',
            dataIndex: 'landName',
            key: 'landName',
            ellipsis: true
        },
        {
            title: '农作物类型',
            dataIndex: 'plantName',
            key: 'plantName',
            ellipsis: true
        },
        // ...coreEnterpriseColumns(),
        {
            title: '播种时间',
            dataIndex: 'sowTime',
            key: 'sowTime',
            ellipsis: true,
            render: (_, row) => (row.sowTime ? dayjs(row.sowTime).format('YYYY-MM-DD HH:mm:ss') : '-')
        },
        {
            title: '收割时间',
            dataIndex: 'harvestTime',
            key: 'harvestTime',
            ellipsis: true,
            render: (_, row) => (row.harvestTime ? dayjs(row.harvestTime).format('YYYY-MM-DD HH:mm:ss') : '-')
        },
        // {
        //     title: '哈希',
        //     dataIndex: 'transactionId',
        //     key: 'transactionId',
        //     ellipsis: true
        // },
        // {
        //     title: '状态',
        //     dataIndex: 'state',
        //     key: 'state',
        //     ellipsis: true,
        //     render: (_, row) => {
        //         if (row.state === 0) {
        //             return <Badge status='success' text='可用' />;
        //         } else if (row.state === 1) {
        //             return <Badge status='error' text='作废' />;
        //         }
        //     }
        // },
        {
            title: '操作',
            render: (_, row) => (
                <BaseButton type='dashed' className='primaryBtn' onClick={() => navigate(`detail/${row?.id}`)}>
                    查看详情
                    {}
                </BaseButton>
            )
        }
    ];

    const searchFormItems = [
        <Form.Item label='生产批次' name='productionBatch'>
            <BaseInput placeholder='请输入生产批次'></BaseInput>
        </Form.Item>,
        <Form.Item label='种植批次' name='plantBatch'>
            <BaseInput placeholder='请输入生产批次'></BaseInput>
        </Form.Item>
        // <Form.Item label='创建时间' name='createTime'>
        //     <BaseDatePicker style={{ width: '100%' }}></BaseDatePicker>
        // </Form.Item>,
        // <Form.Item label='状态' name='state'>
        //     <BaseSelect placeholder='请选择' options={Enum2Object(SourceStateEnum)}></BaseSelect>
        // </Form.Item>,
        // ...([RoleEnum.平台方].includes(LocalLoginIdentity)
        //     ? [
        //           <Form.Item label='生产加工企业' name='coreId'>
        //               <BaseSelect
        //                   placeholder='请选择'
        //                   options={queryCoreOrgListData?.map((item) => ({
        //                       label: item?.shortName,
        //                       value: item?.id
        //                   }))}
        //               ></BaseSelect>
        //           </Form.Item>
        //       ]
        //     : [])
    ];

    return (
        <>
            <Card style={{ marginBottom: 10 }} title={<PageTitle title='种植溯源列表' bg='container zhong' />}>
                <Form
                    form={form}
                    labelCol={{ span: 5 }}
                    labelAlign='left'
                    onFinish={(values) => {
                        console.log(values, 'values');
                        const TimeArr = QueryTime(values?.createTime);
                        setQueryParams({
                            ...values,
                            productionBatch: values?.productionBatch?.trim(),
                            plantBatch: values?.plantBatch?.trim(),
                            // startTime: TimeArr?.[0],
                            // endTime: TimeArr?.[1],
                            pageIndex: 1,
                            pageSize: 10
                        });
                    }}
                    className='label-title label-title-more'
                >
                    <Row gutter={[36, 12]}>
                        {searchFormItems.slice(0, isSimpleSearch ? 2 : searchFormItems.length).map((searchFormItem) => (
                            <Col key={searchFormItem.key} span={8}>
                                {searchFormItem}
                            </Col>
                        ))}
                        <Col span={[RoleEnum.平台方].includes(LocalLoginIdentity) ? 8 : isSimpleSearch ? 8 : 16}>
                            <div style={{ display: 'flex', justifyContent: 'end' }}>
                                <Space>
                                    <BaseButton type='primary' htmlType='submit'>
                                        查询
                                    </BaseButton>
                                    <BaseButton
                                        onClick={() => {
                                            form.resetFields();
                                            setQueryParams({
                                                pageIndex: 1,
                                                pageSize: 10
                                            });
                                        }}
                                    >
                                        重置
                                    </BaseButton>
                                    {/* <BaseButton
                                        type='link'
                                        onClick={() => {
                                            setIsSimpleSearch(!isSimpleSearch);
                                        }}
                                    >
                                        {isSimpleSearch ? '展开' : '收起'}
                                        {isSimpleSearch ? (
                                            <DownOutlined rev={undefined} />
                                        ) : (
                                            <UpOutlined rev={undefined} />
                                        )}
                                    </BaseButton> */}
                                </Space>
                            </div>
                        </Col>
                    </Row>
                </Form>
            </Card>
            <BaseCard className={styles.coreFIrmContainer}>
                <BaseTable
                    loading={queryProduction.isLoading}
                    columns={columns}
                    dataSource={queryProductionData?.records}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={queryParams.pageIndex}
                    pageSize={queryParams.pageSize}
                    total={queryProductionData?.total}
                    onShowSizeChange={(page, pageSize) => {
                        setQueryParams({
                            ...queryParams,
                            pageIndex: page,
                            pageSize
                        });
                    }}
                    onChange={(page, pageSize) => {
                        setQueryParams({
                            ...queryParams,
                            pageIndex: page,
                            pageSize
                        });
                    }}
                />
            </BaseCard>
        </>
    );
};

export default Inspect;

/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 10:01:45
 * @LastEditTime: 2024-09-25 15:21:40
 * @LastEditors: 吴山仁
 */
import BaseButton from '@components/base-button';
import BaseCard from '@components/base-card';
import FilterForm from '@components/filter-form';
import PageTitle from '@components/page-title';
import { Form, message, Upload, Button, InputNumber } from 'antd';
import { useMutation, useQuery } from 'react-query';
import { updateFood, getFoodInfo, foodCategory, foodDetail } from '@services/food';
import React, { Component, useEffect, useRef, useState } from 'react';
import type { FormInstance } from 'antd/es/form';
import { addConfigs } from './config';
import styles from './index.module.less';
import { signData } from '../../utils/blockChainUtils';
import { useLocation, useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { ReformChainError } from '@utils/errorCodeReform';
import { getLocalPrivatekey } from '@utils/blockChainUtils';
import ImgCropUpload from '@components/img-upload';
import dayjs from 'dayjs';
import { fileUpload, getFileUrlFormUploadedFile, fileUrlsToUploadFileList } from '@utils';
import { UploadOutlined } from '@ant-design/icons';
import BaseSelect from '@components/base-select';
import { LandSourceService } from '@services/land-test';
import {
    purchasePageDetail,
    purchasePageEdit,
    plantNameByLandId,
    LandIdAndPlantName,
    purchaseCont
} from '@services/purchase';
import rsaEncrypt from '@utils/rsa';
const FoodInfoConfig = () => {
    const { state } = useLocation();
    console.log('state', state);
    const navigate = useNavigate();
    const [editEmployeesForm] = Form.useForm();
    const [uploadImageUrl, setUploadImageUrl]: any = useState('');
    const [n, setn] = useState(1);
    const [options, setOptions]: any = useState([]);
    const [isUpLoading, setIsUpLoading]: any = useState();
    const [fooddata, setfooddata]: any = useState('');

    const [productAptitude, setProductAptitude] = useState<any>();
    const [productImg, setProductImg] = useState<any>();
    const [productVideo, setProductVideo] = useState<any>();
    const [url, setUrl] = useState<any>();
    const imgUrlRef = useRef(true);
    const dispatch = useDispatch();
    //配置食品信息
    const purchaseEdit: any = useMutation(purchasePageEdit, {
        onSuccess(res) {
            message.success('修改成功');
            navigate('/purchase/list');
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });

    // useEffect(() => {
    //     foodcategory.mutate({
    //         n: n,
    //         name: fooddata
    //     });
    // }, [fooddata]);

    //食品品类
    // const foodcategory = useMutation(foodCategory, {
    //     onSuccess(res) {
    //         // getLocalPrivatekey(dispatch);
    //         // @ts-ignore
    //         const mapOptions = (items: any[]) => {
    //             return items.map((item) => {
    //                 if (item.childrenCategory) {
    //                     return {
    //                         label: item.categoryName,
    //                         // value: item.id,
    //                         value: item.categoryName,
    //                         children: mapOptions(item.childrenCategory)
    //                     };
    //                 } else {
    //                     return {
    //                         label: item.categoryName,
    //                         value: item.id
    //                     };
    //                 }
    //             });
    //         };
    //         setOptions(mapOptions(res?.data || []));
    //     },
    //     onError(err: any) {
    //         ReformChainError(err);
    //     }
    // });

    // 获取地块名
    // const landProductSele = useQuery(['landProductSele'], () => LandSourceService());

    // const landProductListData: any[] = landProductSele?.data?.data;
    const onRemove = () => {
        return imgUrlRef;
    };
    const getfoodInfo = useQuery(
        ['getfoodInfo'],
        () => {
            return purchasePageDetail({
                productId: state.id
            });
        },
        {
            async onSuccess(res) {
                const formInfo = res?.data || {};
                // console.log(dayjs(formInfo.purchaseTime).format('YYYY-MM-DD'));
                editEmployeesForm.setFieldsValue({
                    // ...formInfo,
                    farmerName: formInfo.farmerName,
                    landName: formInfo.landName,
                    phoneNumber: formInfo.phoneNumber,
                    plantBatch: formInfo.plantBatch,
                    bagCount: formInfo.bagCount,
                    materialPurchaseCount: formInfo.materialPurchaseCount,
                    purchaseBatch: formInfo.purchaseBatch,
                    // purchaseTime: formInfo.purchaseTime,
                    purchaseTime: dayjs(dayjs(formInfo.purchaseTime).format('YYYY-MM-DD HH:mm:ss')),
                    landType: formInfo.plantName,
                    purchaseWeight: formInfo.purchaseWeight,
                    purchaseUnitPrice: formInfo.purchaseUnitPrice,
                    userName: formInfo.userName
                });
                // const productAptitude = await fileUrlsToUploadFileList([formInfo.productAptitude]);
                // const productImg = await fileUrlsToUploadFileList(formInfo.productImg);
                // const productVideo = await fileUrlsToUploadFileList([formInfo.productVideo]);
                // setUrl(productVideo[0].response.fileUrl);
                // editEmployeesForm.setFieldsValue({
                //     ...formInfo,
                //     productCategory: formInfo?.productCategory && formInfo.productCategory.split('<'),
                //     productAptitude: formInfo?.productAptitude && productAptitude ? productAptitude : undefined,
                //     productImg: formInfo?.productImg && productImg ? productImg : undefined,
                //     productVideo: formInfo?.productVideo && productVideo ? productVideo : undefined
                // });
            }
        }
    );
    const [purchaseTimesId, setPurchaseTimesId] = useState(null);
    const purchaseTimeData = useQuery(
        ['purchaseCont', editEmployeesForm.getFieldsValue().farmerName, editEmployeesForm.getFieldsValue().phoneNumber],
        async () => {
            return purchaseCont({
                farmerName: editEmployeesForm.getFieldsValue().farmerName,
                phoneNumber: await rsaEncrypt(String(editEmployeesForm.getFieldsValue().phoneNumber))
            });
        },
        {
            async onSuccess(res) {
                editEmployeesForm.setFieldsValue({
                    materialPurchaseCount: res.data || 0
                });
            }
        }
    );

    // 异步函数来确保数据准备好后再更新表单
    const updateMaterialPurchaseCount = async () => {
        if (purchaseTimeData.isSuccess) {
            // 确保数据已经准备好
            editEmployeesForm.setFieldsValue({
                materialPurchaseCount: purchaseTimeData.data?.data || 0
            });
        }
    };
    const addBasicInfoConfigs = [
        {
            type: 'Input',
            label: '农户姓名',
            value: 'farmerName',
            maxLength: 30,
            disable: true,
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,30}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入农户姓名!');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            if (value.length > 30) {
                                callback('请保持字符在30字符以内!');
                            } else {
                                callback('请输入农户姓名，支持中文、字母或数字!');
                            }
                        } else {
                            console.log(111);
                            editEmployeesForm.setFieldsValue({
                                materialPurchaseCount: purchaseTimeData.data?.data || 0
                            });
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入农户姓名',
            onBlur: (e: any) => {
                setPurchaseTimesId(e.target.value);
                editEmployeesForm.setFieldsValue({
                    farmerName: e.target.value
                });
                console.log(purchaseTimeData.data?.data, '1111111111111111111');
                editEmployeesForm.setFieldsValue({
                    materialPurchaseCount: purchaseTimeData.data?.data || 0
                });

                // 这里是失去焦点时触发的事件处理器
                // const value = e.target.value;
                // if (!value) {
                //     // 可以在这里触发Ant Design Form的验证
                //     form.validateFields(['phoneNumber']).catch(() => {});
                // }
            }
        },
        {
            label: '联系方式',
            type: 'Input',
            value: 'phoneNumber',
            disable: true,
            placeholder: '请输入联系方式!',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^(?:(?:\+|00)86)?1[3-9]\d{9}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入联系方式!');
                        } else if (verify === false) {
                            callback('请输入正确的手机号!');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            onBlur: (e: any) => {
                // editEmployeesForm.setFieldsValue({
                //     phoneNumber: e.target.value
                // });
                setPurchaseTimesId(e.target.value);
                editEmployeesForm.setFieldsValue({
                    phoneNumber: e.target.value
                });
                // 可以在这里触发Ant Design Form的验证
                editEmployeesForm.setFieldsValue({
                    materialPurchaseCount: purchaseTimeData.data?.data || 0
                });
            }
        },
        {
            type: 'Input',
            label: '被收购次数',
            value: 'materialPurchaseCount',
            maxLength: 30,
            disable: true,
            rules: [{ required: true, message: '' }],
            placeholder: ''
        }
    ];

    const [selectedLandId, setSelectedLandId] = useState(null);
    // 种植批次;
    const [selectedLandBatch, setSelectedLandBatch] = useState(null);
    // 获取地块名
    const landProductSele = useQuery(['landProductSele'], () => LandSourceService());

    const landProductListData: any[] = landProductSele?.data?.data;
    // 获取农作物类型
    const plantNameByLandIdSele = useQuery(
        ['plantNameByLandId', selectedLandId],
        () => {
            return plantNameByLandId({
                landId: selectedLandId
            });
        },
        {
            enabled: Boolean(selectedLandId) // 只有当selectedLandId有值时才启用查询
        }
    );

    const plantNameByLandIdData = plantNameByLandIdSele?.data?.data;
    // 获取种植批次
    const plantNameByLandIdSeleName = useQuery(
        ['LandIdAndPlantName', selectedLandBatch],
        () => {
            return LandIdAndPlantName({
                landId: selectedLandId,
                plantName: selectedLandBatch
            });
        },
        {
            enabled: Boolean(selectedLandBatch) // 只有当selectedLandId有值时才启用查询
        }
    );

    const plantNameByLandIdNameData = plantNameByLandIdSeleName?.data?.data;

    const addIntroductionConfigs = [
        {
            label: '地块名称',
            value: 'landName',
            type: 'Custom',
            rules: [{ required: true, message: '请选择地块名称!' }],
            children: (
                <BaseSelect
                    // defaultValue={1}
                    placeholder='请选择地块名称'
                    options={landProductListData?.map((item) => ({
                        label: item?.landName,
                        value: item?.landId
                    }))}
                    onChange={(value) => {
                        if (value) {
                            setSelectedLandId(value);
                            editEmployeesForm.setFieldsValue({ landType: undefined });
                            editEmployeesForm.setFieldsValue({ plantBatch: undefined });
                        } else {
                            // setData2([]);
                            // editEmployeesForm.setFieldsValue('landType');
                        }
                    }}
                ></BaseSelect>
            )
        },

        {
            label: '农作物类型',
            value: 'landType',
            type: 'Custom',
            rules: [{ required: true, message: '请选择农作物类型!' }],
            children: (
                <BaseSelect
                    // defaultValue={1}
                    placeholder='请选择农作物类型'
                    options={plantNameByLandIdData?.map((item: any) => ({
                        label: item?.plantName,
                        value: item?.plantName
                    }))}
                    onChange={(value) => {
                        if (value) {
                            setSelectedLandBatch(value);
                            editEmployeesForm.setFieldsValue({ plantBatch: undefined });
                        } else {
                            // setData2([]);
                            // editEmployeesForm.setFieldsValue('landType');
                        }
                    }}
                    disabled={!selectedLandId}
                ></BaseSelect>
            )
        },
        {
            label: '种植批次',
            value: 'plantBatch',
            type: 'Custom',
            rules: [{ required: true, message: '请选择种植批次!' }],
            children: (
                <BaseSelect
                    // defaultValue={1}
                    placeholder='请选择种植批次'
                    options={plantNameByLandIdNameData?.map((item: any) => ({
                        label: item?.plantBatch,
                        value: item?.plantBatch,
                        disabled: item?.state == 1 // 添加禁用逻辑
                    }))}
                    onChange={(value) => {
                        console.log(value);
                    }}
                    disabled={!selectedLandBatch}
                ></BaseSelect>
            )
        },
        {
            type: 'Input',
            label: '收购对接人',
            value: 'userName',
            placeholder: '请输入收购对接人',
            maxLength: 30,
            disable: true,
            rules: [{ required: true, message: '请输入收购对接人!' }]
        },
        {
            type: 'Input',
            label: '收购批次',
            maxLength: 50,
            value: 'purchaseBatch',
            placeholder: '请输入收购批次',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,50}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入收购批次!');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            if (value.length > 50) {
                                callback('请保持字符在50字符以内!');
                            } else {
                                callback('请输入收购批次，支持中文、字母或数字!');
                            }
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            type: 'DatePicker',
            label: '收购时间',
            value: 'purchaseTime',
            placeholder: ['请选择收购时间'],
            wide: '100%',
            showTime: true,
            rules: [{ required: true, message: '请选择收购时间!' }]
        },
        {
            type: 'Custom',
            label: '收购重量',
            value: 'purchaseWeight',

            rules: [{ required: true, message: '请输入收购重量!' }],
            children: (
                <InputNumber
                    addonAfter='吨'
                    placeholder='请输入收购重量'
                    min={0}
                    max={100000}
                    step={0.01} // 设置步长为0.01确保每次改变都是0.01的倍数
                    precision={2} // 限制输入数值保留两位小数
                    style={{ width: '100%', height: '34px' }}
                    onChange={(value) => {
                        // 可以在此处添加额外的验证逻辑，例如处理非数字输入或超过预期范围的值
                    }}
                />
            )
        },
        {
            type: 'Custom',
            label: '装袋数量',
            value: 'bagCount',
            rules: [
                { required: true, message: '请输入装袋数量!' },
                {
                    validator: (_: any, value: any) => {
                        if (value === 0 || value === '0') {
                            return Promise.reject(new Error('装袋数量必须大于0'));
                        }
                        return Promise.resolve();
                    }
                }
            ],
            children: (
                <InputNumber
                    addonAfter='袋'
                    placeholder='请输入装袋数量'
                    min={0}
                    max={10000000}
                    step={0.1} // 设置步长为0.01确保每次改变都是0.01的倍数
                    precision={1} // 限制输入数值保留两位小数
                    style={{ width: '100%', height: '34px' }}
                    onChange={(value) => {
                        // 可以在此处添加额外的验证逻辑，例如处理非数字输入或超过预期范围的值
                    }}
                />
            )
        },
        {
            type: 'Custom',
            label: '收购单价',
            value: 'purchaseUnitPrice',

            rules: [{ required: true, message: '请输入收购单价!' }],
            children: (
                <InputNumber
                    addonAfter='元/吨'
                    placeholder='请输入收购单价'
                    min={0}
                    max={100000}
                    step={0.01} // 设置步长为0.01确保每次改变都是0.01的倍数
                    precision={2} // 限制输入数值保留两位小数
                    style={{ width: '100%', height: '34px' }}
                    onChange={(value) => {
                        // 可以在此处添加额外的验证逻辑，例如处理非数字输入或超过预期范围的值
                    }}
                />
            )
        }
    ];

    const onFinish = (values: any) => {
        console.log(values);
        if (isUpLoading) {
            message.warning('正在上传文件请稍等～');
            return;
        }
        const params: any = {
            id: state.id,
            farmerName: values.farmerName,
            // landName: values.landName,
            // landType: values.landType,
            // productCategory: values.productCategory.join('>'),
            phoneNumber: values.phoneNumber,
            plantBatch: values.plantBatch,
            userName: values.userName,
            purchaseBatch: values.purchaseBatch,
            purchaseWeight: values.purchaseWeight,
            purchaseUnitPrice: values.purchaseUnitPrice,
            purchaseTime: values.purchaseTime,
            bagCount: values.bagCount
            // productImg: getFileUrlFormUploadedFile(values?.productImg),
            // productVideo: url
        };
        console.log(values.purchaseTime);
        const paramStr = JSON.stringify(params);
        signData(dispatch, JSON.stringify(params), (error, result: any) => {
            if (!error && result) {
                purchaseEdit.mutate({
                    modifyMaterialPurchaseVo: params,
                    paramStr: paramStr,
                    signature: result
                });
            } else if (error !== 'misprivatekey') {
                message.info('签名异常，请重试或联系管理员');
            }
        });
        // const paramStr = JSON.stringify(params);
        // signData(dispatch, JSON.stringify(params), (error, result: any) => {
        //     if (!error && result) {
        //         params['signature'] = result;
        //         params['paramStr'] = paramStr;
        //         console.log('addfood.mutate(): ', error, result);
        //         console.log('params7777777777', params);
        //         updatefood.mutate(params);
        //     } else if (error !== 'misprivatekey') {
        //         message.info('签名异常，请重试或联系管理员');
        //     }
        // });
    };

    return (
        <BaseCard title={<PageTitle title='编辑收购信息' bg='container chan' />}>
            <Form onFinish={onFinish} form={editEmployeesForm} className='edit-label-title'>
                <PageTitle title='农户信息' type='primaryIcon' bmagin={16} />
                <FilterForm itemConfig={addBasicInfoConfigs} labelCol={3} wrapperCol={9} />
                <PageTitle title='收购信息' type='primaryIcon' bmagin={16} />
                <FilterForm itemConfig={addIntroductionConfigs} labelCol={3} wrapperCol={9} />

                <div className={styles.addBtnContainer}>
                    <Form.Item className={styles.saveBtn}>
                        <BaseButton type='primary' htmlType='submit' className={styles.submitBtn}>
                            保存
                        </BaseButton>
                    </Form.Item>
                    <Form.Item>
                        <BaseButton
                            htmlType='button'
                            // onClick={this.onReset}
                            type='dashed'
                            className={styles.primaryBtn}
                            onClick={() => {
                                navigate('/purchase/list');
                            }}
                        >
                            取消
                        </BaseButton>
                    </Form.Item>
                </div>
            </Form>
        </BaseCard>
    );
};
// }

export default FoodInfoConfig;

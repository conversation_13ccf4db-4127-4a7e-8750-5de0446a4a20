import { useRef, useState } from 'react';
import { Col, Form, message, Row, Space, Badge, InputNumber } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';
import WithPaginate from '../../hoc/withpaginate';

import BaseCard from '@components/base-card';
import BaseModal from '@components/base-modal';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';
import BaseInput from '@components/base-input/base-input';
import BaseSelect from '@components/base-select/base-select';
import BaseDatePicker from '@components/base-date-picker';
import SingleSearch from '@components/single-search/single-search';

import styles from './index.module.less';
import { LocalLoginIdentity, RoleEnum } from '@config';
import { useAppDispatch, useAppSelector } from '@store';
import { traceCodePage } from '@services/trace-source-code';
import { ReformChainError } from '@utils/errorCodeReform';

interface IUrlState {
    pageIndex: number;
    pageSize: number;
    // ...
}

const TraceCode = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;

    const userInfo = useAppSelector((store) => store.user);

    const [visible, setVisible] = useState(false);

    const querylist: any = useRef('');

    const [form] = Form.useForm();
    const navigate = useNavigate();

    const traceCodeQuery = useQuery(
        ['traceCodeQuery', pageInfo],
        () =>
            traceCodePage({
                code: querylist?.current?.code?.trim() || undefined,
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize
            }),
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );

    console.log(traceCodeQuery, 'queryInspect queryInspect');

    const coreEnterpriseColumns = () => {
        return [RoleEnum.平台方]?.includes(RoleEnum[userInfo.role])
            ? [
                  {
                      title: '生产加工企业',
                      dataIndex: 'shortName',
                      key: 'shortName',
                      ellipsis: true
                  }
              ]
            : [];
    };
    const columns: ColumnsType<any> = [
        {
            title: '溯源码',
            dataIndex: 'code',
            key: 'code4',
            ellipsis: true
        },
        {
            title: '所属码包',
            dataIndex: 'packNumber',
            key: 'packNumber',
            ellipsis: true
        },
        {
            title: '生码时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true,
            render: (_, row) => dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss')
        },
        {
            title: '所属产品',
            dataIndex: 'productName',
            key: 'productName',
            ellipsis: true
        },
        {
            title: '生产批次',
            dataIndex: 'productionBatch',
            key: 'productionBatch',
            ellipsis: true
        },
        ...coreEnterpriseColumns(),
        {
            title: '单码扫码限制',
            dataIndex: 'maxQuantity',
            key: 'maxQuantity',
            width: 130,
            ellipsis: true,
            render: (_, row) => <span>{row.maxQuantity ? row.maxQuantity + '次' : '-'}</span>
        },
        {
            title: '查询次数',
            dataIndex: 'searchCount',
            key: 'searchCount',
            ellipsis: true
        },
        {
            title: '扫码状态',
            dataIndex: 'state',
            key: 'state',
            ellipsis: true,
            render: (state, row) => (
                <Badge
                    status={row.maxQuantity == 0 ? 'success' : row.maxQuantity >= row.searchCount ? 'success' : 'error'}
                    text={row.maxQuantity == 0 ? '正常' : row.maxQuantity >= row.searchCount ? '正常' : '异常'}
                />
            )
        },
        {
            title: '可用状态',
            dataIndex: 'state',
            key: 'state',
            ellipsis: true,
            render: (state, row) => <Badge status={state ? 'error' : 'success'} text={state ? '禁用' : '可用'} />
        },
        {
            title: '操作',
            render: (_, row) => (
                <BaseButton
                    type='dashed'
                    className='primaryBtn'
                    onClick={() => {
                        navigate('detail', {
                            state: {
                                id: row.id
                            }
                        });
                    }}
                >
                    查看详情
                </BaseButton>
            )
        }
    ];

    return (
        <BaseCard className={styles.coreFIrmContainer} title={<PageTitle title='溯源码列表' bg='container su' />}>
            <div style={{ display: 'flex', justifyContent: 'end', marginBottom: '20px' }}>
                <SingleSearch
                    setSearchValue={(values: any) => {
                        handlePaginationChange(1);
                        querylist.current = {
                            code: values
                        };
                        traceCodeQuery.refetch();
                    }}
                    handleSearch={() => {}}
                    placeholder='输入溯源码进行精确查询'
                />
            </div>
            <BaseTable
                loading={traceCodeQuery.isFetching}
                columns={columns}
                dataSource={traceCodeQuery?.data?.data?.records}
            />
            <BasePagination
                shouldShowTotal
                showQuickJumper
                showSizeChanger
                current={pageInfo.pageIndex}
                pageSize={pageInfo.pageSize}
                total={traceCodeQuery?.data?.data?.total}
                onShowSizeChange={handlePaginationChange}
                onChange={handlePaginationChange}
            />
        </BaseCard>
    );
};

export default WithPaginate(TraceCode);

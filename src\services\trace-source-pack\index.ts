/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-11-01 18:28:32
 * @LastEditTime: 2022-11-01 18:28:32
 * @LastEditors: PhilRandWu
 */
import request2 from '../request2';
import request from '../request';
//溯源码分页接口
export const sourcePackPage = (obj: any) => {
    return request({
        url: '/trace-source-pack/sourcePackPage',
        method: 'post',
        data: obj
    });
};
//溯源码修改状态
export const modifySourcePackState = (obj: any) => {
    return request({
        url: `/trace-source-pack/modifySourcePackState?state=${obj.state}&tracePackId=${obj.tracePackId}`,
        method: 'post',
        data: obj
    });
};
//食品列表
export const canInFoodList = (obj: any) => {
    return request({
        url: '/storage-in/canInFoodList',
        method: 'post',
        data: obj
    });
};
//生产批次列表
export const productionBatchList = (obj: any) => {
    return request({
        url: `/production/productionBatchList?foodId=${obj.foodId}`,
        method: 'post',
        data: obj
    });
};
//下载码包
export const download = (obj: any) => {   
        return request2({
            // responseType: 'blob',
            url: `/trace-source-pack/download?tracePackId=${obj.tracePackId}`,
            method: 'get',
            data: obj
        });
};
//新增溯源码包
export const addSourcePack = (obj: any) => {
    return request2({
        url: '/trace-source-pack/addSourcePack',
        method: 'post',
        data: obj
    });
};

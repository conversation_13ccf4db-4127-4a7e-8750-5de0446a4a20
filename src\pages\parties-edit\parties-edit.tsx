/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-21 10:24:01
 * @LastEditTime: 2022-11-02 16:40:52
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import './index.less';
import PageTitle from '@components/page-title';
import { Form, Button, message, Input } from 'antd';
import BaseModal from '@components/base-modal';
import { randomPassword } from '@utils';
import { ReformChainError } from '@utils/errorCodeReform';
import { updateParticipant, addAdmin, updateUser } from '@services/participantPage';
import { resetPassWord } from '@services/user';
import copyToClipboard from 'copy-to-clipboard';
import { managerConfig } from './config';
import BaseButton from '@components/base-button';
import BaseFormItem from '@components/base-form-item';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQuery } from 'react-query';
import { useState, useEffect, useRef } from 'react';
import sha256 from 'crypto-js/sha256';
import FilterForm from '@components/filter-form';
import { useLocation } from 'react-router-dom';
import platformIcon from '@assets/icon/canyufang.png';
import { CheckCircleFilled } from '@ant-design/icons';
import rsaEncrypt from '@utils/rsa';
const CoreFlrm = () => {
    const { state } = useLocation();
    const navigate = useNavigate();
    console.log('state', state);
    const passwordRef: any = useRef();
    const [resetFrom] = Form.useForm();
    const [addModalVisible, setAddModelVisible] = useState(false);
    const [successModalVisible, setsuccessModalVisible] = useState(false);
    const [addEmployeesForm] = Form.useForm();
    const [partiesEdit, setpartiesEdit] = useState(false);
    const [managerEdit, setmanagerEdit] = useState(false);
    const [permissions, setpermissions] = useState(false);
    const [editor, seteditor] = useState(false);
    const [partiesForm] = Form.useForm();
    const [partiesForm_2] = Form.useForm();
    const [managerForm] = Form.useForm();
    const [resetPass, setresetPass] = useState('');
    const [resetPswVisible, setresetPswVisible] = useState(false);
    let editortext = state;
    //重置密码接口
    const resetpassword = useMutation(resetPassWord, {
        onSuccess(res) {
            message.success('重置密码成功');
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    const addadmin = useMutation(addAdmin, {
        onSuccess(res) {
            setsuccessModalVisible(true);
            message.success('设置管理员成功');
            setAddModelVisible(false);
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    // console.log('addadmin', addadmin);
    //编辑参与方
    const partmodiy = useMutation(updateParticipant, {
        onSuccess(res) {
            message.success('编辑成功');
            state.data.remark = editortext.permission;
            state.data.name = editortext.name;
            setpartiesEdit(false);
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    const updateuser = useMutation(updateUser, {
        onSuccess(res) {
            message.success('编辑成功');
            state.data.managerAccount = editortext.managerAccount;
            state.data.manager = editortext.managerName;
            state.data.phone = editortext.phone;
            setmanagerEdit(false);
            setpermissions(true);
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    const partiesdisplayConfig = [
        {
            label: '参与方名称',
            name: 'name',
            type: 'Input',
            value: 'name',
            placeholder: '请输入',
            displayDom: state.data.name,
            className: 'participants',
            size: -8
        },
        {
            label: '参与方类型',
            name: 'type',
            type: 'Radio',
            value: 'type',
            disable: 'string',
            placeholder: '请选择',
            displayDom: state.data.type,
            size: -8,
            fields: [
                {
                    value: 2,
                    label: '供应商'
                },
                {
                    value: 3,
                    label: '质检机构'
                },
                {
                    value: 4,
                    label: '监管机构'
                }
            ]
        },
        {
            label: '备注',
            name: 'note',
            showCount: true,
            type: 'TextArea',
            value: 'permission',
            // placeholder: '请选择',
            displayDom: state.data.remark,
            wide: 280,
            size: -8
        }
    ];
    const partiesConfig = [
        {
            label: '参与方名称',
            name: 'name',
            type: 'Input',
            value: 'name',
            placeholder: '请输入',
            displayDom: state.data.name,
            className: 'participants',
            size: -8,
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,40}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入参与方名称');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            if (value.length > 40) {
                                callback('请保持字符在40字符以内!');
                            } else {
                                callback('请输入参与方名称，支持中文、字母或数字!');
                            }
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            label: '参与方类型',
            name: 'type',
            type: 'Radio',
            value: 'type',
            disable: 'string',
            placeholder: '请选择',
            displayDom: state.data.type,
            size: -8,
            fields: [
                {
                    value: 2,
                    label: '供应商'
                },
                {
                    value: 3,
                    label: '质检机构'
                },
                {
                    value: 4,
                    label: '监管机构'
                }
            ]
        },
        {
            label: '备注',
            name: 'note',
            showCount: true,
            type: 'TextArea',
            value: 'permission',
            placeholder: '请输入',
            displayDom: state.data.remark,
            wide: 280,
            size: -8
        }
    ];

    useEffect(() => {
        if (state.data.manager !== null) {
            setpermissions(true);
            seteditor(true);
        }
        let type = 0;
        if (state.data.type === '供应商') {
            type = 2;
        }
        if (state.data.type === '质检机构') {
            type = 3;
        }
        if (state.data.type === '监管机构') {
            type = 4;
        }
        partiesForm.setFieldsValue({
            name: state.data.name,
            type: type,
            permission: state.data.remark
        });
        partiesForm_2.setFieldsValue({
            managerAccount: state.data.managerAccount,
            managerName: state.data.manager,
            phone: state.data.phone
        });
    }, []);
    //管理员列表
    const managerDisplayConfig = [
        {
            label: '管理员名称',
            name: 'managerName',
            displayDom: state.data.manager
        },
        {
            label: '联系方式',
            name: 'phone',
            displayDom: state.data.phone
        },
        {
            label: '管理员账号',
            name: 'managerAccount',
            displayDom: state.data.managerAccount
        }
    ];
    //增加
    const addEmployeesConfigs = [
        {
            label: '管理员名称',
            type: 'Input',
            value: 'name',
            placeholder: '请输入管理员名称',
            className: 'manaName',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,30}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入管理员名称');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            if (value.length >= 30) {
                                callback('请保持字符在30字符以内!');
                            } else {
                                callback('请输入管理员名称，支持中文、字母或数字!');
                            }
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            label: '联系方式',
            type: 'Input',
            value: 'contact',
            placeholder: '请输入',
            className: 'phone',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^(?:(?:\+|00)86)?1[3-9]\d{9}$/);
                        const verify = regExp.test(value);
                        if (verify === false) {
                            callback('请输入正确的手机号');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        }
    ];
    console.log('ppq777777', addEmployeesConfigs);
    const addEmployeesConfig = {
        okText: '创建管理员',
        title: '设置管理员',
        visible: addModalVisible,
        setVisible: setAddModelVisible,
        okHandle: async () => {
            const values = await addEmployeesForm.validateFields();
            console.log('values', values);
            passwordRef.current = randomPassword(8);
            addadmin.mutate({
                companyId: state.data.companyId,
                userName: values.name,
                telephone: await rsaEncrypt(values.contact),
                password: await rsaEncrypt(passwordRef.current)
            });
            // addEmployeesForm.resetFields();
        },
        onCancelHandle: () => {
            setAddModelVisible(false);
            addEmployeesForm.resetFields();
        }
    };
    const successConfigs = [
        {
            label: '管理员名称',
            value: 'managername',
            placeholder: '请输入管理员名称',
            className: 'manaName',
            rules: [{ required: true, message: '请输入管理员名称!' }],
            display: addadmin?.data?.data?.userName
        },
        {
            label: '联系方式',
            value: 'phone',
            placeholder: '请输入',
            className: 'phone',
            rules: [{ required: true, message: '请输入联系方式' }],
            display: addadmin?.data?.data?.telephone.slice(0, 3) + 'xxxxxxxx'
        },
        {
            label: '管理员账号',
            value: 'manageraccount',
            placeholder: '请输入',
            className: 'phone',
            rules: [{ required: true, message: '请输入联系方式' }],
            display: addadmin?.data?.data?.account
        },
        {
            label: '登录密码',
            value: 'password',
            placeholder: '请输入',
            className: 'phone',
            rules: [{ required: true, message: '请输入联系方式' }],
            display: '. . . . . . . . . .'
        }
    ];
    const successConfig = {
        okText: '复制密码',
        title: '创建成功',
        hidden: true,
        dispaly: 'true',
        visible: successModalVisible,
        setVisible: setsuccessModalVisible,
        okHandle: async () => {
            const values = await addEmployeesForm.getFieldsValue();
            setsuccessModalVisible(false);
            const username: any = {
                // account: addadmin?.data?.data?.account,
                password: passwordRef.current    //断点加密
            };
            const copyRet = copyToClipboard(`${username.password}`);
            copyRet ? message.success('复制成功') : message.error('复制失败');
            addEmployeesForm.resetFields();
            navigate('/parties/manage');
        },
        onCancelHandle: () => {
            setsuccessModalVisible(false);
            navigate('/parties/manage');
        }
    };
    //参与方信息
    const onFinish = (values: any) => {
        editortext = values; //在本页暂存修改信息
        partmodiy.mutate({
            companyId: state.data.companyId,
            shortName: values.name,
            type: values.type,
            remark: values.permission
        });
    };

    const onFinish_2 = async (values: any) => {
        editortext = values; //在本页暂存修改信息
        updateuser.mutate({
            userId: values.managerAccount,
            userName: values.managerName,
            telephone: await rsaEncrypt(values.phone)
        });
    };

    const onFinishFailed = (errorInfo: any) => {
        console.log('Failed:', errorInfo);
    };
    const goadministrator = () => {
        setAddModelVisible(true);
    };
    const resetPasswordConfig = {
        title: '重置密码',
        visible: resetPswVisible,
        setVisible: setresetPswVisible,
        okHandle: async () => {
            resetpassword.mutate({
                userId: state.data.managerAccount,
                password: await rsaEncrypt(resetPass)
            });
            setresetPass('');
            setresetPswVisible(false);
        },
        onCancelHandle: () => {
            setresetPass('');
            setresetPswVisible(false);
        }
    };
    return (
        <div className='edit'>
            <BaseCard
                title={
                    <>
                        <PageTitle title='编辑参与方' />
                    </>
                }
            >
                {/* <Form
                name="basic"
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                autoComplete="off"
            > */}
                <div className='titleHead blueBgTitle'>
                    <PageTitle title='参与方信息' type='primaryIcon' />
                    {partiesEdit ? (
                        <></>
                    ) : (
                        <a
                            className='editBtn'
                            onClick={() => {
                                setpartiesEdit(true);
                            }}
                        >
                            编辑
                        </a>
                    )}
                </div>
                <Form form={partiesForm} onFinish={onFinish} labelAlign={'left'} style={{ marginLeft: 20 }}>
                    {partiesEdit ? (
                        <>
                            <FilterForm labelCol={2} wrapperCol={0} itemConfig={partiesConfig} />
                            <Button className='submitBtn' type='primary' style={{ width: 89 }} htmlType='submit'>
                                保存
                            </Button>
                            <Button
                                className='cancelBtn'
                                style={{ width: 89 }}
                                onClick={() => {
                                    setpartiesEdit(false);
                                }}
                            >
                                取消
                            </Button>
                        </>
                    ) : (
                        <BaseFormItem configs={partiesdisplayConfig} />
                    )}
                </Form>

                <div className='titleHead blueBgTitle'>
                    <PageTitle title='管理员信息' type='primaryIcon' />
                    {permissions ? (
                        <>
                            <a
                                className='editBtn'
                                onClick={() => {
                                    setpermissions(false);
                                    setmanagerEdit(true);
                                    seteditor(true);
                                }}
                            >
                                编辑
                            </a>
                        </>
                    ) : (
                        <></>
                    )}
                </div>
                {editor ? (
                    <>
                        <Form form={partiesForm_2} onFinish={onFinish_2} labelAlign={'left'} style={{ marginLeft: 15 }}>
                            {managerEdit ? (
                                <>
                                    <FilterForm labelCol={2} wrapperCol={6} itemConfig={managerConfig} />
                                    <div>
                                        <Form.Item
                                            className='managerAccount'
                                            label='管理员账号'
                                            name='managerAccount'
                                            labelCol={{ span: 2 }}
                                            wrapperCol={{ span: 6 }}
                                        >
                                            <h3 className='text'>{state.data.managerAccount}</h3>
                                            <a
                                                className='reset'
                                                onClick={() => {
                                                    setresetPswVisible(true);
                                                }}
                                            >
                                                重置密码
                                            </a>
                                        </Form.Item>
                                    </div>
                                    <Button className='submitBtn_1' type='primary' htmlType='submit'>
                                        保存
                                    </Button>
                                    <Button
                                        className='cancelBtn_1'
                                        onClick={() => {
                                            setpermissions(true);
                                            setmanagerEdit(false);
                                        }}
                                    >
                                        取消
                                    </Button>
                                </>
                            ) : (
                                <>
                                    <BaseFormItem configs={managerDisplayConfig} />
                                    <a
                                        className='resetpassword'
                                        onClick={() => {
                                            setresetPswVisible(true);
                                        }}
                                    >
                                        重置密码
                                    </a>
                                </>
                            )}
                        </Form>
                    </>
                ) : (
                    <div className='noAdminContainer'>
                        <img src={platformIcon} alt='logo' />
                        <span>暂无管理员...</span>
                        <Button type='link' onClick={goadministrator}>
                            <span className='setAdminBtn'>设置管理员</span>
                        </Button>
                    </div>
                )}

                {/* </Form> */}
            </BaseCard>
            <BaseModal
                centered
                {...resetPasswordConfig}
                width={515}
                style={{ height: 411 }}
                cancelText='取消'
                okText='确认'
            >
                <Form
                    name='resetpassword'
                    labelCol={{
                        span: 5
                    }}
                    wrapperCol={{
                        span: 14
                    }}
                    autoComplete='off'
                    form={resetFrom}
                >
                    <Form.Item
                        label='重置密码'
                        name='password'
                        wrapperCol={{ span: 20 }}
                        initialValue={''}
                        rules={[
                            {
                                required: true,
                                message: '请生成登录密码!'
                            }
                        ]}
                    >
                        <div className='resetContainer'>
                            <Input id='pswRef' value={resetPass} readOnly className='pswInput' />
                            <span
                                className={`${'btn'} ${'newPswBtn'} ${'active'}`}
                                onClick={() => {
                                    // createNewPsw.mutate();
                                    setresetPass(randomPassword(8));
                                }}
                            >
                                生成
                            </span>
                            <span
                                id='copyBtn'
                                className={resetPass ? `${'btn'} ${'active'}` : `${'btn'} ${'disable'}`}
                                data-clipboard-target='#pswRef'
                                onClick={() => {
                                    if (resetPass) {
                                        // console.log("password", pswRef.current.input.value, pswRef);
                                        const copyRet = copyToClipboard(resetPass);
                                        // console.log('copyRet', copyRet)
                                        copyRet ? message.success('复制成功') : message.error('复制失败');
                                    }
                                }}
                            >
                                复制
                            </span>
                        </div>
                    </Form.Item>
                </Form>
            </BaseModal>
            <BaseModal {...addEmployeesConfig}>
                <Form name='addEmployeesForm' form={addEmployeesForm} className='edit-label-title'>
                    {<FilterForm itemConfig={addEmployeesConfigs} />}
                </Form>
            </BaseModal>
            <BaseModal
                cancelButtonProps={{ style: { display: 'none' } }}
                className='employees_header'
                closeIcon={<CheckCircleFilled rev={undefined} className='employess_icon' />}
                {...successConfig}
            >
                <Form
                    name='addEmployeesForm'
                    form={addEmployeesForm}
                    labelAlign='left'
                    className='employess-label-title'
                >
                    {<FilterForm itemConfig={successConfigs} />}
                </Form>
            </BaseModal>
        </div>
    );
};

export default CoreFlrm;

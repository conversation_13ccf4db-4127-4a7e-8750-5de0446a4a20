import { useEffect, useState } from 'react';
import { Col, Form, message, Row, Space, Descriptions, Table } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useLocation, useNavigate } from 'react-router-dom';

import BaseCard from '@components/base-card';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';

import styles from './index.module.less';
import FilterForm from '@components/filter-form';
import { useForm } from 'antd/lib/form/Form';
import { useQuery } from 'react-query';
import { logisticDetail } from '@services/logistics';
import { ReformChainError } from '@utils/errorCodeReform';
import { getTransportationType } from '@pages/logistics-data-detail';
import ChainDetailModal from '@components/chain_detail_modal';
import dayjs from 'dayjs';

const IconStyle: React.CSSProperties = {
    display: 'inline-block',
    width: 16,
    height: 16,
    fontSize: 12,
    background: '#cecece',
    color: '#fff',
    borderRadius: 100,
    textAlign: 'center',
    margin: '0 6px 0 6px'
};

const LogisticsDetail = () => {
    const location = useLocation();

    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    const [clickTransactionId, setClickTransactionId] = useState<any>();

    useEffect(() => {}, []);

    console.log(location, 'locationlocation');

    const logicticdetailsQuery = useQuery(
        ['logicticdetailsQuery'],
        () =>
            logisticDetail({
                logisticsId: location?.state?.id
            }),
        {
            onSuccess(res) {
                LoadForm.setFieldValue('transactionId', res?.data?.transactionId);
                LoadForm.setFieldValue(
                    'transactionTime',
                    res?.data?.transactionTime ? dayjs(res?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss') : '-'
                );
                UnloadForm.setFieldValue('unloadTransactionId', res?.data?.unloadTransactionId);
                UnloadForm.setFieldValue(
                    'unloadTransactionTime',
                    res?.data?.unloadTransactionTime
                        ? dayjs(res?.data?.unloadTransactionTime).format('YYYY-MM-DD HH:mm:ss')
                        : '-'
                );
            },
            onError(err) {
                ReformChainError(err);
            }
        }
    );
    console.log(logicticdetailsQuery);

    const ChainDetailModalConfig = {
        transactionId: clickTransactionId,
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };

    const columns: ColumnsType<any> = [
        {
            title: '箱码',
            dataIndex: 'boxCode',
            key: 'boxCode',
            ellipsis: true
        },

        {
            title: '产品名称',
            dataIndex: 'productName',
            key: 'productName',
            ellipsis: true
        }
    ];
    const [UnloadForm] = useForm();
    const [LoadForm] = useForm();
    const onchainConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Custom',
            children: (
                <a
                    onClick={() => {
                        setClickTransactionId(logicticdetailsQuery?.data?.data?.transactionId);
                        setChainDetailModalVisible(true);
                    }}
                >
                    {logicticdetailsQuery?.data?.data?.transactionId}
                </a>
            )
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];
    const inchainConfig = [
        {
            label: '链上哈希',
            name: 'unloadTransactionId',
            value: 'unloadTransactionId',
            title: '信息的链上的哈希值',
            type: 'Custom',
            children: (
                <a
                    onClick={() => {
                        setClickTransactionId(logicticdetailsQuery?.data?.data?.unloadTransactionId);
                        setChainDetailModalVisible(true);
                    }}
                >
                    {logicticdetailsQuery?.data?.data?.unloadTransactionId}
                </a>
            )
        },
        {
            label: '上链时间',
            name: 'unloadTransactionTime',
            value: 'unloadTransactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];
    return (
        <BaseCard className={styles.coreFIrmContainer} title={<PageTitle title='运输单详情' />}>
            {location?.state?.type !== 2 ? (
                <Descriptions title={<PageTitle title='装货信息' type='primaryIcon' />}>
                    <Descriptions.Item label='运输单号'>
                        {logicticdetailsQuery?.data?.data?.transNumber || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label='装货地点'>
                        {logicticdetailsQuery?.data?.data?.loadingLocation || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label='运输方式'>
                        {getTransportationType(Number(logicticdetailsQuery?.data?.data?.transportationType))}
                    </Descriptions.Item>
                </Descriptions>
            ) : (
                <Descriptions title={<PageTitle title='运输单信息' type='primaryIcon' />}>
                    <Descriptions.Item label='运输单号'>
                        {logicticdetailsQuery?.data?.data?.transNumber || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label='物流企业'>
                        {logicticdetailsQuery?.data?.data?.loEnterprises || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label='物流单号'>
                        {logicticdetailsQuery?.data?.data?.loNumber || '-'}
                    </Descriptions.Item>
                </Descriptions>
            )}
            <Descriptions
                title={<PageTitle title='箱码信息' type='primaryIcon' />}
                style={{ marginTop: 36 }}
            ></Descriptions>
            <Table
                columns={columns}
                dataSource={logicticdetailsQuery?.data?.data?.boxTo}
                style={{ width: '50%', marginBottom: 24 }}
                pagination={false}
            />

            <div>
                <Form form={LoadForm}>
                    <PageTitle title='装货区块链信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={onchainConfig} labelCol={false} />
                </Form>
            </div>

            <br />

            <div>
                {location.state?.state !== 0 && location?.state?.type !== 2 ? (
                    <Descriptions title={<PageTitle title='卸货信息' type='primaryIcon' />}>
                        <Descriptions.Item label='卸货地点'>
                            {logicticdetailsQuery?.data?.data?.unloadingLocation || '-'}
                        </Descriptions.Item>
                    </Descriptions>
                ) : null}
                {location.state?.state !== 0 && location?.state?.type !== 2 ? (
                    <Form form={UnloadForm}>
                        <PageTitle title='卸货区块链信息' type='primaryIcon' bmagin={16} />
                        <FilterForm showMode itemConfig={inchainConfig} labelCol={false} />
                    </Form>
                ) : null}
            </div>
            <ChainDetailModal {...ChainDetailModalConfig} />
        </BaseCard>
    );
};

export default LogisticsDetail;

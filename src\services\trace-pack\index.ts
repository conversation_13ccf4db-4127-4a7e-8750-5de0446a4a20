import request from '../request';

// 溯源码分页
export const tracePackPage = (obj: any) => {
    return request({
        url: '/trace-pack/page',
        method: 'post',
        data: obj
    });
};

// 新建溯源码包
export const tracePackAdd = (obj: any) => {
    return request({
        url: '/trace-pack/add',
        method: 'post',
        data: obj
    });
};
//编辑溯源码包
export const tracePackEdit = (obj: any) => {
  return request({
      url: '/trace-pack/update',
      method: 'post',
      data: obj
  });
};
// 禁用启用
export const tracePackUpdate = (obj: any) => {
    return request({
        url: '/trace-pack/modify-state',
        method: 'post',
        data: obj
    });
};

// 下载码包
export const tracePackDownload = (obj: any) => {
    return request({
        url: '/trace-pack/download',
        method: 'get',
        params: obj
    });
};
/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-11 11:04:25
 * @LastEditTime: 2022-11-01 18:23:07
 * @LastEditors: PhilRandWu
 */
import BaseCard from '@components/base-card';
import BaseFormItem from '@components/base-form-item';
import BaseTable from '@components/base-table';
import PageTitle from '@components/page-title';
import { Form, Modal } from 'antd';
import { useMutation} from 'react-query';
import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { ReformChainError } from '@utils/errorCodeReform';
import BaseTooptip from '@components/base-tooltip';

import { traceMaterialDetail, traceProductionDetail, traceTestDetail } from '@services/trace-source-record';
import dayjs from 'dayjs';
// import {
//     SourceInfoConfig,
//     chainInfoConfig,
//     ProductInfoConfig,
//     QsqaInfoConfig,
//     ProductSourceInfoConfig
// } from './config';
import './index.less';

function SourceDataDetail() {
    const [SourceCodeForm] = Form.useForm();
    const [ChainForm] = Form.useForm();
    const [ProductForm] = Form.useForm();
    const [QsqaForm] = Form.useForm();
    const [addModalVisible, setAddModelVisible] = useState(false);

    const { state } = useLocation();
    console.log('state', state);

    //原料详情
    const traceMaterial = useMutation(traceMaterialDetail, {
        onSuccess(res) {
            // ChainForm.setFieldsValue({
            //     blockNum: res?.data?.purchaseDetailTo.blockNum,
            //     transactionId: res?.data?.purchaseDetailTo.transactionId,
            //     transactionTime: res?.data?.purchaseDetailTo.transactionTime
            //         ? dayjs(res?.data?.purchaseDetailTo.transactionTime).format('YYYY-MM-DD HH:mm:ss')
            //         : null
            // });
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    const SourceInfoConfig = [
        {
            label: '原料名称',
            name: 'materialName',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: traceMaterial?.data?.data?.purchaseDetailTo.materialName
        },
        {
            label: '数量',
            name: 'count',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: traceMaterial?.data?.data?.purchaseDetailTo.count
        },
        {
            label: '原料采购批次',
            name: 'batchNumber',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: traceMaterial?.data?.data?.purchaseDetailTo.batchNumber
        },
        {
            label: '规格',
            name: 'specification',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: traceMaterial?.data?.data?.purchaseDetailTo.specification
        },
        {
            label: '生产日期',
            name: 'productionDate',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: dayjs(traceMaterial?.data?.data?.purchaseDetailTo.productionDate).format('YYYY-MM-DD')
        },
        {
            label: '保质期',
            name: 'expirationDate',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: traceMaterial?.data?.data?.purchaseDetailTo.expirationDate
        },
        {
            type: 'Display',
            label: '生产批号',
            name: 'productionNumber',
            value: 'productionNumber',
            placeholder: '请输入',
            span: 8,
            show: traceMaterial?.data?.data?.purchaseDetailTo?.productionNumber ? null : '1',
            displayDom: traceMaterial?.data?.data?.purchaseDetailTo?.productionNumber
        },
        {
            type: 'Display',
            name: 'deliveryDate',
            label: '供货日期',
            value: 'deliveryDate',
            placeholder: '请输入',
            span: 8,
            show: traceMaterial?.data?.data?.purchaseDetailTo?.deliveryDate ? null : '1',
            displayDom: dayjs(traceMaterial?.data?.data?.purchaseDetailTo?.deliveryDate).format('YYYY-MM-DD')
        },
        {
            label: '产品合格证明材料',
            name: 'certificateQualification',
            value: '123',
            type: 'Url',
            span: 8,
            show: traceMaterial?.data?.data?.purchaseDetailTo?.certificateQualification ? null : '1',
            display: <a href={traceMaterial?.data?.data?.purchaseDetailTo?.certificateQualification}>{'下载'}</a>
        },
        {
            label: '附件',
            name: 'purchaseAccessory',
            value: '123',
            type: 'Url',
            span: 8,
            show: traceMaterial?.data?.data?.purchaseDetailTo?.purchaseAccessory ? null : '1',
            display: <a href={traceMaterial?.data?.data?.purchaseDetailTo?.purchaseAccessory}>{'下载'}</a>
        },
        {
            label: '生产批次',
            name: 'batch',
            value: '123',
            type: 'Modal',
            span: 8,
            tips: '点击查看'
        }
    ];
    //生产详情
    const traceProduction = useMutation(traceProductionDetail, {
        onSuccess(res) {},
        onError(err: any) {
            ReformChainError(err);
        }
    });
    const ProductInfoConfig = [
        {
            label: '食品名称',
            name: 'foodName',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: traceProduction?.data?.data?.foodName
        },
        {
            label: '生产批次',
            name: 'productionBatch',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: traceProduction?.data?.data?.productionBatch
        },
        {
            label: '生产日期',
            name: 'productionDate',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: dayjs(traceProduction?.data?.data?.productionDate).format('YYYY-MM-DD')
        },
        {
            label: '数量',
            name: 'count',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: traceProduction?.data?.data?.count||'-'
        },
        {
            label: '生产线',
            name: 'time',
            type: 'Display',
            value: 'productionLine',
            span: 8,
            show: traceProduction?.data?.data?.productionLine ? null : '1',
            displayDom: traceProduction?.data?.data?.productionLine||'-'
        },
        {
            type: 'Display',
            name: 'time',
            label: '种植户',
            value: 'productionShift',
            span: 8,
            show: traceProduction?.data?.data?.productionShift ? null : '1',
            displayDom: traceProduction?.data?.data?.productionShift||'-'
        },
        {
            type: 'Display',
            name: 'time',
            label: '生产地点',
            value: 'productionPlace',
            span: 8,
            show: traceProduction?.data?.data?.productionPlace ? null : '1',
            displayDom: traceProduction?.data?.data?.productionPlace||'-'
        },
        {
            type: 'Display',
            name: 'time',
            label: '生产环境信息',
            value: 'environmentInfo',
            span: 8,
            show: traceProduction?.data?.data?.environmentInfo ? null : '1',
            displayDom: traceProduction?.data?.data?.environmentInfo||'-'
        },
        {
            type: 'Display',
            name: 'time',
            label: '责任人员',
            value: 'personLiable',
            span: 8,
            show: traceProduction?.data?.data?.personLiable ? null : '1',
            displayDom: traceProduction?.data?.data?.personLiable||'-'
        },
        {
            type: 'Display',
            name: 'time',
            label: '联系电话',
            value: 'contactNumber',
            span: 8,
            show: traceProduction?.data?.data?.contactNumber ? null : '1',
            displayDom: traceProduction?.data?.data?.contactNumber||'-'
        },
        {
            type: 'DatePicker',
            name: 'time',
            label: '抽检时间',
            value: 'checkTime',
            span: 8,
            show: traceProduction?.data?.data?.checkTime ? null : '1',
            displayDom: dayjs(traceProduction?.data?.data?.checkTime).format('YYYY-MM-DD')||'-'
        },
        {
            type: 'Display',
            name: 'time',
            label: '抽检记录',
            value: 'checkRecord',
            span: 8,
            show: traceProduction?.data?.data?.checkRecord ? null : '1',
            displayDom: traceProduction?.data?.data?.checkRecord||'-'
        },
        {
            type: 'DatePicker',
            name: 'time',
            label: '留样时间',
            value: 'sampleTime',
            span: 8,
            show: traceProduction?.data?.data?.sampleTime ? null : '1',
            displayDom: dayjs(traceProduction?.data?.data?.sampleTime).format('YYYY-MM-DD')
        },
        {
            type: 'Display',
            name: 'time',
            label: '留样记录',
            value: 'sampleRecord',
            span: 8,
            show: traceProduction?.data?.data?.sampleRecord ? null : '1',
            displayDom: traceProduction?.data?.data?.sampleRecord
        },
        {
            label: '附件',
            name: 'time',
            value: '123',
            type: 'Url',
            span: 8,
            show: traceProduction?.data?.data?.productionAccessory ? null : '1',
            display:traceProduction?.data?.data?.productionAccessory ? <a href={traceProduction?.data?.data?.productionAccessory}>{'下载'}</a>:'-'
        }
    ];
    //质检详情
    const traceTest = useMutation(traceTestDetail, {
        onSuccess(res) {},
        onError(err: any) {
            ReformChainError(err);
        }
    });
    const chainInfoConfig = [
        // {
        //     label: '区块号',
        //     name: 'blockNum',
        //     value: '123',
        //     type: 'Display',
        //     span: 24,
        //     write: 'true',
        //     tooltip: '信息上链时所在的区块编号',
        //     displayDom:
        //         state?.data?.dataType === '原料'
        //             ? traceMaterial?.data?.data?.purchaseDetailTo?.blockNum
        //             : state.data.dataType === '生产'
        //             ? traceProduction?.data?.data?.blockNum
        //             : traceTest?.data?.data?.blockNum
        // },
        {
            label: '链上哈希',
            name: 'transactionId',
            value: '123',
            type: 'Display',
            span: 24,
            write: 'true',
            tooltip: '信息的链上的哈希值',
            displayDom:
                state?.data?.dataType === '原料'
                    ? traceMaterial?.data?.data?.purchaseDetailTo?.transactionId
                    : state.data.dataType === '生产'
                    ? traceProduction?.data?.data?.transactionId
                    : traceTest?.data?.data?.transactionId
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: '123',
            write: 'true',
            type: 'Display',
            span: 24,
            tooltip: '信息上链的时间',
            displayDom:
                state?.data?.dataType === '原料'
                    ? dayjs(traceMaterial?.data?.data?.purchaseDetailTo?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
                    : state.data.dataType === '生产'
                    ? dayjs(traceProduction?.data?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
                    : dayjs(traceTest?.data?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
        }
    ];
    useEffect(() => {
        if (state !== null) {
            if (state?.data?.dataType === '原料') {
                traceMaterial.mutate({
                    id: state?.data?.id
                });
            }
            if (state.data.dataType === '生产') {
                traceProduction.mutate({
                    id: state.data.id
                });
            }
            if (state.data.dataType === '质检') {
                traceTest.mutate({
                    id: state.data.id
                });
            }
        }
    }, [state?.data?.dataType]);
    const listColumn = [
        {
            title: '食品名称',
            dataIndex: 'foodName',
            key: 'foodName'
        },
        {
            title: '生产批次',
            dataIndex: 'batch',
            key: 'batch'
        }
    ];

    const listData = [
        ...(traceMaterial?.data?.data?.productionList || [])?.map((item: any, index: any) => {
            // console.log(7777777,item)
            const manager = {
                foodName: item.foodName,
                batch: item.productionBatch
            };
            return manager;
        })
    ];
    //原料中的生产信息
    const ProductSourceInfoConfig: any = [
        ...(traceProduction?.data?.data?.materialInfo || [])?.map((item: any, index: any) => {
            // console.log(7777777,item)
            const manager = {
                displayDom: item.purchaseBatch,
                // label: item.materialName,
                label: (
                    <div>
                        <BaseTooptip
                            changeplay='true'
                            data={item?.materialName}
                            slice={true}
                            maxWidth={200}
                            sliceData={`${
                                typeof item?.materialName === 'string' &&
                                item?.materialName &&
                                item?.materialName.length > 15
                                    ? item?.materialName?.slice(0, 15)?.trim() + '...'
                                    : item?.materialName
                            }`}
                        ></BaseTooptip>
                    </div>
                ),
                span: 8,
                type: 'Display'
            };
            return manager;
        })
    ];
    //质检信息
    const QsqaInfoConfig = [
        {
            label: '食品名称',
            name: 'foodName',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: traceTest?.data?.data?.foodName
        },
        {
            label: '生产批次',
            name: 'productionBatch',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: traceTest?.data?.data?.productionBatch
        },
        {
            label: '质检时间',
            name: 'testTime',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: dayjs(traceTest?.data?.data?.testTime).format('YYYY-MM-DD')
        },
        {
            type: 'Display',
            label: '食品批号',
            name: 'foodNumber',
            placeholder: '请输入',
            span: 8,
            className: 'count',
            show: traceTest?.data?.data?.foodNumber ? null : '1',
            displayDom: traceTest?.data?.data?.foodNumber
        },
        {
            label: '质检内容',
            name: 'testRecord',
            value: '123',
            type: 'Display',
            span: 8,
            displayDom: traceTest?.data?.data?.testRecord
        },
        {
            label: '质检结果',
            name: 'testResult',
            value: '123',
            type: 'Status',
            status: 'SuccessStatus',
            span: 8,
            display: (
                <h3 className={!traceTest?.data?.data?.testResult ? 'SuccessStatus' : 'ErrorStatus'}>
                    {!traceTest?.data?.data?.testResult ? '合格' : '不合格'}
                </h3>
            )
        },
        {
            type: 'Display',
            label: '质检人员',
            name: 'testPersonnel',
            placeholder: '请输入',
            span: 8,
            className: 'count',
            show: traceTest?.data?.data?.testPersonnel ? null : '1',
            displayDom: traceTest?.data?.data?.testPersonnel
        },
        {
            type: 'Display',
            label: '联系电话',
            name: 'phone',
            placeholder: '请输入',
            span: 8,
            className: 'count',
            show: traceTest?.data?.data?.phone ? null : '1',
            displayDom: traceTest?.data?.data?.phone
        },
        {
            label: '质检报告',
            name: 'testReport',
            value: '123',
            type: 'Url',
            tips: '下载报告',
            span: 8,
            display: <a href={traceTest?.data?.data?.testReport}>{'下载'}</a>
        },
        {
            label: '附件',
            name: 'accessory',
            value: '123',
            type: 'Url',
            span: 8,
            show: traceTest?.data?.data?.accessory ? null : '1',
            display: <a href={traceTest?.data?.data?.accessory}>{'下载'}</a>
        }
    ];
    const valuesForm = SourceCodeForm.getFieldsValue();
    console.log('SourceCodeForm', SourceCodeForm, valuesForm);

    // const productionBatchConfig = {
    //     title: '生产批次',
    //     visible: addModalVisible,
    //     setVisible: setAddModelVisible,
    //     okHandle: () => {
    //         setAddModelVisible(false);
    //     },
    //     onCancelHandle: () => {
    //         setAddModelVisible(false);
    //     }
    // };

    const handleOk = () => {
        setAddModelVisible(false);
    };
    const handleCancel = () => {
        setAddModelVisible(false);
    };
    return state?.data?.dataType === '原料' ? (
        <>
            {traceMaterial?.data?.data?.purchaseDetailTo &&
            <BaseCard title={<PageTitle title='原料批次详情' />}>
                <Form form={SourceCodeForm}>
                    <PageTitle title='原料信息' type='primaryIcon' bmagin={16} />
                    <BaseFormItem
                        configs={SourceInfoConfig}
                        handle={() => {
                            setAddModelVisible(true);
                        }}
                    />
                </Form>

                {/* <Form form={ProductForm}>
                    <PageTitle title='原料信息' type='primaryIcon' />
                    <BaseFormItem configs={ProductSourceInfoConfig} />
                </Form> */}
                {/* <Form form={ProductForm}>
                    <PageTitle title='生产信息' type='primaryIcon' />
                    <BaseFormItem configs={ProductInfoConfig} />
                </Form> */}
                {/* <Form form={QsqaForm}>
                    <PageTitle title='质检信息' type='primaryIcon' />
                    <BaseFormItem configs={QsqaInfoConfig} />
                </Form> */}
                <Form form={ChainForm}>
                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={chainInfoConfig} />
                </Form>
            </BaseCard>}
            <Modal
                title='生产批次'
                open={addModalVisible}
                onOk={handleOk}
                onCancel={handleCancel}
                wrapClassName='production'
            >
                <BaseTable
                    className='batch-table'
                    rowKey='account'
                    columns={listColumn}
                    dataSource={listData}
                    // loading={queryList?.isLoading}
                />
            </Modal>
        </>
    ) : state?.data?.dataType === '生产' ? (
        <>
            <BaseCard title={<PageTitle title='生产批次详情' bg='container sheng'/>}>
                {(traceProduction?.data?.data?.materialInfo && traceProduction?.data?.data?.materialInfo.length>0)&&
                    <Form form={ProductForm}>
                    <PageTitle title='原料信息' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={ProductSourceInfoConfig} />
                </Form>}
                <Form form={ProductForm}>
                    <PageTitle title='生产信息' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={ProductInfoConfig} />
                </Form>
                <Form form={ChainForm}>
                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={chainInfoConfig} />
                </Form>
            </BaseCard>
        </>
    ) : (
        <>
            <BaseCard title={<PageTitle title='质检批次详情' />}>
                <Form form={QsqaForm}>
                    <PageTitle title='质检详情' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={QsqaInfoConfig} />
                </Form>
                <Form form={ChainForm}>
                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <BaseFormItem configs={chainInfoConfig} />
                </Form>
            </BaseCard>
        </>
    );
}

export default SourceDataDetail;

.container {
    padding: 24px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.cardContainer {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    :global(.ant-card-body) {
        padding: 24px;
    }
}

.searchContainer {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    
    :global(.ant-form) {
        display: flex;
        align-items: center;
        gap: 16px;
    }
    
    :global(.ant-form-item) {
        margin-bottom: 0;
    }
}

.baseBtn {
    height: 32px;
    border-radius: 4px;
    font-size: 14px;
    
    &.searchBtn {
        background: #1890ff;
        border-color: #1890ff;
        color: #fff;
        
        &:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }
    }
}

:global(.greenBtn) {
    background: #52c41a;
    border-color: #52c41a;
    color: #fff;
    
    &:hover {
        background: #73d13d;
        border-color: #73d13d;
    }
}

:global(.base-table) {
    .ant-table-thead > tr > th {
        background: #fafafa;
        font-weight: 600;
        color: #262626;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .ant-table-tbody > tr > td {
        border-bottom: 1px solid #f0f0f0;
        padding: 12px 16px;
    }
    
    .ant-table-tbody > tr:hover > td {
        background: #f5f5f5;
    }
}

:global(.ant-badge-status-success) {
    .ant-badge-status-dot {
        background-color: #52c41a;
    }
}

:global(.ant-badge-status-error) {
    .ant-badge-status-dot {
        background-color: #ff4d4f;
    }
}

:global(.ant-btn-link) {
    padding: 0;
    height: auto;
    line-height: 1.5715;
    
    &.ant-btn-dangerous {
        color: #ff4d4f;
        
        &:hover {
            color: #ff7875;
        }
    }
}

:global(.modal) {
    .ant-modal-header {
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 24px;
        
        .ant-modal-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
        }
    }
    
    .ant-modal-body {
        padding: 24px;
    }
    
    .ant-modal-footer {
        border-top: 1px solid #f0f0f0;
        padding: 10px 16px;
        text-align: right;
    }
}

:global(.ant-form-vertical) {
    .ant-form-item-label {
        padding-bottom: 4px;
        
        label {
            font-weight: 500;
            color: #262626;
        }
    }
    
    .ant-form-item-control-input {
        min-height: 32px;
    }
}

:global(.ant-pagination) {
    margin-top: 24px;
    text-align: right;
    
    .ant-pagination-item {
        border-radius: 4px;
        
        &.ant-pagination-item-active {
            background: #1890ff;
            border-color: #1890ff;
            
            a {
                color: #fff;
            }
        }
    }
    
    .ant-pagination-prev,
    .ant-pagination-next {
        border-radius: 4px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 16px;
    }
    
    .searchContainer {
        flex-direction: column;
        align-items: stretch;
        
        :global(.ant-form) {
            flex-direction: column;
            gap: 8px;
        }
    }
}

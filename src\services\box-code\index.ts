import request from '../request';

//箱码列表
export const boxCodePage = (obj: any) => {
    return request({
        url: '/box/page',
        method: 'post',
        data: obj
    });
};
//作废
export const cancelBox = (obj: any) => {
    return request({
        url: '/box/cancel',
        method: 'post',
        data: obj
    });
};
//add
export const addBox = (obj: any) => {
    return request({
        url: '/box/add',
        method: 'post',
        data: obj
    });
};
//查看详情
export const getBoxCodeDetails = (obj: any) => {
    return request({
        url: '/box/detail',
        method: 'get',
        params: obj
    });
};
//下载箱码
export const downloadBoxCode = (obj: any) => {
    return request({
        responseType: 'blob',
        url: '/box/download',
        method: 'post',
        data: obj
    });
};


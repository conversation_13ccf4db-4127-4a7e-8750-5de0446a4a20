.searchContainer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .ant-row {
        width: 100% !important;
    }
    .ant-card-body {
        padding-top: 0px;
    }
    .ant-form-item {
        width: 200px !important;
        // .ant-picker.ant-picker-range {
        //     width: 200px;
        // }
    }
    .searchBtn {
        margin-left: 10px;
        margin-right: 5px;
    }
    .baseBtn {
        background: @blueBtn;
    }
}
.color{
    color: red;
}
.warnBtn {
    color: @redBtn;
}
.coreFIrmContainer {
    .ant-table-title {
        padding: 0;
    }
}
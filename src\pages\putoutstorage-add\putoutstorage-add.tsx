import BaseCard from '@components/base-card';
import PageTitle from '@components/page-title';
import BaseButton from '@components/base-button';
import FilterForm from '@components/filter-form';
import { Button, Input, Space } from 'antd';
import { Form, message } from 'antd';

import BaseModal from '@components/base-modal';
import BaseTable from '@components/base-table';
import TableHead from '@components/table-head';
import { useMutation, useQuery } from 'react-query';
import { canOutFoodList, canOutProductionList, addStorageOut } from '@services/storage-out';
import { useNavigate } from 'react-router-dom';
import { signData } from '../../utils/blockChainUtils';
import { ReformChainError } from '@utils/errorCodeReform';
import { getLocalPrivatekey } from '@utils/blockChainUtils';

import './index.less';
import { useLocation } from 'react-router-dom';
import { useState, useRef } from 'react';
import { useDispatch } from 'react-redux';
export default function PutOutStorageAdd() {
    const navigate = useNavigate();
    const [FoodForm] = Form.useForm();
    const dispatch = useDispatch();
    const [SourceCodeForm] = Form.useForm();
    const [addModalVisible, setAddModelVisible] = useState(false);
    const [addEmployeesForm] = Form.useForm();
    const [tablelist, settablelist]: any = useState([]);
    const [food_id, setfood_id] = useState('');
    const foodData: any = useRef('');
    const productionquery = useQuery(
        ['purchsequery'],
        () => {
            return canOutFoodList({});
        },
        {
            onError(err: any) {
                ReformChainError(err);
            },
            onSuccess(res) {
                getLocalPrivatekey(dispatch);
                const date = new Date().getTime();
                const datetext = date.toString().slice(1, 13);
                SourceCodeForm.setFieldsValue({
                    outNumber: datetext
                });
                console.log('res999999', res);
            }
        }
    );
    //生产批次
    const productionList = useMutation(canOutProductionList, {
        onSuccess(res) {
            console.log('res', res);
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    //新增
    const addStoragein = useMutation(addStorageOut, {
        onSuccess(res) {
            message.success('添加成功');
            navigate('/cpsjsr/putOut');
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    const mapToEnum_name: any = {};
    const mapToEnum_number: any = {};
    const mapToEnum_batch: any = {};

    (productionquery?.data?.data || [])?.forEach((item: any, index: any) => {
        mapToEnum_name[item.food_id] = item.food_name;
        mapToEnum_number[item.food_id] = item.food_number;
    });

    (productionList?.data?.data || [])?.forEach((item: any, index: any) => {
        mapToEnum_batch[item.production_id] = item.production_batch;
    });
    const addConfigs = [
        {
            type: 'Input',
            label: '出库单号:',
            value: 'outNumber',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback('请输入出库单号！');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入',
            span: 12,
            className: 'count'
        },
        {
            type: 'DatePicker',
            label: '出库时间:',
            value: 'outTime',
            placeholder: '请输入',
            rules: [{ required: true, message: '请选择出库时间' }],
            span: 12,
            wide: 280
        },
        {
            type: 'Input',
            label: '物流企业名称:',
            value: 'logisticsName',
            placeholder: '请输入',
            span: 12,
            className: 'count',
            rules: [
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback();
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            type: 'Input',
            label: '物流单号:',
            value: 'logisticsNumber',
            placeholder: '请输入',
            span: 12,
            className: 'count',
            rules: [
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback();
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            type: 'Input',
            label: '销售渠道:',
            value: 'marketingChannel',
            placeholder: '请输入',
            span: 12,
            className: 'count',
            rules: [
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback();
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            type: 'Input',
            label: '订单号:',
            value: 'orderNumber',
            placeholder: '请输入',
            span: 12,
            className: 'count',
            rules: [
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback();
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保证订单号在50字符以内');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            type: 'Input',
            label: '经销商:',
            value: 'dealer',
            placeholder: '请输入',
            span: 12,
            className: 'count',
            rules: [
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        if (!value) {
                            callback();
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (value.length > 50) {
                            callback('请保持字符在50字符以内!');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            type: 'Button',
            label: '选择食品:',
            color: 'primary',
            value: 'foodlist',
            placeholder: '选择食品',
            onClick: () => {
                setAddModelVisible(true);
            },
            span: 12,
            rules: [{ required: true, message: '请选择食品品类!' }]
        }
    ];
    const Storingfood = [
        {
            title: '食品编号',
            dataIndex: 'foodNumber',
            key: 'foodNumber'
        },
        {
            title: '食品名称',
            dataIndex: 'foodName',
            key: 'foodName'
        },
        {
            title: '生产批次',
            dataIndex: 'productionBatch',
            key: 'productionBatch'
        },
        {
            title: '出库数量',
            dataIndex: 'count',
            key: 'count'
        },
        {
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle'>
                    <BaseButton
                        type='dashed'
                        className='warnBtn'
                        onClick={() => {
                            for (let i = 0; i < tablelist.length; i++) {
                                console.log('i', i, record.id);
                                if (i === record.id) {
                                    tablelist.splice(i, 1);
                                    const arr = tablelist;
                                    settablelist([...arr]);
                                }
                            }
                        }}
                    >
                        删除
                    </BaseButton>
                </Space>
            )
        }
    ];
    const tableData = tablelist.map((item: any, index: any) => ({
        foodNumber: item.foodNumber,
        foodName: item.foodName,
        productionBatch: item.productionBatch,
        count: item.count,
        id: index
    }));
    const addEmployeesConfig = {
        okText: '确定',
        title: '选择食品',
        visible: addModalVisible,
        setVisible: setAddModelVisible,
        okHandle: async () => {
            const values = await addEmployeesForm.validateFields();
            const values_2 = await SourceCodeForm.getFieldsValue();
            foodData.current = values;
            const arr = tablelist;
            let foodlist = null;
            Object.keys(values).forEach((key) => {
                console.log(values[key] == undefined);
                if (values[key] == undefined) {
                    console.log('123');
                    foodlist = 1;
                }
            });
            console.log('foodlist', foodlist);
            if (
                tablelist.length !== 0 &&
                tablelist.some(function (item: any) {
                    if (item.productionId === values.foodbatch) {
                        return true;
                    }
                })
            ) {
                message.error('您选的批次号已重复,请正确选择食品!');
            } else {
                if (foodlist === null) {
                    arr.push({
                        foodId: values.food,
                        foodNumber: mapToEnum_number[values.food],
                        foodName: mapToEnum_name[values.food],
                        productionId: values.foodbatch,
                        productionBatch: mapToEnum_batch[values.foodbatch],
                        count: values.OutputNumber
                    });
                    setAddModelVisible(false);
                    SourceCodeForm.setFieldsValue({
                        foodlist: 1
                    });
                }
            }
            // if (foodlist === null) {
            //     arr.push({
            //         foodId: values.food,
            //         foodNumber: mapToEnum_number[values.food],
            //         foodName: mapToEnum_name[values.food],
            //         productionId: values.foodbatch,
            //         productionBatch: mapToEnum_batch[values.foodbatch],
            //         count: values.OutputNumber
            //     });
            //     setAddModelVisible(false);
            //     SourceCodeForm.setFieldsValue({
            //         foodlist: 1
            //     });
            // } else {
            //     message.error('请正确选择食品');
            // }
            settablelist(arr);
            // setAddModelVisible(false);
            addEmployeesForm.resetFields();
        },
        onCancelHandle: () => {
            setAddModelVisible(false);
            addEmployeesForm.resetFields();
        }
    };
    //选择食品
    const choosefood = [
        {
            label: '食品',
            type: 'Select',
            value: 'food',
            placeholder: '请输入',
            rules: [{ required: true, message: '请输入食品' }],
            onChange: (option: any, input: any) => {
                setfood_id(option);
                if (option !== undefined) {
                    productionList.mutate({
                        foodId: option
                    });
                }
                console.log('option', option, input);
            },
            fields: [
                ...(productionquery?.data?.data || [])?.map((item: any, index: any) => {
                    const materialdata = {
                        value: item.food_id,
                        label: item.food_name,
                        food_number: item.food_number
                    };
                    return materialdata;
                })
            ]
        },
        {
            label: '生产批次',
            type: 'Select',
            value: 'foodbatch',
            placeholder: '请选择',
            name:'foodbatch',
            rules: [{ required: true, message: '请输入生产批次' }],
            fields: [
                ...(food_id
                    ? (productionList?.data?.data || []).map((item: any, index: any) => {
                          const materialdata = {
                              value: item.production_id,
                              label: item.production_batch
                          };
                          return materialdata;
                      })
                    : [])
            ]
        },
        {
            label: '出库数量',
            type: 'Input',
            value: 'OutputNumber',
            placeholder: '请选择',
            wide: 292,
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^\+?[1-9]\d{0,9}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入出库数量！');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            callback('请输入正整数，并保持10字符以内');
                        } else {
                            callback();
                        }
                    }
                })
            ]
        }
    ];
    //监听数据变化
    const onFieldsChange=( values:any, errorFields:any)=>{
        console.log('values',values)
        if(values[0].name[0]==='food'){
            addEmployeesForm.setFieldsValue({foodbatch: null })      //清空生产批次输入框
        }else{
        }

    }
    const onFinish = async (values: any) => {
        console.log('valuess', values);
        if (tablelist !== '') {
            const params: any = {
                outNumber: values.outNumber,
                outTime: values.outTime,
                logisticsName: values.logisticsName,
                logisticsNumber: values.logisticsNumber,
                marketingChannel: values.marketingChannel,
                orderNumber: values.orderNumber,
                dealer: values.dealer,
                foodList: tablelist.map((item: any) => {
                    const materialdata = {
                        foodId: item.foodId,
                        foodNumber: mapToEnum_number[item.foodId],
                        foodName: mapToEnum_name[item.foodId],
                        productionId: item.productionId,
                        productionBatch: item.productionBatch,
                        count: item.count
                    };
                    return materialdata;
                })
            };
            console.log('params888888888', JSON.stringify(params));
            const paramStr = JSON.stringify(params);
            signData(dispatch, JSON.stringify(params), (error, result: any) => {
                if (!error && result) {
                    params['signature'] = result;
                    params['paramStr'] = paramStr;
                    console.log('addfood.mutate(): ', error, result);
                    console.log('params7777777777', params);
                    addStoragein.mutate(params);
                } else if (error !== 'misprivatekey') {
                    message.info('签名异常，请重试或联系管理员');
                }
            });
        } else {
            message.error('请选择食品');
        }
    };
    return (
        <div className='putoutStorage'>
            <BaseCard title={<PageTitle title='出库信息填写' />}>
                <Form form={SourceCodeForm} onFinish={onFinish} className='edit-label-title'>
                    <PageTitle title='出库信息' type='primaryIcon' bmagin={16} />
                    <FilterForm itemConfig={addConfigs} labelCol={6} wrapperCol={9} />
                    <Form form={FoodForm}>
                        <PageTitle title='出库食品' type='primaryIcon' />
                        <BaseTable
                            className='baseTable-title-nopadding'
                            rowKey='account'
                            btnDisplay={(checkData: any, resetSelect: any) => {
                                return (
                                    <TableHead
                                        LeftDom={<div></div>}
                                        RightDom={
                                            <div
                                                style={{
                                                    display: 'flex'
                                                }}
                                            ></div>
                                        }
                                    />
                                );
                            }}
                            columns={Storingfood}
                            dataSource={tableData}
                        />
                    </Form>
                    <div className='footerBtn'>
                        <Form.Item>
                            <Button className='submitBtn' type='primary' htmlType='submit'>
                                提交
                            </Button>
                            <Button
                                className='cancelBtn'
                                onClick={() => {
                                    navigate('/cpsjsr/putOut');
                                }}
                            >
                                取消
                            </Button>
                        </Form.Item>
                    </div>
                </Form>
            </BaseCard>
            <BaseModal {...addEmployeesConfig}>
                <Form
                    name='addEmployeesForm'
                    form={addEmployeesForm}
                    onFieldsChange={onFieldsChange}
                    className='edit-label-title'
                >
                    {<FilterForm itemConfig={choosefood} />}
                </Form>
            </BaseModal>
        </div>
    );
}

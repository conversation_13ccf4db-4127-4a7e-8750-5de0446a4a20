.searchContainer {
    display: flex;
    flex-direction: row;
    .ant-card-body {
        padding-top: 0px;
    }
    .ant-form-item {
        width: 200px !important;
        // .ant-picker.ant-picker-range {
        //     width: 200px;
        // }
    }
    .searchBtn {
        margin-left: 40px;
        margin-right: 5px;
    }
    .baseBtn {
        background: @blueBtn;
    }
}

.coreFIrmContainer {
    .ant-table-title {
        padding: 0;
    }
}

.resetContainer {
    display: flex;
    align-items: center;
}

.btn {
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    color: #909090;
    background: #f7f8fa;
    word-break: keep-all;
}

.disable {
    cursor: not-allowed;
}
.newPswBtn {
    margin: 0 8px 0 10px;
}

.active {
    color: #fff;
    background: #80a932;
}
.warnBtn {
    color: @redBtn;
}
.pswInput {
    background: #f7f8fa;
    cursor: not-allowed;
}
.employess_icon > svg {
    font-size: 25px;
    position: relative;
    top: 5px;
    left: -449px;
    color: #15ad31;
}
.employess-label-title {
    font-family: AlibabaPuHuiTiR;
    color: #333333;
}

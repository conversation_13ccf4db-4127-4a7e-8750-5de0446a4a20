/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-11-01 18:03:37
 * @LastEditTime: 2022-11-01 18:03:37
 * @LastEditors: PhilRandWu
 */
.searchContainer {
    display: flex;
    flex-direction: row;
    .ant-card-body {
        padding-top: 0px;
    }
    .ant-form-item {
        width: 200px !important;
        // .ant-picker.ant-picker-range {
        //     width: 200px;
        // }
    }
    .searchBtn {
        margin-left: 10px;
        margin-right: 5px;
    }
    .baseBtn {
        background: @blueBtn;
    }
}
.employess_icon>svg{
    font-size: 25px;
    position: relative;
    top: 5px;
    left: -449px;
    color: #15ad31;
 }

// .resetPswModal {
//   .ant-row.ant-form-item {
//     // align-items: center;
//   }

//   .btn {
//     &.disable {
//       cursor: not-allowed;
//     }
//     &.newPswBtn {
//       margin: 0 8px 0 10px;
//     }
//     &.active {
//       color: #fff;
//       background: #3d73ef;
//     }
//   }
//   .pswInput {
//     background: #F7F8FA;
//     cursor: not-allowed;
//   }
// }

.resetContainer {
    display: flex;
    align-items: center;
}

.btn {
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    color: #909090;
    background: #f7f8fa;
    word-break: keep-all;
}

.disable {
    cursor: not-allowed;
}
.newPswBtn {
    margin: 0 8px 0 10px;
}

.active {
    color: #fff;
    background: #80a932;
}
.warnBtn {
    color: @redBtn;
}
.pswInput {
    background: #f7f8fa;
    cursor: not-allowed;
}


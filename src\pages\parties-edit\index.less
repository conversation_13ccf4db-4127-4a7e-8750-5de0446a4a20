.manager {
    margin-left: 2px;
}
.titleHead {
    margin-top: 10px;
    .flexContent(row,left);
    &.blueBgTitle {
        padding: 17px 0px;
        padding-left: 15px;
        background: #eceffc;
    }
}

.editBtn {
    margin-left: 16px;
    color: @blueBtn;
}
.managerAccount {
    margin-bottom: 0;
}
.edit {
    .cancelBtn {
        margin-left: 10px;
    }
    .text {
        margin-left: 12px;
        display: inline-block;
    }
    .reset {
        margin-left: 110px;
    }
    .ant-form-item-row {
        margin-top: 10px;
    }

    .noAdminContainer {
        margin-top: 38px;
        .flexContent(column,center);
        .setAdminBtn {
            color: @blueBtn;
            text-decoration: underline;
        }
        img{
            width: 70px;
            height: 70px;
        }
    }
}
.submitBtn {
    width: 89px;
    border-radius: 5px;
}
.submitBtn_1{
    width: 89px;
    margin-top: 15px;
    border-radius: 5px;
}
.cancelBtn_1 {
    width: 89px;
    border-radius: 5px;
    margin-left: 10px;
}
.cancelBtn{
    width: 89px;
    border-radius: 5px;
}
.searchContainer {
    display: flex;
    flex-direction: row;
    .ant-card-body {
        padding-top: 0px;
    }
    .ant-form-item {
        width: 200px !important;
        // .ant-picker.ant-picker-range {
        //     width: 200px;
        // }
    }
    .searchBtn {
        margin-right: 5px;
    }
    .baseBtn {
        background: @blueBtn;
    }
}

.coreFIrmContainer {
    .ant-table-title {
        padding: 0;
    }
}

.resetContainer {
    display: flex;
    align-items: center;
}

.btn {
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    color: #909090;
    background: #f7f8fa;
    word-break: keep-all;
}

.disable {
    cursor: not-allowed;
}
.newPswBtn {
    margin: 0 8px 0 10px;
}

.active {
    color: #fff;
    background: #3d73ef;
}
.warnBtn {
    color: @redBtn;
}
.pswInput {
    background: #f7f8fa;
    cursor: not-allowed;
}
.employess_icon>svg {
    font-size: 25px;
    position: relative;
    top: 5px;
    left: -449px;
    color: #15ad31;
}
.resetpassword{
    position: absolute;
    margin-left: 220px;
    margin-top: -43px;
}
import React, { memo, useState, useEffect, useContext, useRef } from 'react';

import { Button } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

import type { ButtonProps } from 'antd';

import './app-button.less';

const AddButton = function (props: ButtonProps) {
    return (
        <div className='AddButtonWrapper'>
            <Button icon={<PlusOutlined rev={undefined} />} {...props}></Button>
        </div>
    );
};

const AppButton = function (props: ButtonProps) {
    return (
        <div className='AppButtonWrapper'>
            <Button {...props}></Button>
        </div>
    );
};

AppButton.Add = AddButton;

export default AppButton;

import React from 'react';
import { Layout } from 'antd';

import TopNavBar from './top-nav-bar';
import SideNavBar from './side-nav-bar';
import Sider from './sider';
import BasicContent from './basic-content';
import OldBreadcrumbNavBar from '@components/base-breadcrumb';
import BreadcrumbNavBar from '@components/app-breadcrumb';

import './styles.less';

const BasicLayout = () => {
    return (
        <Layout className='basic-layout'>
            <Sider></Sider>
            {/* <SideNavBar /> */}
            <Layout id="scrollArea" className='basic-layout-content'>
                <TopNavBar />
                <BreadcrumbNavBar />
                <BasicContent />
            </Layout>
        </Layout>
    );
};

export default React.memo(BasicLayout);

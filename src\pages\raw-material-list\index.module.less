.searchContainer {
    display: flex;
    flex-direction: row;
    .ant-card-body {
        padding-top: 0px;
    }
    .ant-form-item {
        width: 200px !important;
        // .ant-picker.ant-picker-range {
        //     width: 200px;
        // }
    }
    .searchBtn {
        margin-right: 5px;
    }
    .baseBtn {
        background: @blueBtn;
    }
    :global {
        .ant-form-item {
            margin-bottom: 20px;
        }
    }
}

.coreFIrmContainer {
    .ant-table-title {
        padding: 0;
    }
    :global {
        .ant-table-title {
            padding-top: 0;
        }
    }
}

.resetContainer {
    display: flex;
    align-items: center;
}

.btn {
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    color: #909090;
    background: #f7f8fa;
    word-break: keep-all;
}

.disable {
    cursor: not-allowed;
}
.newPswBtn {
    margin: 0 8px 0 10px;
}

.active {
    color: #fff;
    background: @primary-color;
}

.pswInput {
    background: #f7f8fa;
    cursor: not-allowed;
}

.configBtn {
    color: @primary-color;
}

// .warnBtn {
//   color: @redBtn;
// }

.primaryBtn {
    color: @primary-color;
}

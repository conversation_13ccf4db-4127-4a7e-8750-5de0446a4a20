import request from '../request';

export interface ISourceInspectQuery {
    coreId: number,
    endTime: string,
    inspectionResults: number,
    orgId: number,
    pageIndex: number,
    pageSize: number,
    productionBatch: string,
    startTime: string
}

export enum SourceInspectResultsEnum {
    _,
    "合格",
    "不合格"
}

export default class SourceInspectService {
    public static async Query(data: ISourceInspectQuery) {
        return request({
            url: '/traceData/qualityPage',
            method: 'post',
            data
        });
    };

    public static async detail(qualityId: number) {
        return request({
            url: '/traceData/qualityDetail',
            method: 'get',
            params: {qualityId}
        });
    }
}
import BaseCard from '@components/base-card';
import BasePagination from '@components/base-pagination';
import BaseTable from '@components/base-table';
import { Badge, Form, message, Space, Row, Col, Card, DatePicker } from 'antd';
import useUrlState from '@ahooksjs/use-url-state';
import dayjs from 'dayjs';
import { alidFoodList } from '@services/material';
import { productProcessPage, cancelProduction, cancelProcess } from '@services/production';
import { useMutation, useQuery } from 'react-query';
import BaseInput from '@components/base-input';
import styles from './index.module.less';
import { useRef, useState } from 'react';
import PageTitle from '@components/page-title';
import { SearchOutlined, PlusOutlined, SyncOutlined } from '@ant-design/icons';
import TableHead from '@components/table-head';
import BaseButton from '@components/base-button';
import { useAccountList } from '../../myhooks/useaccountlist';
import SingleSearch from '@components/single-search';
import { useNavigate } from 'react-router-dom';
import BaseModal from '@components/base-modal';
import FilterForm from '@components/filter-form';
import { ReformChainError } from '@utils/errorCodeReform';
import { addProductConfig } from './config';
import WithPaginate from '../../hoc/withpaginate';
import { ColumnsType } from 'antd/lib/table';
import utc from 'dayjs/plugin/utc';
import BaseSelect from '@components/base-select';
dayjs.extend(utc);

const { RangePicker } = DatePicker;

interface IUrlState {
    pageIndex: number;
    pageSize: number;
}

const ProductList = (props: any) => {
    const { pageInfo, handlePaginationChange } = props;
    const navigate = useNavigate();
    const [modalVisible, setmodalVisible] = useState(false);
    const [search]: any = Form.useForm();
    const [addProductForm] = Form.useForm();
    const querylist: any = useRef('');
    //作废
    const matermodiy = useMutation(cancelProcess, {
        onSuccess(res) {
            message.success('作废成功');
            productionquery.refetch();
        },
        onError(err: any) {
            ReformChainError(err);
            productionquery.refetch();
        }
    });
    //分页数据
    const productionquery = useQuery(
        ['productionquery1', pageInfo],
        () => {
            return productProcessPage({
                pageIndex: pageInfo.pageIndex,
                pageSize: pageInfo.pageSize,
                state: querylist?.current?.state,
                processName: querylist?.current?.processName?.trim() || undefined,
                startTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[0]).startOf('day')).format()
                    : undefined,
                endTime: querylist?.current?.createTime
                    ? dayjs.utc(dayjs(querylist?.current?.createTime[1]).endOf('day')).format()
                    : undefined
            });
        },
        {
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );

    const tableData = productionquery?.data?.data?.records?.map((item: any) => ({
        processName: item?.processName,
        id: item?.id,
        createTime: dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss'),
        status: item.state,
        optName: item.optName
    }));

    const listColumn: ColumnsType<any> = [
        {
            title: '生产过程编号',
            dataIndex: 'id',
            key: 'id',
            ellipsis: true,
            render(data: any, record: any, index: number) {
                return data;
            }
        },
        {
            title: '过程名称',
            dataIndex: 'processName',
            key: 'processName',
            ellipsis: true,
            render(data) {
                return data;
            }
        },

        {
            title: '操作人',
            dataIndex: 'optName',
            key: 'optName',
            ellipsis: true
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            ellipsis: true
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            ellipsis: true,
            render: (data: any) => (
                <span style={{ color: data ? 'rgb(161, 158, 162)' : '#666666' }}>
                    <Badge
                        status={data ? 'success' : 'error'}
                        color={data ? 'rgb(161, 158, 162)' : 'rgb(36, 171, 59)'}
                        text={data ? '已作废' : '可用'}
                    />
                </span>
            )
        },
        {
            width: 200,
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            render: (data: any, record: any) => (
                <Space size='middle'>
                    <BaseButton
                        danger
                        type='dashed'
                        className='warnBtn'
                        disabled={record.status}
                        onClick={() => {
                            matermodiy.mutate({
                                id: record?.id
                            });
                        }}
                    >
                        作废
                    </BaseButton>
                    <BaseButton
                        type='dashed'
                        className='primaryBtn'
                        onClick={() => {
                            navigate('/product/product/detail', {
                                state: {
                                    data: record
                                }
                            });
                        }}
                    >
                        查看详情
                    </BaseButton>
                </Space>
            )
        }
    ];

    const searchConfig = [
        {
            label: '过程名称',
            type: 'Input',
            value: 'processName',
            placeholder: '请输入过程名称',
            span: 8
        },
        {
            label: '创建时间',
            type: 'Custom',
            value: 'createTime',
            placeholder: '请选择',
            span: 10,
            className: 'find',
            children: <RangePicker style={{ width: '100%' }} getPopupContainer={(trigger: any) => trigger.parentNode} />
        },
        {
            label: '状态',
            type: 'Select',
            value: 'state',
            placeholder: '请选择',
            span: 6,
            className: 'find',
            fields: [
                {
                    value: 0,
                    label: '可用'
                },
                {
                    value: 1,
                    label: '作废'
                }
            ]
        }
    ];

    return (
        <>
            <BaseCard
                title={<PageTitle title='生产过程列表' bg='container sheng' />}
                className={styles.coreFIrmContainer}
            >
                <div className={`${styles.searchContainer}`}>
                    <Form
                        style={{
                            width: '100%',
                            display: 'flex',
                            justifyContent: 'space-between',
                            marginBottom: '20px'
                        }}
                        layout='inline'
                        className='label-title'
                        onFinish={(values) => {
                            handlePaginationChange(1);
                            querylist.current = values;
                            productionquery.refetch();
                        }}
                        labelCol={{ span: 6 }}
                        form={search}
                    >
                        <Row
                            gutter={[24, 0]}
                            style={{
                                width: '100%'
                            }}
                        >
                            {/* <FilterForm itemConfig={searchConfig} labelCol={7} wrapperCol={16} /> */}
                            <Col span={6}>
                                <Form.Item label='过程名称' name='processName'>
                                    <BaseInput placeholder='请输入过程名称'></BaseInput>
                                </Form.Item>
                            </Col>
                            <Col span={7}>
                                <Form.Item label='创建时间' name='createTime'>
                                    <RangePicker
                                        style={{ width: '220px' }}
                                        // getPopupContainer={(trigger: any) => trigger.parentNode}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={6}>
                                <Form.Item label='状态' name='state'>
                                    {/* <BaseInput style={{ width: '90%' }} placeholder='请输入产品名称'></BaseInput> */}
                                    <BaseSelect
                                        placeholder='请选择'
                                        options={[
                                            {
                                                value: 0,
                                                label: '可用'
                                            },
                                            {
                                                value: 1,
                                                label: '作废'
                                            }
                                        ]}
                                    ></BaseSelect>
                                </Form.Item>
                            </Col>
                            <Col span={4}>
                                <Space style={{ marginRight: 16 }}>
                                    <BaseButton
                                        htmlType='submit'
                                        type='primary'
                                        style={{ width: 100 }}
                                        className={`${styles.searchBtn} ${styles.baseBtn}`}
                                        icon={<SearchOutlined rev={undefined} />}
                                    >
                                        查询
                                    </BaseButton>
                                    <BaseButton
                                        type='dashed'
                                        className='primaryBtn'
                                        style={{ width: 100 }}
                                        icon={<SyncOutlined rev={undefined} />}
                                        onClick={() => {
                                            querylist.current = null;
                                            productionquery.refetch();
                                            search.resetFields();
                                        }}
                                    >
                                        重置
                                    </BaseButton>
                                </Space>
                            </Col>
                        </Row>
                    </Form>
                </div>
                <BaseTable
                    rowKey='account'
                    columns={listColumn}
                    dataSource={tableData}
                    loading={productionquery?.isFetching}
                />
                <BasePagination
                    shouldShowTotal
                    showQuickJumper
                    showSizeChanger
                    current={pageInfo.pageIndex}
                    pageSize={pageInfo.pageSize}
                    total={productionquery?.data?.data.total}
                    onShowSizeChange={handlePaginationChange}
                    onChange={handlePaginationChange}
                />
            </BaseCard>
        </>
    );
};

export default WithPaginate(ProductList);

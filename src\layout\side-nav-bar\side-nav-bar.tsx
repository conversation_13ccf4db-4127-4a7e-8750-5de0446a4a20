/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-20 11:48:31
 * @LastEditTime: 2022-11-01 18:30:35
 * @LastEditors: PhilRandWu
 */
import React, { useEffect, useState } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { Layout, Menu } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import { basiceRouter } from '@router/routers';
import './style.less';
import { updataisFill } from '@store/slice-reducer/user';

// import style from './styles.module.less';

const SideNavBar = () => {
    const location = useLocation();
    const { pathname } = location;
    const renderRouter: any = (routes: any, basePath: string, pathname: string) => {
        return (
            routes
                // .filter((route: any) => route.showInSideMenu)
                .map((route: any) => {
                    const reg = new RegExp(`^${route.path}`);
                    const isExact = reg.test(pathname);
                    console.log('router', route.path, isExact);
                    if ('children' in route && !route?.children?.[0]?.index && !route?.hidden) {
                        console.log('subMenu', route);
                        return (
                            <Menu.SubMenu
                                className={
                                    isExact ? 'menuContainer mainSubMenu activeMain' : 'menuContainer mainSubMenu'
                                }
                                key={basePath + route.path}
                                title={
                                    <div className='picture'>
                                        {route?.icon && <img src={route.icon} alt='icon' />}
                                        <NavLink to={route?.jumpPage ? route.path + '/' + route?.jumpPage : route.path}>
                                            {route.label}
                                        </NavLink>
                                    </div>
                                }
                            >
                                {renderRouter(route.children, basePath + route.path, pathname)}
                            </Menu.SubMenu>
                        );
                    } else if (!route?.hidden) {
                        console.log('Menu', route, basePath + route.path);
                        return (
                            <Menu.Item key={basePath + route.path} className='menuContainer'>
                                {route?.icon && <img src={route.icon} alt='icon' />}
                                <NavLink
                                    to={route?.children?.[0]?.index ? `${route.path}` : `${basePath}/${route.path}`}
                                >
                                    {route.label}
                                </NavLink>
                            </Menu.Item>
                        );
                    }
                })
        );
    };

    return (
        <Layout.Sider
            className='app-side-nav-bar'
            // className={style.appsidenavbar}
            width={265}
            breakpoint='lg'
            collapsedWidth='0'
        >
            <div
                // className={style.applogo}
                className='app-logo'
            >
                {/* <img src={} alt='logo' /> */}
                {sessionStorage.systemTitle}
            </div>
            {/* // selectedKeys:当前选中的菜单项 key 数组 */}
            <Menu theme='dark' mode='inline' selectedKeys={[pathname.split('/')[1]]}>
                {renderRouter(basiceRouter, '', window.location.pathname)}
            </Menu>
        </Layout.Sider>
    );
};

export default React.memo(SideNavBar);

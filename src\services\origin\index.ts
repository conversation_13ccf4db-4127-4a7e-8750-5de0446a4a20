/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-11-01 18:29:23
 * @LastEditTime: 2022-11-01 18:29:23
 * @LastEditors: PhilRandWu
 */
import request from '../request';
export const getOriginList = (obj: any) => {
    return request({
        url: '/place/getPlaceList',
        method: 'post',
        data: obj
    });
};

//修改食品状态
export const updateOriginState = (obj: any) => {
    // console.log("obj777",obj)
    return request({
        url: `/place/updatePlaceState`,
        method: 'post',
        data: obj
    });
};
//配置所属产品
export const updateProduct = (obj: any) => {
    // console.log("obj777",obj)
    return request({
        url: `/place/updatePlaceProduct`,
        method: 'post',
        data: obj
    });
};
//查看食品详情
export const getOriginDetail = (obj: any) => {
    return request({
        url: `/place/getPlaceDetail?placeId=${obj.placeId}`,
        method: 'get',
    });
};
//新增食品
export const addOrigin = (obj: any) => {
    return request({
        url: '/place/addPlace',
        method: 'post',
        data: obj
    });
};
//配置食品溯源码
export const sourceConfig = (obj: any) => {
    return request({
        url: '/food/sourceConfig',
        method: 'post',
        data: obj
    });
};
//编辑食品
export const updatePlace = (obj: any) => {
    return request({
        url: '/place/updatePlace',
        method: 'post',
        data: obj
    });
};
//食品列表
export const materialList = (obj: any) => {
    return request({
        url: `/food/materialList?foodId=${obj.foodId}`,
        method: 'post',
        data: obj
    });
};
//食品品类
export const foodCategory = (obj: any) => {
    return request({
        url: `/category/getCategoryList`,
        method: 'get'
    })
};
//上传图片
export const temporaryUploadUrl = (obj: any) => {
    return request({
        url: `/minio/get-url`,
        method: 'get',
        params: obj
    });
};
export const getFoodInfo = (obj: any) => {
    return request({
        url: `/food/getFoodInfo?foodId=${obj.id}`,
        method: 'post',
        data: obj
    });
};
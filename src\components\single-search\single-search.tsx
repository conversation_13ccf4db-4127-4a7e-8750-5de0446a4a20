/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-06 16:23:09
 * @LastEditTime: 2022-11-01 18:38:55
 * @LastEditors: PhilRandWu
 */
import { Input } from 'antd';
import './style.less';
const { Search } = Input;

interface searchConfig {
    label?: string;
    handleSearch: any;
    placeholder: string;
    setSearchValue: any;
    classname?: any;
}

const SingleSearch = (props: searchConfig) => {
    return (
        <div className='singleSearchContainer'>
            {props.label}
            <Search
                className={`${props?.classname} singleSearchInput`}
                placeholder={props?.placeholder}
                allowClear
                onSearch={(value: string) => {
                    props.setSearchValue(value.trim());
                    props.handleSearch();
                }}
                style={{ width: 267, height: 32 }}
            />
        </div>
    );
};

export default SingleSearch;

.SwitchListContainer {
    margin-top: 36px;
    background: #fcfdfd;
    .ant-list-item {
        border-color: #ccc !important;
        &.first-child {
            font-size: 12px;
        }
    }
    .ant-list-items {
        li:nth-child(1) {
            .baseTooltip .preData {
                font-size: 14px;
                font-weight: 500;
            }
            font-size: 14px;
            font-weight: 500;
        }
        .baseTooltip .preData {
            font-weight: 500;
        }
    }

    .ant-list-split .ant-list-item:last-child {
        border-bottom: 1px solid #ccc;
    }

    .displayContent,
    .listHead {
        padding-right: 686px;
        padding-left: 44px;
        // width: 447px;
        margin: 0;
        .flexContent(row,space-between);
        .ant-form-item {
            margin: 0;
        }
    }

    .listHead {
        padding-right: 730px;
        padding-left: 0px;
    }
    span {
        white-space: nowrap;
        // width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

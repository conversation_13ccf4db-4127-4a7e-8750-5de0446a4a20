import BaseCard from '@components/base-card';
import BaseFormItem from '@components/base-form-item';
import PageTitle from '@components/page-title';
import React, { useState } from 'react';
import { Form, message } from 'antd';
import { useMutation, useQuery } from 'react-query';
import { foodDetail } from '@services/food';
import { enumeration } from './config';
import SwitchList from '@components/switch-list';
import styles from './index.module.less';
import { useLocation } from 'react-router-dom';
import { ReformChainError } from '@utils/errorCodeReform';
import dayjs from 'dayjs';
import { Image } from 'antd';
import FilterForm from '@components/filter-form';

import { FormItemImages, FormItemVideo } from '@components';
import ChainDetailModal from '@components/chain_detail_modal';
import { decryptedUrl, isArrayArr } from '@utils';
import { getSalesDetail } from '@services/land-test';
import { getWareDetail } from '@services/ware';
import { RoleEnum } from '@config';
import { useAppSelector } from '@store';

const FoodDetail = (props: any) => {
    const { state } = useLocation();
    const userInfo = useAppSelector((store) => store.user);
    const LocalLoginIdentity = Number(userInfo?.userInfo?.identity);
    const [detailForm] = Form.useForm();
    const [detailsForm] = Form.useForm();
    const [channelInformation] = Form.useForm();
    const [basicForm] = Form.useForm();
    const [returnForm] = Form.useForm();
    const [chainForm] = Form.useForm();

    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    console.log('state', state);

    const detailquery = useQuery(
        ['detailquery9999'],
        () => {
            return getWareDetail({
                id: state?.id
            });
        },
        {
            async onSuccess(res) {
                console.log('res0990099', res);
                const arrayData = await Promise.all(
                    isArrayArr(res?.data?.productImg)?.map((item: any) => {
                        return decryptedUrl(item);
                    })
                );

                // setPromotionPicData(arrayData)
                // const videos = await decryptedUrl(res?.data?.productVideo);
                detailForm.setFieldsValue({
                    ...res.data,
                    inboundNum: res?.data?.inboundNum ? res?.data?.inboundNum + '袋' : '-',
                    amount: res?.data?.amount ? res?.data?.amount + '袋' : '-',
                    inboundTime: res?.data?.inboundTime
                        ? dayjs(res?.data?.inboundTime).format('YYYY-MM-DD HH:mm:ss')
                        : '-'
                });
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    const fooddetail = detailquery?.data?.data?.food;
    // 系统功能
    const feedbackToData = detailquery?.data?.data?.feedbackTo;

    // 商品质量
    const ReturnData = detailquery?.data?.data?.productFeedbackTo;
    // 其他建议
    const OtherDetailData = detailquery?.data?.data?.otherFeedbackTo;

    const chainData = detailquery?.data?.data;

    const feedbackToConfig = [
        {
            label: '入库单号',
            name: 'inboundNo',
            value: 'inboundNo',
            type: 'ShowText',
            span: 8
        },
        {
            label: '仓库名称',
            name: 'warehouseName',
            value: 'warehouseName',
            type: 'ShowText',
            maxWidth: 400,
            span: 8
        },
        {
            label: '仓库地址',
            name: 'warehouseAddress',
            value: 'warehouseAddress',
            type: 'ShowText',
            maxWidth: 400,
            span: 8
        },
        {
            label: '入库数量',
            name: 'inboundNum',
            value: 'inboundNum',
            type: 'ShowText',
            maxWidth: 400,
            span: 8
        },
        {
            label: '操作人',
            name: 'optName',
            value: 'optName',
            type: 'ShowText',
            maxWidth: 400,
            span: 8
        },
        {
            label: '入库时间',
            name: 'inboundTime',
            value: 'inboundTime',
            type: 'ShowText',
            span: 8
        }
    ];
    // 根据角色生成产品信息配置
    const getWarebackToConfig = () => {
        const baseConfig = [
            {
                label: '产品名称',
                name: 'productName',
                value: 'productName',
                type: 'ShowText',
                span: 8
            },
            {
                label: '生产批次',
                name: 'productionBatch',
                value: 'productionBatch',
                type: 'ShowText',
                maxWidth: 400,
                span: 8
            }
        ];

        // 根据角色添加不同的字段
        if ([RoleEnum.平台方, RoleEnum.运营方].includes(LocalLoginIdentity)) {
            // 平台方和运营方：显示生产加工企业和经销商
            baseConfig.push(
                {
                    label: '生产加工企业',
                    name: 'orgName',
                    value: 'orgName',
                    type: 'ShowText',
                    maxWidth: 400,
                    span: 8
                },
                {
                    label: '加工数量',
                    name: 'amount',
                    value: 'amount',
                    type: 'ShowText',
                    maxWidth: 400,
                    span: 8
                },
                {
                    label: '经销商',
                    name: 'dealerName',
                    value: 'dealerName',
                    type: 'ShowText',
                    maxWidth: 400,
                    span: 8
                }
            );
        } else if (LocalLoginIdentity === RoleEnum.生产加工企业) {
            // 生产加工企业：只显示经销商
            baseConfig.push(
                {
                    label: '加工数量',
                    name: 'amount',
                    value: 'amount',
                    type: 'ShowText',
                    maxWidth: 400,
                    span: 8
                },
                {
                    label: '经销商',
                    name: 'dealerName',
                    value: 'dealerName',
                    type: 'ShowText',
                    maxWidth: 400,
                    span: 8
                }
            );
        } else if (LocalLoginIdentity === RoleEnum.销售企业) {
            // 销售企业：只显示生产加工企业
            baseConfig.push({
                label: '生产加工企业',
                name: 'orgName',
                value: 'orgName',
                type: 'ShowText',
                maxWidth: 400,
                span: 8
            });
        }

        return baseConfig;
    };

    const warebackToConfig = getWarebackToConfig();
    const chainConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Link',
            onClick: () => {
                setChainDetailModalVisible(true);
            }
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];
    //系统反馈
    channelInformation.setFieldsValue({
        type: feedbackToData?.type,
        issues: feedbackToData?.issues
    });
    chainForm.setFieldsValue({
        transactionId: detailquery?.data?.data?.transactionId,
        transactionTime: detailquery?.data?.data?.transactionTime
            ? dayjs(detailquery?.data?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
            : '-'
    });
    const onFinish = (values: any) => {
        console.log('Success:', values);
    };

    const onFinishFailed = (errorInfo: any) => {
        console.log('Failed:', errorInfo);
    };
    console.log('detailquery?.data?.data?.material', detailquery?.data?.data?.material);
    return (
        <div>
            <BaseCard title={<PageTitle title='仓储信息详情' bg='container war' />}>
                <Form
                    name='basic'
                    form={detailForm}
                    onFinish={onFinish}
                    onFinishFailed={onFinishFailed}
                    autoComplete='off'
                >
                    <PageTitle title='入库单信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={feedbackToConfig} labelCol={false} />

                    <PageTitle title='产品信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={warebackToConfig} labelCol={false} />
                </Form>

                <Form form={chainForm}>
                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={chainConfig} labelCol={false} />
                </Form>
            </BaseCard>
            <ChainDetailModal
                transactionId={chainData?.transactionId}
                open={ChainDetailModalVisible}
                onCancel={() => setChainDetailModalVisible(false)}
            />
        </div>
    );
};

export default FoodDetail;

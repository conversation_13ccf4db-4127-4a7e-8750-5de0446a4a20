import { Badge, Tooltip } from 'antd';

export function renderStatusBadge(state: number) {
    switch (state) {
        case 0:
            return <Badge color={'#FFBF00'} text='未激活' />;

        case 1:
            return <Badge color={'#1890FF'} text='已激活' />;

        case 2:
            return <Badge color={'#BFBFBF'} text='已作废' />;

        default:
            return <Badge status='error' text='状态错误' />;
    }
}

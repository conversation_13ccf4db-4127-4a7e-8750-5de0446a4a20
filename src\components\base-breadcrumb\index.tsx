/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-06-28 11:25:22
 * @LastEditTime: 2022-11-01 18:31:27
 * @LastEditors: PhilRandWu
 */
import './index.less';
import { useLocation, Link } from 'react-router-dom';
import { basiceRouter } from '@router/routers';

const Memo = () => {
    const location = useLocation();
    // 进行路由查询 /admin ---> routes
    const findPathInRoutes = (path: string, routes: any) => {
        console.log('findPath', path, routes);
        return {
            info: routes?.[0]?.children?.find((route: any) => {
                console.log('find', route, route.path, path);
                if (route.path === path) {
                    return route;
                }
            }),
            index: routes?.[0].children?.findIndex((route: any) => route.path === path)
        };
    };
    const verifyPathname = () => {
        const pathname = location.pathname;
        const paths = pathname.split('/').slice(1);
        const usedPaths = paths;

        console.log('paths', paths);

        const ret: {
            path: string;
            name: string;
            canRoute: boolean;
        }[] = [];
        // 返回找到路由里第一项为 admin 的路由
        let routes: any = basiceRouter.filter((e: any) => e.path === `/${paths[0]}`);
        // 没有找到相应路由
        console.log('filter path', routes, usedPaths);
        if (routes.length === 0) {
            return undefined;
        }
        // 循环截取的地址栏 admin user list
        if (usedPaths.length === 1) {
            const curRouter = routes?.[0];
            ret.push({
                path: curRouter?.path,
                name: curRouter?.label || '',
                canRoute: curRouter?.path !== '/admin' && curRouter?.path !== '/storage'
            });
            if (curRouter?.children?.[0]?.index) {
                const indexRouter = curRouter?.children?.[0];
                ret.push({
                    path: indexRouter?.path,
                    name: indexRouter?.label || '',
                    canRoute: indexRouter?.path !== '/admin' && indexRouter?.path !== '/storage'
                });
            }
            return ret;
        }
        for (let i = 0; i < usedPaths.length; i++) {
            let pathItem = '';
            console.log('usedPath1', pathItem, i);
            for (let j = 0; j < i + 1; j++) {
                pathItem = pathItem + '/' + usedPaths[j];
                console.log('usedPath2', usedPaths[j], pathItem, i);
            }
            const routeInfo = findPathInRoutes(
                usedPaths[i],
                // pathItem
                // .slice(
                // usedPaths[0].length + 2
                // ,)
                routes
            );
            console.log('routeInfo', routeInfo);
            // routes = routes[routeInfo.index]?.children ? routes[routeInfo.index]?.children : routes;

            if (routeInfo.info) {
                ret.push({
                    path: routes?.[0]?.path,
                    name: routes?.[0]?.label || '',
                    canRoute: routes?.[0]?.path !== '/admin' && routes?.[0]?.path !== '/storage'
                });
                if (routeInfo.info?.upRouter) {
                    ret.push({
                        path: routeInfo.info?.upRouter?.path,
                        name: routeInfo.info?.upRouter?.label || '',
                        canRoute: routeInfo.info.path !== '/admin' && routeInfo.info.path !== '/storage'
                    });
                }
                ret.push({
                    path: routeInfo.info?.path,
                    name: routeInfo.info?.label || '',
                    canRoute: routeInfo.info.path !== '/admin' && routeInfo.info.path !== '/storage'
                });
            }
        }
        console.log('ret', ret);
        return ret;
    };

    const verified = verifyPathname() || [];
    console.log('verified', verified);

    return (
        <div className='BreadcrumbContainer'>
            <div className='Breadcrumb'>
                {verified.map((item, index) => {
                    console.log('item.path', item.path);
                    let enterPath = item.path;

                    return (
                        <div key={index} className='BreadcrumbItem'>
                            {item.canRoute ? (
                                <Link
                                    className={index === verified.length - 1 ? 'lastBreadcrumbItem' : ''}
                                    to={enterPath}
                                >
                                    {item.name}
                                </Link>
                            ) : (
                                <span className={index === verified.length - 1 ? 'lastBreadcrumbItem' : ''}>
                                    {item.name}
                                </span>
                            )}
                            <div className='division'>{index < verified.length - 1 && '/'}</div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

const BreadcrumbNavBar = () => {
    return (
        <div className='Bread'>
            <Memo />
        </div>
    );
};

export default BreadcrumbNavBar;

/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 17:15:57
 * @LastEditTime: 2024-10-10 11:13:40
 * @LastEditors: 吴山仁
 */
import BaseCard from '@components/base-card';
import BaseFormItem from '@components/base-form-item';
import PageTitle from '@components/page-title';
import React, { useState, useEffect } from 'react';
import { Form, message, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { useMutation, useQuery } from 'react-query';
import { foodDetail } from '@services/food';
import { enumeration } from './config';
import SwitchList from '@components/switch-list';
import styles from './index.module.less';
import { useLocation } from 'react-router-dom';
import { ReformChainError } from '@utils/errorCodeReform';
import dayjs from 'dayjs';
import { Image } from 'antd';
import FilterForm from '@components/filter-form';
import * as nzh from 'nzh/cn';
import { FormItemImages, FormItemVideo } from '@components';
import ChainDetailModal from '@components/chain_detail_modal';
import { decryptedUrl, isArrayArr } from '@utils';
import { purchaseDetail, getChainHisListDetail, purchaseInfo } from '@services/purchase';
const FoodDetail = (props: any) => {
    const { state } = useLocation();
    const [detailForm] = Form.useForm();
    const [transId, setTransId] = useState('');
    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    // console.log("state",state)

    const detailquery = useQuery(
        ['detailquery9999'],
        () => {
            return purchaseInfo({
                productId: state?.id,
                productionBatch: state?.productionBatch
            });
        },
        {
            async onSuccess(res) {
                console.log('res0990099', res);
                const arrayData = await Promise.all(
                    isArrayArr(res?.data?.productImg)?.map((item: any) => {
                        return decryptedUrl(item);
                    })
                );
                // setPromotionPicData(arrayData)
                const videos = await decryptedUrl(res?.data?.productVideo);
                detailForm.setFieldsValue({
                    ...res.data,
                    purchaseTime: res?.data?.purchaseTime
                        ? dayjs(res?.data?.purchaseTime).format('YYYY-MM-DD HH:mm:ss')
                        : '-',
                    bagCount: res?.data?.bagCount ? res?.data?.bagCount + '袋' : '-',
                    purchaseWeight: res?.data?.purchaseWeight ? res?.data?.purchaseWeight + '吨' : '-',
                    purchaseUnitPrice: res?.data?.purchaseUnitPrice ? res?.data?.purchaseUnitPrice + '元/吨' : '-'
                });
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    const getChainHisListDetailData = useQuery(
        ['getChainHisListDetail'],
        () => {
            return getChainHisListDetail({
                productId: state?.id
            });
        }
        // {
        //     async onSuccess(res) {
        //         console.log('res0990099', res);
        //         const arrayData = await Promise.all(
        //             isArrayArr(res?.data?.productImg)?.map((item: any) => {
        //                 return decryptedUrl(item);
        //             })
        //         );
        //         // setPromotionPicData(arrayData)
        //         const videos = await decryptedUrl(res?.data?.productVideo);
        //         detailForm.setFieldsValue({
        //             ...res.data,
        //             productImg: arrayData && arrayData.length > 0 ? arrayData : null,
        //             productVideo: videos ? videos : null,
        //             transactionTime: res?.data?.transactionTime
        //                 ? dayjs(res?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
        //                 : '-'
        //         });
        //     },
        //     onError(err: any) {
        //         ReformChainError(err);
        //     }
        // }
    );
    // console.log("detailquery",detailquery)
    const fooddetail = detailquery?.data?.data?.food;

    const productionConfig = [
        {
            label: '农作物类型',
            name: 'plantName',
            value: 'plantName',
            type: 'ShowScrollText',
            span: 8
        },
        {
            label: '收购对接人',
            name: 'optName',
            value: 'optName',
            type: 'ShowScrollText',
            span: 8
        },
        {
            label: '收购次数',
            name: 'materialPurchaseCount',
            value: 'materialPurchaseCount',
            type: 'ShowScrollText',
            span: 8
        },
        {
            label: '收购批次',
            name: 'purchaseBatch',
            value: 'purchaseBatch',
            type: 'ShowScrollText',
            span: 8
        },
        {
            label: '收购时间',
            name: 'purchaseTime',
            value: 'purchaseTime',
            type: 'ShowScrollText',
            span: 8
        },
        {
            label: '收购重量',
            name: 'purchaseWeight',
            value: 'purchaseWeight',
            type: 'ShowScrollText',
            span: 8
        },
        {
            label: '装袋数量',
            name: 'bagCount',
            value: 'bagCount',
            type: 'ShowScrollText',
            span: 8
        },
        {
            label: '收购单价',
            name: 'purchaseUnitPrice',
            value: 'purchaseUnitPrice',
            type: 'ShowScrollText',
            span: 8
        },
        {
            label: '种植批次',
            name: 'plantBatch',
            value: 'plantBatch',
            type: 'ShowScrollText',
            span: 8
        }
        // {
        //     label: '宣传图片',
        //     name: 'productImg',
        //     value: 'productImg',
        //     type: 'Custom',
        //     children: (
        //         <Form.Item key='productImg' name='productImg'>
        //             <FormItemImages height={100}></FormItemImages>
        //         </Form.Item>
        //     )
        // }
        // {
        //     label: '宣传视频',
        //     name: 'productVideo',
        //     value: 'productVideo',
        //     type: 'Custom',
        //     children: (
        //         <Form.Item key='productVideo' name='productVideo'>
        //             <FormItemVideo width={200} controls></FormItemVideo>
        //         </Form.Item>
        //     )
        // }
    ];
    const foodInfoConfig = [
        {
            label: '产品名称',
            name: 'productName',
            value: 'productName',
            type: 'ShowText',
            span: 8
        },
        {
            label: '生产批次',
            name: 'productionBatch',
            value: 'productionBatch',
            type: 'ShowText',
            span: 8
        },
        {
            label: '数量',
            name: 'amount',
            value: 'amount',
            type: 'ShowText',
            span: 8
        },
        // {
        //     label: '产品编码',
        //     name: 'code',
        //     value: 'productCode',
        //     type: 'ShowText'
        // },
        {
            label: '生产线',
            name: 'line',
            value: 'line',
            type: 'ShowText',
            span: 8
        }
    ];
    const ChainDetailModalConfig = {
        transactionId: detailForm.getFieldValue('transactionId'),
        // transactionId: transId,
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };

    const chainConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Link',
            onClick: () => {
                setChainDetailModalVisible(true);
            }
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];

    const onFinish = (values: any) => {
        console.log('Success:', values);
    };

    const onFinishFailed = (errorInfo: any) => {
        console.log('Failed:', errorInfo);
    };
    console.log('detailquery?.data?.data?.material', detailquery?.data?.data?.material);

    useEffect(() => {
        detailForm.setFieldsValue({
            transactionId: getChainHisListDetailData?.data?.data[0]?.transactionId || '-',
            transactionTime: getChainHisListDetailData?.data?.data[0]?.transactionTime
                ? dayjs(getChainHisListDetailData?.data?.data[0]?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
                : '-'
        });
    }, [getChainHisListDetailData]);
    return (
        <div>
            <BaseCard title={<PageTitle title='收购溯源数据详情' bg='container chan' />}>
                <Form
                    name='basic'
                    form={detailForm}
                    onFinish={onFinish}
                    onFinishFailed={onFinishFailed}
                    autoComplete='off'
                >
                    {/* <PageTitle title='食品信息' type='primaryIcon' />
                    <BaseFormItem configs={foodInfoConfig} /> */}
                    <PageTitle title='收购信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={productionConfig} labelCol={false} />

                    <PageTitle title='生产加工信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={foodInfoConfig} labelCol={false} />

                    {/* <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} /> */}
                    {/* <div>
                        {getChainHisListDetailData?.data?.data.length > 0 ? (
                            <div
                                style={{
                                    display: 'flex',
                                    margin: '20px 0',
                                    color: '#9b9b9b',
                                    width: '1000px',
                                    justifyContent: 'space-between'
                                }}
                            >
                                <span style={{ marginRight: '40px' }}>
                                    最新链上哈希&nbsp;
                                    <Tooltip title='信息的链上的哈希值'>
                                        <QuestionCircleOutlined />
                                    </Tooltip>
                                    &nbsp; :&nbsp;&nbsp;&nbsp;{' '}
                                    <a
                                        onClick={() => {
                                            setTransId(getChainHisListDetailData?.data?.data[0]?.transactionId);
                                            setChainDetailModalVisible(true);
                                        }}
                                    >
                                        {getChainHisListDetailData?.data?.data[0]?.transactionId}
                                    </a>{' '}
                                </span>

                                <span>
                                    最新上链时间&nbsp;
                                    <Tooltip title='信息上链的时间'>
                                        <QuestionCircleOutlined />
                                    </Tooltip>
                                    &nbsp;:&nbsp;&nbsp;&nbsp;
                                    {dayjs(getChainHisListDetailData?.data?.data[0]?.transactionTime).format(
                                        'YYYY-MM-DD HH:mm:ss'
                                    )}
                                </span>
                            </div>
                        ) : (
                            ''
                        )}
                    </div> */}
                    {/* <div>
                        {getChainHisListDetailData?.data?.data.map((item: any, index: any) => {
                            return index == 0 ? (
                                <div key={index} style={{ display: 'flex', margin: '20px 0', color: '#9b9b9b' }}>
                                    <span style={{ marginRight: '40px' }}>
                                        最新链上哈希:&nbsp;&nbsp;&nbsp; <a>{item.transactionId}</a>{' '}
                                    </span>

                                    <span>
                                        最新上链时间:&nbsp;&nbsp;&nbsp;
                                        {dayjs(item.transactionTime).format('YYYY-MM-DD HH:mm:ss')}
                                    </span>
                                </div>
                            ) : (
                                <div key={index} style={{ display: 'flex', margin: '20px 0', color: '#9b9b9b' }}>
                                    <span style={{ marginRight: '40px' }}>
                                        第{nzh.encodeS(item.order)}次链上哈希:&nbsp;&nbsp;&nbsp;
                                        <a>{item.transactionId}</a>
                                    </span>

                                    <span>
                                        第{nzh.encodeS(item.order)}次链上时间:&nbsp;&nbsp;&nbsp;
                                        {dayjs(item.transactionTime).format('YYYY-MM-DD HH:mm:ss')}
                                    </span>
                                </div>
                            );
                        })}
                    </div> */}

                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={chainConfig} labelCol={false} />
                </Form>
            </BaseCard>
            <ChainDetailModal {...ChainDetailModalConfig} />
        </div>
    );
};

export default FoodDetail;
